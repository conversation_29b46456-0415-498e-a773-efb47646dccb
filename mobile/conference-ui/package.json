{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev --host --dotenv .env.development", "generate:dev": "nuxt generate --dotenv .env.development", "generate:test": "nuxt generate --dotenv .env.test", "generate": "nuxt generate --dotenv .env.production", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"@nuxt/icon": "1.15.0", "@nuxt/image": "1.11.0", "@nuxt/scripts": "0.11.13", "@pinia/nuxt": "^0.11.2", "@unhead/vue": "^2.0.14", "nuxt": "^3.18.1", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.5.0", "vue": "^3.5.20", "vue-router": "^4.5.1"}, "devDependencies": {"@antfu/eslint-config": "^5.2.1", "@nuxt/eslint": "1.9.0", "@unocss/eslint-plugin": "^66.4.2", "@unocss/nuxt": "^66.4.2", "@vant/nuxt": "^1.0.7", "eslint": "^9.34.0", "eslint-plugin-format": "^1.0.1", "nuxt-lodash": "^2.5.3", "typescript": "^5.9.2", "typescript-eslint": "^8.41.0", "unocss": "^66.4.2", "vant": "^4.9.21", "vue-tsc": "^3.0.6"}}