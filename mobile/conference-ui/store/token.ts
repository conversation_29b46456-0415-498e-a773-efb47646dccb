const baseUrl = useRuntimeConfig().public.baseUrl
export const tokenStore = defineStore(TOKEN_KEY, () => {
  const token = ref<string | undefined>('')
  const getToken = () => {
    return token.value
  }
  const setToken = (newToken: string) => {
    token.value = newToken
  }
  const clearToken = () => {
    token.value = undefined
  }
  return {
    getToken,
    setToken,
    clearToken,
  }
}, {
  persist: {
    storage: piniaPluginPersistedstate.cookies({
      path: baseUrl,
    }),
  },
})
