import type { UseFetchOptions } from '#app'

type CustomFetchOptions<DataT> = UseFetchOptions<DataT> & {
  isAuth?: boolean
}

export async function useCustomFetch<DataT>(url: string, options: CustomFetchOptions<DataT> = {}) {
  const res = await useFetch(url, {
    timeout: 60000,
    baseURL: options.baseURL ?? useRuntimeConfig().public.backendApi,
    ...options,
    headers: {
      ...options.headers,
      authorization: options.isAuth === false ? '' : `${useCookie(TOKEN_KEY).value}`,
    } as any,

  })
  if (res.status.value !== 'success') {
    // ElMessage.error('后端接口异常')
    return Promise.reject(res)
  }
  const data = res.data.value as any
  if (data.code && data.code !== 200) {
    if (data.code === 401) {
      // ElMessage.error({
      //     message: '登录已过期，即将跳转登录页...',
      //     duration: 2000
      // })
      // 清除项目信息
      setTimeout(() => {
        useRouter().push({ name: 'COMPANY_LOGIN' })
      }, 2000)
      return Promise.reject(data)
    }
    else {
      // ElMessage.error({
      //     message: data.msg??'后端接口异常',
      //     duration: 2000
      // })
      return Promise.reject(data)
    }
  }
  return data as DataT
}
