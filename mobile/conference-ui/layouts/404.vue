<script setup lang="ts">
const router = useRouter()

function onBack() {
  if (window.history.state.back)
    history.back()
  else
    router.replace('/')
}
</script>

<template>
  <main class="gray-300 dark:gray-200 py-20 text-center">
    <van-icon name="warn-o" size="large" />
    <slot />
    <div class="mt-10">
      <van-button type="primary" @click="onBack">
        返回
      </van-button>
    </div>
  </main>
</template>
