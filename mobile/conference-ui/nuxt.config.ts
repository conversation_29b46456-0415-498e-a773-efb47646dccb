// https://nuxt.com/docs/api/configuration/nuxt-config
import process from 'node:process'

export default defineNuxtConfig({
  compatibilityDate: '2025-07-15',
  devtools: { enabled: true },
  ssr: false,
  modules: [
    '@nuxt/eslint',
    '@nuxt/icon',
    '@nuxt/image',
    '@nuxt/scripts',
    '@pinia/nuxt',
    '@vant/nuxt',
    '@unocss/nuxt',
    'pinia-plugin-persistedstate/nuxt',
    'nuxt-lodash',
  ],
  app: {
    baseURL: process.env.NUXT_PUBLIC_BASE_URL,
    head: {
      viewport: 'width=device-width,initial-scale=1,viewport-fit=cover',
      link: [
        { rel: 'icon', href: `${process.env.NUXT_PUBLIC_BASE_URL}/favicon.ico`, sizes: 'any' },
      ],
      meta: [
        { name: 'viewport', content: 'width=device-width, initial-scale=1, viewport-fit=cover' },
        { name: 'description', content: '上海市核电办会务' },
        { name: 'apple-mobile-web-app-status-bar-style', content: 'black-translucent' },
        // { name: 'theme-color', media: '(prefers-color-scheme: light)', content: '#ffffff' },
        // { name: 'theme-color', media: '(prefers-color-scheme: dark)', content: '#222222' },
      ],
      script: [
        // { innerHTML: preload(), type: 'text/javascript', tagPosition: 'head' },
      ],
    },
  },
  nitro: {
    compressPublicAssets: true,
    devProxy: {
      [process.env.NUXT_PUBLIC_BACKEND_API as string]: process.env.NUXT_PUBLIC_HOST_URL as string + process.env.NUXT_PUBLIC_BACKEND_API,
    },
  },
  runtimeConfig: {
    public: {
      baseUrl: '',
      backendApi: '',
      hostUrl: '',
      wxAppid: '',
      wxAuthUrl: '',
    },
  },
  vant: {
    defaultLocale: 'zh-CN',
  },
  eslint: {
    config: {
      standalone: false,
    },
  },
  typescript: {
    shim: false,
    typeCheck: true,
  },
  features: {
    // For UnoCSS
    inlineStyles: false,
  },
})
