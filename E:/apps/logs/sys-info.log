09:41:59.582 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.2.Final
09:41:59.653 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,53] - Starting RuoYiApplication using Java 21.0.2 with PID 74190 (/Users/<USER>/workspace/project2025/核电办门户/offical-site/admin/site-admin/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/核电办门户/offical-site)
09:41:59.654 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,658] - The following 1 profile is active: "local"
09:42:03.297 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
09:42:04.837 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8083"]
09:42:04.840 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:42:04.840 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.39]
09:42:04.901 [restartedMain] INFO  o.a.c.c.C.[.[.[/hdbmhht] - [log,173] - Initializing Spring embedded WebApplicationContext
09:42:06.081 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
09:42:06.938 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
09:42:09.688 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1010] - {dataSource-1} inited
09:42:12.888 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
09:42:12.903 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
09:42:12.903 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
09:42:12.904 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
09:42:12.905 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

09:42:12.905 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
09:42:12.905 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
09:42:12.905 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@200e788b
09:42:20.489 [restartedMain] INFO  o.a.s.r.ResourceFinder - [discoverResources,54] - The following process definition files will be deployed: [diagram.bpmn]
09:42:23.282 [restartedMain] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
09:42:23.536 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1276] - Found 1 Process Engine Configurators in total:
09:42:23.536 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1281] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
09:42:23.536 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1293] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
09:42:24.044 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1304] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
09:42:24.113 [restartedMain] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
09:42:24.114 [restartedMain] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [start,173] - Starting up the default async job executor [org.activiti.spring.SpringAsyncExecutor].
09:42:24.115 [Thread-8] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,49] - {} starting to acquire async jobs due
09:42:24.115 [Thread-9] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,53] - {} starting to acquire async jobs due
09:42:24.116 [Thread-10] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,55] - {} starting to reset expired jobs
09:42:24.324 [restartedMain] INFO  o.a.e.i.c.DeployCmd - [executeDeploy,103] - Launching new deployment with version: 1
09:42:24.670 [restartedMain] INFO  o.a.e.i.b.d.BpmnDeployer - [dispatchProcessDefinitionEntityInitializedEvent,240] - Process deployed: {id: 927cf4d6-89f9-11f0-bef7-2a8d686d9ea7, key: news_release_audit_process, name: news_release_audit_process }
09:42:34.338 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8083"]
09:42:34.359 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
09:42:34.498 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
09:42:34.498 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
09:42:34.594 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
09:42:34.594 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
09:43:10.817 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
09:43:10.818 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
09:43:10.912 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
09:43:10.912 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
09:43:10.943 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,59] - Started RuoYiApplication in 71.962 seconds (process running for 74.049)
09:43:11.742 [RMI TCP Connection(2)-127.0.0.1] INFO  o.a.c.c.C.[.[.[/hdbmhht] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:43:26.588 [tomcat-handler-0] INFO  o.a.t.u.h.p.Cookie - [log,173] - A cookie header was received [Hm_lvt_e5446a47ad623682dc4db55879d44a46=1756367713,1756694163;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
09:43:38.242 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[admin][Success][登录成功]
17:18:46.827 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.2.Final
17:18:46.917 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,53] - Starting RuoYiApplication using Java 21.0.2 with PID 99525 (/Users/<USER>/workspace/project2025/核电办门户/offical-site/admin/site-admin/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/核电办门户/offical-site)
17:18:46.919 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,658] - The following 1 profile is active: "local"
17:18:49.826 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
17:18:50.945 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8083"]
17:18:50.947 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:18:50.947 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.39]
17:18:50.995 [restartedMain] INFO  o.a.c.c.C.[.[.[/hdbmhht] - [log,173] - Initializing Spring embedded WebApplicationContext
17:18:51.911 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
17:18:52.460 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
17:18:54.152 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1010] - {dataSource-1} inited
17:18:56.343 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
17:18:56.355 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
17:18:56.355 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
17:18:56.356 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
17:18:56.356 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

17:18:56.356 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
17:18:56.356 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
17:18:56.357 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6d8b904
17:19:00.718 [restartedMain] INFO  o.a.s.r.ResourceFinder - [discoverResources,54] - The following process definition files will be deployed: [diagram.bpmn]
17:19:02.367 [restartedMain] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
17:19:02.528 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1276] - Found 1 Process Engine Configurators in total:
17:19:02.528 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1281] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
17:19:02.528 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1293] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
17:19:02.906 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1304] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration (priority:10000)
17:19:03.012 [restartedMain] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
17:19:03.013 [restartedMain] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [start,173] - Starting up the default async job executor [org.activiti.spring.SpringAsyncExecutor].
17:19:03.014 [Thread-8] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,49] - {} starting to acquire async jobs due
17:19:03.014 [Thread-9] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,53] - {} starting to acquire async jobs due
17:19:03.015 [Thread-10] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,55] - {} starting to reset expired jobs
17:19:03.177 [restartedMain] INFO  o.a.e.i.c.DeployCmd - [executeDeploy,103] - Launching new deployment with version: 1
17:19:03.489 [restartedMain] INFO  o.a.e.i.b.d.BpmnDeployer - [dispatchProcessDefinitionEntityInitializedEvent,240] - Process deployed: {id: 5d73469f-8a39-11f0-a007-2a8d686d9ea7, key: news_release_audit_process, name: news_release_audit_process }
17:19:08.149 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8083"]
17:19:08.163 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
17:19:08.311 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
17:19:08.311 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
17:19:08.482 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
17:19:08.483 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
17:19:37.516 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
17:19:37.517 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
17:19:37.616 [restartedMain] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
17:19:37.617 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
17:19:37.633 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,59] - Started RuoYiApplication in 51.751 seconds (process running for 54.282)
17:19:38.858 [RMI TCP Connection(4)-127.0.0.1] INFO  o.a.c.c.C.[.[.[/hdbmhht] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:22:49.710 [tomcat-handler-0] INFO  o.a.t.u.h.p.Cookie - [log,173] - A cookie header was received [Hm_lvt_e5446a47ad623682dc4db55879d44a46=1756367713,1756694163;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
17:54:33.238 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
17:54:33.243 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
17:54:33.243 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
17:54:33.243 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
17:54:33.244 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
17:54:33.244 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
17:54:33.244 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
17:54:33.244 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
17:54:33.247 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
