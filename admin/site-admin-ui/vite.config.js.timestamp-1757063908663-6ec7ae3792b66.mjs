// vite.config.js
import { defineConfig, loadEnv } from "file:///Users/<USER>/workspace/project2025/%E6%A0%B8%E7%94%B5%E5%8A%9E%E9%97%A8%E6%88%B7/offical-site/admin/site-admin-ui/node_modules/.pnpm/vite@5.0.4_@types+node@24.3.0_sass@1.69.5/node_modules/vite/dist/node/index.js";
import path2 from "path";

// vite/plugins/index.js
import vue from "file:///Users/<USER>/workspace/project2025/%E6%A0%B8%E7%94%B5%E5%8A%9E%E9%97%A8%E6%88%B7/offical-site/admin/site-admin-ui/node_modules/.pnpm/@vitejs+plugin-vue@4.5.0_vite@5.0.4_@types+node@24.3.0_sass@1.69.5__vue@3.3.9/node_modules/@vitejs/plugin-vue/dist/index.mjs";

// vite/plugins/auto-import.js
import autoImport from "file:///Users/<USER>/workspace/project2025/%E6%A0%B8%E7%94%B5%E5%8A%9E%E9%97%A8%E6%88%B7/offical-site/admin/site-admin-ui/node_modules/.pnpm/unplugin-auto-import@0.17.1_@vueuse+core@10.6.1_vue@3.3.9__rollup@4.48.1/node_modules/unplugin-auto-import/dist/vite.js";
function createAutoImport() {
  return autoImport({
    imports: [
      "vue",
      "vue-router",
      "pinia"
    ],
    dts: false
  });
}

// vite/plugins/svg-icon.js
import { createSvgIconsPlugin } from "file:///Users/<USER>/workspace/project2025/%E6%A0%B8%E7%94%B5%E5%8A%9E%E9%97%A8%E6%88%B7/offical-site/admin/site-admin-ui/node_modules/.pnpm/vite-plugin-svg-icons@2.0.1_vite@5.0.4_@types+node@24.3.0_sass@1.69.5_/node_modules/vite-plugin-svg-icons/dist/index.mjs";
import path from "path";
function createSvgIcon(isBuild) {
  return createSvgIconsPlugin({
    iconDirs: [path.resolve(process.cwd(), "src/assets/icons/svg")],
    symbolId: "icon-[dir]-[name]",
    svgoOptions: isBuild
  });
}

// vite/plugins/compression.js
import compression from "file:///Users/<USER>/workspace/project2025/%E6%A0%B8%E7%94%B5%E5%8A%9E%E9%97%A8%E6%88%B7/offical-site/admin/site-admin-ui/node_modules/.pnpm/vite-plugin-compression@0.5.1_vite@5.0.4_@types+node@24.3.0_sass@1.69.5_/node_modules/vite-plugin-compression/dist/index.mjs";
function createCompression(env) {
  const { VITE_BUILD_COMPRESS } = env;
  const plugin = [];
  if (VITE_BUILD_COMPRESS) {
    const compressList = VITE_BUILD_COMPRESS.split(",");
    if (compressList.includes("gzip")) {
      plugin.push(
        compression({
          ext: ".gz",
          deleteOriginFile: false
        })
      );
    }
    if (compressList.includes("brotli")) {
      plugin.push(
        compression({
          ext: ".br",
          algorithm: "brotliCompress",
          deleteOriginFile: false
        })
      );
    }
  }
  return plugin;
}

// vite/plugins/setup-extend.js
import setupExtend from "file:///Users/<USER>/workspace/project2025/%E6%A0%B8%E7%94%B5%E5%8A%9E%E9%97%A8%E6%88%B7/offical-site/admin/site-admin-ui/node_modules/.pnpm/unplugin-vue-setup-extend-plus@1.0.0/node_modules/unplugin-vue-setup-extend-plus/dist/vite.js";
function createSetupExtend() {
  return setupExtend({});
}

// vite/plugins/index.js
import UnoCSS from "file:///Users/<USER>/workspace/project2025/%E6%A0%B8%E7%94%B5%E5%8A%9E%E9%97%A8%E6%88%B7/offical-site/admin/site-admin-ui/node_modules/.pnpm/unocss@0.61.9_postcss@8.5.6_rollup@4.48.1_vite@5.0.4_@types+node@24.3.0_sass@1.69.5_/node_modules/unocss/dist/vite.mjs";
function createVitePlugins(viteEnv, isBuild = false) {
  const vitePlugins = [vue()];
  vitePlugins.push(createAutoImport());
  vitePlugins.push(createSetupExtend());
  vitePlugins.push(createSvgIcon(isBuild));
  vitePlugins.push(UnoCSS());
  isBuild && vitePlugins.push(...createCompression(viteEnv));
  return vitePlugins;
}

// vite.config.js
var __vite_injected_original_dirname = "/Users/<USER>/workspace/project2025/\u6838\u7535\u529E\u95E8\u6237/offical-site/admin/site-admin-ui";
var vite_config_default = defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd());
  const { VITE_APP_ENV } = env;
  return {
    // 部署生产环境和开发环境下的URL。
    // 默认情况下，vite 会假设你的应用是被部署在一个域名的根路径上
    // 例如 https://www.ruoyi.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.ruoyi.vip/admin/，则设置 baseUrl 为 /admin/。
    base: VITE_APP_ENV === "production" ? "/hdbmhht-admin" : "/",
    build: {
      outDir: "hdbmhht-admin"
    },
    plugins: createVitePlugins(env, command === "build"),
    resolve: {
      // https://cn.vitejs.dev/config/#resolve-alias
      alias: {
        // 设置路径
        "~": path2.resolve(__vite_injected_original_dirname, "./"),
        // 设置别名
        "@": path2.resolve(__vite_injected_original_dirname, "./src")
      },
      // https://cn.vitejs.dev/config/#resolve-extensions
      extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".vue"]
    },
    // vite 相关配置
    server: {
      port: 80,
      host: true,
      open: true,
      proxy: {
        // https://cn.vitejs.dev/config/#server-proxy
        "/hdbmhht": {
          // target: 'http://*************:8083/hdbmhht',
          target: "http://localhost:8083/hdbmhht",
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/hdbmhht/, "")
        },
        // 自定义地图服务代理
        "/_AMapService/v4/map/styles": {
          target: "https://webapi.amap.com",
          changeOrigin: true,
          // rewrite: (path) => path.replace(/^\/_AMapService/, ''),
          configure: (proxy, options) => {
            proxy.on("proxyReq", (proxyReq, req, res) => {
              const url = new URL(req.url, `http://${req.headers.host}`);
              const searchParams = url.searchParams;
              searchParams.set("jscode", "146a4a89cd7f5ca7269c23cf61d5081d");
              const newSearch = searchParams.toString();
              const newPath = url.pathname + (newSearch ? `?${newSearch}` : "");
              proxyReq.path = newPath.replace(/^\/_AMapService/, "");
            });
          }
        },
        // Web服务API代理
        "/_AMapService": {
          target: "https://restapi.amap.com",
          changeOrigin: true,
          // rewrite: (path) => path.replace(/^\/_AMapService/, ''),
          configure: (proxy, options) => {
            proxy.on("proxyReq", (proxyReq, req, res) => {
              const url = new URL(req.url, `http://${req.headers.host}`);
              const searchParams = url.searchParams;
              searchParams.set("jscode", "146a4a89cd7f5ca7269c23cf61d5081d");
              const newSearch = searchParams.toString();
              const newPath = url.pathname + (newSearch ? `?${newSearch}` : "");
              proxyReq.path = newPath.replace(/^\/_AMapService/, "");
            });
          }
        }
      }
    },
    //fix:error:stdin>:7356:1: warning: "@charset" must be the first rule in the file
    css: {
      postcss: {
        plugins: [
          {
            postcssPlugin: "internal:charset-removal",
            AtRule: {
              charset: (atRule) => {
                if (atRule.name === "charset") {
                  atRule.remove();
                }
              }
            }
          }
        ]
      }
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
