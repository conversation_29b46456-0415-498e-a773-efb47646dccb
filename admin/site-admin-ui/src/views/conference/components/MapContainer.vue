<script setup lang="ts">
import AMapLoader from '@amap/amap-jsapi-loader';
import {ref, onMounted, onUnmounted, shallowRef, watch} from 'vue';
import {Location} from "@element-plus/icons-vue";
// amap 对象
const AMap:any = shallowRef(null)
// 地图对象
const mapInstance:any = shallowRef(null)
// 地图autoComplete
const autoInstance:any = shallowRef(null)
// 地图搜索
const placeSearch:any = shallowRef(null)

const resultPanel = ref(null)


onMounted(()=>{
  initAMap()
})

onUnmounted(() => {
  mapInstance.value?.destroy();
});
async function initAMap() {

  window._AMapSecurityConfig = {
    serviceHost: window.location.origin + "/_AMapService",
    // securityJsCode: "xxx",
  };
  AMap.value = await AMapLoader.load({
    key: "26f9b73b39a5efd5bb0ecb7e58809bdc", // 申请好的Web端开发者Key，首次调用 load 时必填
    version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
    plugins: ["AMap.Scale","AMap.ToolBar","AMap.AutoComplete","AMap.PlaceSearch"], //需要使用的的插件列表，如比例尺'AMap.Scale'，支持添加多个如：['...','...']
  })
  mapInstance.value = new AMap.value.Map("container", {
    // 设置地图容器id
    viewMode: "3D", // 是否为3D地图模式
    zoom: 11, // 初始化地图级别
    resizeEnable: true,
  });
  let scale = new AMap.value.Scale()
  mapInstance.value.addControl(scale);
  autoInstance.value = new AMap.value.AutoComplete();
  placeSearch.value = new AMap.value.PlaceSearch({
    map: mapInstance.value,
  });
}

const searchLocation = ref()
const querySearchAsync = (queryString: string, cb: (arg: any) => void) => {
  if (!queryString) {
    cb([]);
    return;
  }
  autoInstance.value.search(queryString, function(status: string, result: any) {
    if (status === 'complete' && result.info === 'OK') {
      cb(result.tips)
    } else {
      cb([])
    }
  });
}

const placeInfos = ref<any>([])
const placeSearchAsync = (item: any) => {
  placeSearch.value.setCity(item.adcode);
  placeSearch.value.search(item.name, (status:any, result:any) => {
    if (status === 'complete' && result.info === 'OK') {
      placeInfos.value = result.poiList.pois
    } else {
      placeInfos.value = []
    }
  });
}
</script>

<template>
  <div id="container">
    <div class="z-10 absolute top-2 left-2 flex">
      <el-autocomplete v-model="searchLocation" :fetch-suggestions="querySearchAsync" value-key="name" @select="placeSearchAsync">
        <template #default="{ item }">
          <div class="flex gap-2 items-center">
            <el-icon><Location /></el-icon>
            <span>{{ item.name }}</span>
            <span class="text-gray-400">{{ item.district }}</span>
          </div>
        </template>
      </el-autocomplete>
      <div ref="resultPanel" class="bg-white p-2">
        <div v-for="item in placeInfos" :key="item.id">
          {{ item.name }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
#container{
  padding:0;
  margin: 0;
  width: 100%;
  height: 800px;
}
</style>