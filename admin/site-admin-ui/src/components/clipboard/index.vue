<template>
    <div @click="copy">
        <slot></slot>
    </div>
</template>

<script lang="ts" setup>
import { elError, elSuccess } from '@/utils/elmessage';


const props = defineProps<{
    content: any
}>()

const copy = () => {
    try {
        var input = document.createElement("input");
        input.value = props.content.toLocaleString();
        document.body.appendChild(input);
        input.select();
        document.execCommand("copy");
        input.remove();
        elSuccess('复制成功')
    } catch (error) {
        elError('链接复制异常')
    }
    
}
</script>