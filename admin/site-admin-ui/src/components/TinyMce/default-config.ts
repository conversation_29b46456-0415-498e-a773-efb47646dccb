import { uploadFile, viewFile } from "@/api/modules/common";

const baseUrl = import.meta.env.VITE_APP_BASE + "/static/tinymce";
// default-config.ts
export default {
  base_url: baseUrl,
  suffix: ".min",
  language: "zh-Hans",
  height: 500,
  images_upload_url: "",
  convert_urls: false, //关闭编辑器将链接转相对路径
  content_css: baseUrl+"/skins/content/document/content.min.css",
  // font_family_formats: "思源黑体=SimHei;Arial=arial;Times New Roman=times new roman,times;Impact=impact,chicago;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;",
  font_family_formats:"思源黑体='思源黑体';微软雅黑='微软雅黑';宋体='宋体';黑体='黑体';仿宋='仿宋';楷体='楷体';隶书='隶书';幼圆='幼圆';Andale Mono=andale mono,times;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier;Georgia=georgia,palatino;Helvetica=helvetica;Impact=impact,chicago;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco;Times New Roman=times new roman,times;Trebuchet MS=trebuchet ms,geneva;Verdana=verdana,geneva;Webdings=webdings;Wingdings=wingdings",
  font_size_formats: "8pt 10pt 12pt 14pt 16pt 18pt 20pt 22pt 24pt 36pt 42pt",
  lineheight_formats:
    "8pt 9pt 10pt 11pt 12pt 14pt 16pt 18pt 20pt 22pt 24pt 26pt 36pt",
  menubar: "file edit insert view format table",
  menu: {
    file: { title: "File", items: "newdocument" },
    edit: {
      title: "Edit",
      items: "undo redo | cut copy powerpaste pastetext | selectall",
    },
    insert: { title: "Insert", items: "link media | template hr" },
    view: { title: "View", items: "visualaid" },
    format: {
      title: "Format",
      items:
        "bold italic underline strikethrough superscript subscript | formats | removeformat",
    },
    table: {
      title: "Table",
      items: "inserttable tableprops deletetable | cell row column",
    },
  },
  plugins:
    "advlist anchor autolink autosave code codesample   directionality emoticons fullscreen hr image insertdatetime link lists media nonbreaking noneditable pagebreak  preview print save searchreplace tabfocus table template  textpattern visualblocks visualchars wordcount",
  toolbar:
    "bold italic underline strikethrough alignleft aligncenter alignright alignjustify alignnone styles fontfamily fontsize cut copy paste outdent indent lineheight blockquote undo redo removeformat subscript superscript hr link unlink openlink image charmap pastetext print preview spellchecker searchreplace code fullscreen insertdatetime media nonbreaking save cancel table tabledelete tablecellprops tablemergecells tablesplitcells tableinsertrowbefore tableinsertrowafter tabledeleterow tablerowprops tablecutrow tablecopyrow tablepasterowbefore tablepasterowafter tableinsertcolbefore tableinsertcolafter tabledeletecol rotateleft rotateright flipv fliph editimage imageoptions fullpage emoticons template forecolor backcolor restoredraft insertfile quickimage quicklink",
  // external_plugins: {
  //   'powerpaste': baseUrl + '/plugins/powerpaste-4.0.1-317/plugin.min.js'
  // },
  paste_word_valid_elements: "*[*]",
  paste_webkit_styles: "all",
  paste_merge_formats: true,
  powerpaste_word_import: "merge", // 参数:propmt, merge, clear
  powerpaste_html_import: "merge", // propmt, merge, clear
  powerpaste_allow_local_images: true, //粘贴图片
  images_upload_handler: async (blobInfo: any, success: any) => {
    
    var formData;
    formData = new FormData();
    formData.append("file", blobInfo.blob(), blobInfo.blob().name);
    
    let response = await uploadFile(formData)
    return import.meta.env.VITE_APP_BASE_API +  response.fileName
  },
  file_picker_types: "file image media",
  file_picker_callback: (callback: any, value: any, meta: any) => {
    //文件分类
    var filetype =
      ".pdf, .txt, .zip, .rar, .7z, .doc, .docx, .xls, .xlsx, .ppt, .pptx, .mp3, .mp4,.png,.jpg,.gif";
    //模拟出一个input用于添加本地文件
    var input = document.createElement("input");
    input.setAttribute("type", "file");
    input.setAttribute("accept", filetype);
    input.click();
    input.onchange = function () {
      // @ts-ignore
      var file = this.files[0];
      var formData;
      formData = new FormData();
      formData.append("file", file, file.name);
      uploadFile(formData)
        .then((response) => {
          callback(viewFile(response.fileId), {
            title: file.name,
            text: file.name,
            source2: "alt.ogg",
          });
        })
        .catch((err) => {
          console.log(err);
        });
    };
  },
};
