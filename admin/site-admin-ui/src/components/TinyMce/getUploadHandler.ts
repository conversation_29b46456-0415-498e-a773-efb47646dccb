// getUploadHandler.ts
const getUploadHandler = (uploadUrl: string): any => {
    return (
      blobInfo: any,
      success: any,
      failure: any,
      progress: any
    ) => {
  
      let xhr: any, formData: any;
      xhr = new XMLHttpRequest();
      xhr.withCredentials = false;
      xhr.open("POST", uploadUrl);
  
      xhr.upload.onprogress = function (e: any) {
        progress((e.loaded / e.total) * 100);
      };
  
      xhr.onload = function () {
        var json;
        if (xhr.status == 403) {
          failure("HTTP Error: " + xhr.status, { remove: true });
          return;
        }
        if (xhr.status < 200 || xhr.status >= 300) {
          failure("HTTP Error: " + xhr.status);
          return;
        }
        json = JSON.parse(xhr.responseText);
        const url = json.data.url;
        if (!json || typeof url != "string") {
          failure("Invalid JSON: " + xhr.responseText);
          return;
        }
        success(url);
      };
  
      xhr.onerror = function () {
        failure(
          "Image upload failed due to a XHR Transport error. Code: " +
          xhr.status
        );
      };
  
      formData = new FormData();
      formData.append("file", blobInfo.blob(), blobInfo.filename());
  
      xhr.send(formData);
    }
  }
  
  export default getUploadHandler