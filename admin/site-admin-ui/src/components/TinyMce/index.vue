<template>
  <Editor v-model="val" :init="config" :id="eid"  />
</template>

<script lang="ts">
import { computed, watch, onMounted } from "vue";
import Editor from "@tinymce/tinymce-vue";

declare global {
  interface Window {
    tinymce: any;
  }
}

import configDefault from "./default-config";
// import getUploadHandler from "./getUploadHandler";

export default {
  components: {
    Editor,
  },
  props: {
    value: {
      type: String,
      default: "",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    uploadUrl: {
      type: String,
      default: "",
    },
    init: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  emits: ["update:value"],
  setup(props: any, context: any) {
    const eid = 'editor';

    // 双向绑定
    const val = computed({
      get() {
        return props.value;
      },
      set(newVal: string) {
        context.emit("update:value", newVal);
      },
    });

    // 配置对象
    const config = computed(() => {
      const obj = Object.assign(configDefault, props.init);
      // obj.images_upload_handler = getUploadHandler('szmanage/common/upload');

      return obj;
    });

    return {
      val,
      config,
      eid,
    };
  },
};
</script>