<script setup lang="ts">
import { ref, computed, watch, inject, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Close, ArrowRight, DocumentCopy } from '@element-plus/icons-vue';

defineOptions({
  name: 'TailwindColorClassPicker'
})

const props = withDefaults(defineProps<{
  modelValue: string,
  type?: 'text' | 'background',
  placeholder?: string,
  disabled?: boolean,
}>(), {
  modelValue: '',
  type: 'text',
  placeholder: '点击设置样式...',
  disabled: false
})

const emit = defineEmits(['update:modelValue', 'change', 'blur'])

// 表单项注入，用于验证
const elFormItem = inject('elFormItem', {} as any);

// 状态
const popoverVisible = ref(false);
const backgroundMode = ref<'solid' | 'gradient'>('solid');
const color1Base = ref('blue');
const color1Shade = ref(5);
const color2Base = ref('purple');
const color2Shade = ref(5);
const gradientDirection = ref('to-br');

// 新增：记录组件是否处于初始未设置状态
const isInitialState = ref(true);

// 重置所有状态到默认值
const resetToDefaults = () => {
  backgroundMode.value = 'solid';
  color1Base.value = 'blue';
  color1Shade.value = 5;
  color2Base.value = 'purple';
  color2Shade.value = 5;
  gradientDirection.value = 'to-br';
  isInitialState.value = true;
};

// 添加处理点击的方法
const handleReferenceClick = () => {
  if (props.disabled) return;

  // 如果 modelValue 为空，重置到默认状态
  if (!props.modelValue || props.modelValue.trim() === '') {
    resetToDefaults();
  }

  popoverVisible.value = !popoverVisible.value;
};

// 处理 popover 关闭事件
const handlePopoverClose = () => {
  // 触发 blur 事件用于表单验证
  emit('blur');
  // 调用表单项的验证方法
  elFormItem.validate?.('blur').catch(() => {});
};

// 监听 popover 可见性变化
watch(popoverVisible, (newVisible) => {
  if (!newVisible) {
    handlePopoverClose();
  }
});

// 颜色配置
const colorBases = [
  'slate', 'gray', 'zinc', 'neutral', 'stone',
  'red', 'orange', 'amber', 'yellow', 'lime', 'green', 'emerald', 'teal', 'cyan',
  'sky', 'blue', 'indigo', 'violet', 'purple', 'fuchsia', 'pink', 'rose'
];

const specialColors = ['black', 'white'];
const standardShades = [50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950];

const gradientDirections: { value: string, label: string }[] = [
  { value: 'to-t', label: '向上 (to-t)' },
  { value: 'to-tr', label: '右上 (to-tr)' },
  { value: 'to-r', label: '向右 (to-r)' },
  { value: 'to-br', label: '右下 (to-br)' },
  { value: 'to-b', label: '向下 (to-b)' },
  { value: 'to-bl', label: '左下 (to-bl)' },
  { value: 'to-l', label: '向左 (to-l)' },
  { value: 'to-tl', label: '左上 (to-tl)' }
];

const colorNameMap: Record<string, string> = {
  'slate': '石板', 'gray': '灰色', 'zinc': '锌色', 'neutral': '中性', 'stone': '石色',
  'red': '红色', 'orange': '橙色', 'amber': '琥珀', 'yellow': '黄色', 'lime': '青柠',
  'green': '绿色', 'emerald': '祖母绿', 'teal': '青色', 'cyan': '青蓝',
  'sky': '天蓝', 'blue': '蓝色', 'indigo': '靛蓝', 'violet': '紫罗兰',
  'purple': '紫色', 'fuchsia': '洋红', 'pink': '粉色', 'rose': '玫瑰',
  'black': '黑色', 'white': '白色'
};

const sliderMarks: Record<number, string> = {
  0: '50',
  2: '200',
  5: '500',
  8: '800',
  10: '950'
};

// 工具函数
const isSpecialColor = (color: string) => specialColors.includes(color);
const getColorBaseName = (base: string) => colorNameMap[base] || base;
const getActualShade = (index: number) => standardShades[Math.max(0, Math.min(index, standardShades.length - 1))];

const getColorName = (base: string, shade: number) => {
  if (isSpecialColor(base)) return base;
  return `${base}-${shade}`;
};

const getPreviewClass = (base: string, shade: number, colorType: string) => {
  if (isSpecialColor(base)) return `${colorType}-${base}`;
  return `${colorType}-${base}-${shade}`;
};

// 解析外部传入的 modelValue
const parseModelValue = (value: string) => {
  if (!value || value.trim() === '') {
    resetToDefaults();
    return;
  }

  try {
    isInitialState.value = false;

    if (props.type === 'text' && value.startsWith('text-')) {
      // 解析文字颜色: text-blue-500
      const colorPart = value.replace('text-', '');
      const [base, shade] = colorPart.includes('-') ? colorPart.split('-') : [colorPart, ''];

      if (specialColors.includes(base)) {
        color1Base.value = base;
      } else if (colorBases.includes(base) && shade) {
        color1Base.value = base;
        const shadeIndex = standardShades.indexOf(parseInt(shade));
        if (shadeIndex !== -1) {
          color1Shade.value = shadeIndex;
        }
      }
    } else if (props.type === 'background') {
      if (value.startsWith('bg-gradient-')) {
        // 渐变背景
        backgroundMode.value = 'gradient';

        const parts = value.split(' ');

        // 解析方向
        const gradientPart = parts[0];
        const direction = gradientPart.replace('bg-gradient-', '');
        if (gradientDirections.some(d => d.value === direction)) {
          gradientDirection.value = direction;
        }

        // 解析起始颜色
        const fromPart = parts.find(p => p.startsWith('from-'));
        if (fromPart) {
          const colorPart = fromPart.replace('from-', '');
          const [base, shade] = colorPart.includes('-') ? colorPart.split('-') : [colorPart, ''];

          if (specialColors.includes(base)) {
            color1Base.value = base;
          } else if (colorBases.includes(base) && shade) {
            color1Base.value = base;
            const shadeIndex = standardShades.indexOf(parseInt(shade));
            if (shadeIndex !== -1) {
              color1Shade.value = shadeIndex;
            }
          }
        }

        // 解析结束颜色
        const toPart = parts.find(p => p.startsWith('to-'));
        if (toPart) {
          const colorPart = toPart.replace('to-', '');
          const [base, shade] = colorPart.includes('-') ? colorPart.split('-') : [colorPart, ''];

          if (specialColors.includes(base)) {
            color2Base.value = base;
          } else if (colorBases.includes(base) && shade) {
            color2Base.value = base;
            const shadeIndex = standardShades.indexOf(parseInt(shade));
            if (shadeIndex !== -1) {
              color2Shade.value = shadeIndex;
            }
          }
        }
      } else if (value.startsWith('bg-')) {
        // 纯色背景
        backgroundMode.value = 'solid';

        const colorPart = value.replace('bg-', '');
        const [base, shade] = colorPart.includes('-') ? colorPart.split('-') : [colorPart, ''];

        if (specialColors.includes(base)) {
          color1Base.value = base;
        } else if (colorBases.includes(base) && shade) {
          color1Base.value = base;
          const shadeIndex = standardShades.indexOf(parseInt(shade));
          if (shadeIndex !== -1) {
            color1Shade.value = shadeIndex;
          }
        }
      }
    }
  } catch (error) {
    console.warn('解析 modelValue 失败:', error);
    resetToDefaults();
  }
};

// 计算属性
const typeLabels = computed(() => ({
  text: '文字颜色',
  background: '背景颜色'
}));

const backgroundModeLabels = computed(() => ({
  solid: '纯色背景',
  gradient: '渐变背景'
}));

const generatedClass = computed(() => {
  // 如果处于初始状态且没有外部值，返回空字符串
  if (isInitialState.value && (!props.modelValue || props.modelValue.trim() === '')) {
    return '';
  }

  const color1Name = getColorName(color1Base.value, getActualShade(color1Shade.value));
  const color2Name = getColorName(color2Base.value, getActualShade(color2Shade.value));

  switch (props.type) {
    case 'text':
      return `text-${color1Name}`;
    case 'background':
      if (backgroundMode.value === 'gradient') {
        return `bg-gradient-${gradientDirection.value} from-${color1Name} to-${color2Name}`;
      } else {
        return `bg-${color1Name}`;
      }
    default:
      return '';
  }
});

// 显示在界面上的状态文本
const displayStatus = computed(() => {
  if (isInitialState.value && (!props.modelValue || props.modelValue.trim() === '')) {
    return '未设置';
  }
  // return props.modelValue || props.placeholder;
  return '';
});

// 方法
const updateModelValue = () => {
  if (isInitialState.value) return; // 初始状态不触发更新

  const newValue = generatedClass.value;
  emit('update:modelValue', newValue);
  emit('change', newValue);

  // 触发表单验证
  nextTick(() => {
    elFormItem.validate?.('change').catch(() => {});
  });
};

const clearStyle = () => {
  if (props.disabled) return;
  emit('update:modelValue', '');
  emit('change', '');
  resetToDefaults();

  // 触发表单验证
  nextTick(() => {
    elFormItem.validate?.('change').catch(() => {});
  });
};

const copyToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(generatedClass.value);
    ElMessage.success('已复制到剪贴板！');
  } catch (err) {
    ElMessage.error('复制失败，请手动复制');
  }
};

// 背景模式切换处理
const handleBackgroundModeChange = (newMode: 'solid' | 'gradient') => {
  if (newMode !== backgroundMode.value) {
    backgroundMode.value = newMode;
    isInitialState.value = false; // 标记为已设置状态
    // 不重置颜色值，保持用户已选择的颜色
  }
};

// 颜色选择处理
const handleColorChange = (isColor2 = false) => {
  isInitialState.value = false; // 标记为已设置状态
  // 自动触发更新
  nextTick(() => {
    updateModelValue();
  });
};

// 监听器 - 仅在非初始状态时更新
watch([color1Base, color1Shade, color2Base, color2Shade, gradientDirection, backgroundMode], () => {
  if (!isInitialState.value) {
    updateModelValue();
  }
});

// 监听外部传入的 modelValue 变化
watch(() => props.modelValue, (newValue) => {
  if (newValue !== generatedClass.value) {
    parseModelValue(newValue);
  }
}, { immediate: true });

// 快速深度选择
const quickSelectShade = (shade: number) => {
  const index = standardShades.indexOf(shade);
  if (index !== -1) {
    color1Shade.value = index;
    isInitialState.value = false; // 标记为已设置状态
  }
};
</script>

<template>
  <el-popover
      v-model:visible="popoverVisible"
      placement="right"
      :width="380"
      trigger="manual"
      :title="typeLabels[type] + ' 设置'"
  >
    <template #reference>
      <div :class="[
          'reference-container',
          {
            'disabled': disabled,
            'cursor-not-allowed': disabled,
            'cursor-pointer': !disabled,
            'is-empty': isInitialState && (!modelValue || modelValue.trim() === '')
          }
        ]"
           @click="handleReferenceClick">
        <!-- 预览色块 -->
        <div
            :class="`w-6 h-6 rounded border-2 border-gray-200 flex-shrink-0 ${
              modelValue ? (type === 'text' ? `${modelValue} bg-gray-100` : modelValue) : 'bg-gray-100'
            }`"
        >
          <div
              v-if="type === 'text'"
              :class="`w-full h-full flex items-center justify-center text-xs font-bold ${modelValue || 'text-gray-400'}`"
          >
            A
          </div>
          <!-- 未设置状态的问号图标 -->
          <div
              v-if="!modelValue && type === 'background'"
              class="w-full h-full flex items-center justify-center text-xs text-gray-400"
          >
            ?
          </div>
        </div>

        <div class="flex-1 min-w-0">
          <div class="text-sm font-medium text-gray-900">{{ typeLabels[type] }}</div>
          <div class="text-xs truncate" :class="{ 'text-gray-400': !modelValue, 'text-gray-500': modelValue }">
            {{ displayStatus }}
          </div>
        </div>

        <el-button
            v-if="modelValue && !disabled"
            @click.stop="clearStyle"
            type="text"
            size="small"
            class="!p-1 !text-gray-400 hover:!text-red-500 flex-shrink-0"
        >
          <el-icon><Close /></el-icon>
        </el-button>

        <el-icon
            class="text-gray-400 flex-shrink-0"
            :style="{
              transform: popoverVisible ? 'rotate(0deg)' : 'rotate(180deg)',
              transition: 'transform 0.2s ease'
            }"
        >
          <ArrowRight />
        </el-icon>
      </div>
    </template>

    <div class="space-y-4">
      <!-- 空状态提示 -->
      <div v-if="isInitialState && (!modelValue || modelValue.trim() === '')" class="text-center py-2">
        <div class="text-sm text-gray-500 mb-2">当前未设置任何样式</div>
        <div class="text-xs text-gray-400">请选择颜色和样式选项</div>
      </div>

      <!-- 背景模式选择 (仅在背景色类型下显示) -->
      <div v-if="type === 'background'">
        <label class="block text-sm font-medium text-gray-700 mb-2">背景类型</label>
        <div class="flex space-x-2">
          <el-button
              :type="backgroundMode === 'solid' ? 'primary' : 'default'"
              size="small"
              @click="handleBackgroundModeChange('solid')"
              class="flex-1"
          >
            {{ backgroundModeLabels.solid }}
          </el-button>
          <el-button
              :type="backgroundMode === 'gradient' ? 'primary' : 'default'"
              size="small"
              @click="handleBackgroundModeChange('gradient')"
              class="flex-1"
          >
            {{ backgroundModeLabels.gradient }}
          </el-button>
        </div>
      </div>

      <!-- 主颜色选择 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          {{ (type === 'background' && backgroundMode === 'gradient') ? '起始颜色' : '颜色' }}
        </label>

        <el-select
            v-model="color1Base"
            placeholder="选择颜色类别"
            class="w-full"
            :teleported="false"
            @change="handleColorChange(false)"
        >
          <el-option-group label="特殊颜色">
            <el-option
                v-for="color in specialColors"
                :key="color"
                :value="color"
                :label="getColorBaseName(color)"
            >
              <div class="flex items-center">
                <div
                    :class="`w-4 h-4 rounded mr-2 border ${getPreviewClass(color, 500, 'bg')}`"
                ></div>
                {{ getColorBaseName(color) }}
              </div>
            </el-option>
          </el-option-group>
          <el-option-group label="标准颜色">
            <el-option
                v-for="base in colorBases"
                :key="base"
                :value="base"
                :label="getColorBaseName(base)"
            >
              <div class="flex items-center">
                <div
                    :class="`w-4 h-4 rounded mr-2 border ${getPreviewClass(base, 500, 'bg')}`"
                ></div>
                {{ getColorBaseName(base) }}
              </div>
            </el-option>
          </el-option-group>
        </el-select>

        <!-- 深度滑块 -->
        <div v-if="!isSpecialColor(color1Base)" class="mt-3">
          <label class="block text-xs text-gray-600 mb-2">
            深度: {{ getActualShade(color1Shade) }}
          </label>
          <el-slider
              v-model="color1Shade"
              :min="0"
              :max="10"
              :step="1"
              :marks="sliderMarks"
              show-stops
              @change="handleColorChange(false)"
          />
        </div>

        <!-- 预览 -->
        <div class="flex items-center space-x-3 mt-3 p-3 bg-gray-50 rounded-lg">
          <div
              :class="`w-8 h-8 rounded-lg border-2 border-gray-300 shadow-sm ${getPreviewClass(color1Base, getActualShade(color1Shade), 'bg')}`"
          ></div>
          <div>
            <div class="font-mono text-sm font-medium">{{ getColorName(color1Base, getActualShade(color1Shade)) }}</div>
            <div class="text-xs text-gray-500">{{ getColorBaseName(color1Base) }}</div>
          </div>
        </div>
      </div>

      <!-- 渐变第二色 (仅在背景渐变模式下显示) -->
      <div v-if="type === 'background' && backgroundMode === 'gradient'">
        <label class="block text-sm font-medium text-gray-700 mb-2">结束颜色</label>

        <el-select
            v-model="color2Base"
            placeholder="选择颜色类别"
            class="w-full"
            :teleported="false"
            @change="handleColorChange(true)"
        >
          <el-option-group label="特殊颜色">
            <el-option
                v-for="color in specialColors"
                :key="color"
                :value="color"
                :label="getColorBaseName(color)"
            >
              <div class="flex items-center">
                <div
                    :class="`w-4 h-4 rounded mr-2 border ${getPreviewClass(color, 500, 'bg')}`"
                ></div>
                {{ getColorBaseName(color) }}
              </div>
            </el-option>
          </el-option-group>
          <el-option-group label="标准颜色">
            <el-option
                v-for="base in colorBases"
                :key="base"
                :value="base"
                :label="getColorBaseName(base)"
            >
              <div class="flex items-center">
                <div
                    :class="`w-4 h-4 rounded mr-2 border ${getPreviewClass(base, 500, 'bg')}`"
                ></div>
                {{ getColorBaseName(base) }}
              </div>
            </el-option>
          </el-option-group>
        </el-select>

        <div v-if="!isSpecialColor(color2Base)" class="mt-3">
          <label class="block text-xs text-gray-600 mb-2">
            深度: {{ getActualShade(color2Shade) }}
          </label>
          <el-slider
              v-model="color2Shade"
              :min="0"
              :max="10"
              :step="1"
              :marks="sliderMarks"
              show-stops
              @change="handleColorChange(true)"
          />
        </div>

        <div class="flex items-center space-x-3 mt-3 p-3 bg-gray-50 rounded-lg">
          <div
              :class="`w-8 h-8 rounded-lg border-2 border-gray-300 shadow-sm ${getPreviewClass(color2Base, getActualShade(color2Shade), 'bg')}`"
          ></div>
          <div>
            <div class="font-mono text-sm font-medium">{{ getColorName(color2Base, getActualShade(color2Shade)) }}</div>
            <div class="text-xs text-gray-500">{{ getColorBaseName(color2Base) }}</div>
          </div>
        </div>
      </div>

      <!-- 渐变方向 (仅在背景渐变模式下显示) -->
      <div v-if="type === 'background' && backgroundMode === 'gradient'">
        <label class="block text-sm font-medium text-gray-700 mb-2">渐变方向</label>
        <el-select
            v-model="gradientDirection"
            placeholder="选择渐变方向"
            class="w-full"
            :teleported="false"
            @change="handleColorChange(false)"
        >
          <el-option
              v-for="dir in gradientDirections"
              :key="dir.value"
              :label="dir.label"
              :value="dir.value"
          />
        </el-select>
      </div>

      <!-- 预览示例 -->
      <div v-if="!isInitialState || modelValue">
        <label class="block text-sm font-medium text-gray-700 mb-2">样式预览</label>
        <div class="p-4 border-2 border-dashed border-gray-300 rounded-lg text-center">
          <div
              :class="`inline-block px-4 py-2 rounded-lg font-medium transition-all duration-300 ${generatedClass}${type === 'text' ? ' bg-gray-100' : ''}`"
          >
            示例文字 Sample Text
          </div>
        </div>
      </div>
    </div>
  </el-popover>
</template>

<style scoped>
.reference-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border: 1px solid #d1d5db; /* gray-300 */
  border-radius: 0.5rem;
  background-color: white;
  min-width: 0;
  transition: border-color 0.2s ease;
}

.reference-container:not(.disabled):hover {
  border-color: #60a5fa !important;
}

.reference-container:not(.disabled):focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.reference-container.is-empty {
  border-style: dashed;
  border-color: #d1d5db;
}

.reference-container.is-empty:not(.disabled):hover {
  border-color: #9ca3af !important;
}

.el-popper {
  max-width: none !important;
}

.el-slider__marks-text {
  font-size: 12px !important;
}

/* Tailwind 兼容性 */
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  margin-top: 1rem;
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  margin-left: 0.5rem;
}

.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  margin-left: 0.75rem;
}

.grid-cols-6 {
  grid-template-columns: repeat(6, minmax(0, 1fr));
}

.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}

.flex-1 {
  flex: 1 1 0%;
}
</style>