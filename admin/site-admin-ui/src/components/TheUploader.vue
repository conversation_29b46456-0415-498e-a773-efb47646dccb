<template>
    <div class="flex-1">
        <div v-if="showUpload" class="flex items-center gap-1">
            <el-upload v-bind="$attrs" :show-file-list="false" class="upload-demo" multiple
                :http-request="handleHttpRequest">
                <el-button type="primary" size="small" class="uploadbutton">
                    <span class="iconfont icon-yunshangchuan mr-[3px] text-[14px]" /> 上传
                </el-button>
            </el-upload>
            <div class="el-upload__tip">
                <slot name="tooltip"></slot>
            </div>
        </div>
        <div class="flex flex-col gap-1">
            <div v-for="item in fileList"
                class="w-full flex items-center justify-between">
                <a class="text-[#15aff9]" :href="item.url" :download="item.name">{{ item.name }}</a>
                <el-icon class="cursor-pointer" @click="removeFile(item.id)"><Close color="#E02D2D" /></el-icon>
                <!-- <el-icon-close v-if="showDelete" class="cursor-pointer hover:text-[#E02D2D]" size="4"
                    @click="removeFile(item.id)"></el-icon-close> -->
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
// @ts-ignore
import { getFileListByObjId, removeFileById, uploadFile } from '@/api/company/file'
import { ElMessage, ElMessageBox } from 'element-plus';
import { ref } from 'vue';
import { Close } from '@element-plus/icons-vue'

const props = withDefaults(defineProps<{
    objId: string,
    fileType?: string,
    data?: Record<string, any>,
    showUpload?: boolean,
    showDelete?: boolean
}>(), {
    showUpload: true,
    showDelete: true
})

const fileList = ref<{ id: string, name: string, url: string }[]>([])

const handleHttpRequest = async (options: any) => {
    const formData = new FormData()
    formData.append('file', options.file)
    formData.append('objId', props.objId)
    formData.append('fileType', props.fileType??'')
    if (props.data) {
        Object.keys(props.data).forEach((key) => {
            formData.append(key, props.data?.[key])
        })
    }

    try {
        await uploadFile(formData)
        ElMessage.success('上传成功')
        getFileList()
    } catch (error) {
        console.error(error)
        ElMessage.error('上传失败')
    }
}
const getFileList = () => {
    getFileListByObjId({ objId: props.objId, fileType: props.fileType }).then((res: any) => {
        fileList.value = res.data?.map((item: any) => ({
            id: item.id,
            name: item.fileName,
            url: item.fileUrl
        })) ?? []
    })
}
getFileList()
const removeFile = (id: string) => {
    ElMessageBox.confirm('是否删除该文件？', '提示', {
        type: 'warning',
    }).then(() => {
        removeFileById({ id: id }).then(() => {
            ElMessage.success('删除成功')
        }).finally(() => {
            getFileList()
        })
    })
}

defineExpose({
    getFileList: () => {
        return fileList.value
    }
})
</script>

<style scoped>
.uploadbutton {
  padding: 0 10px !important;
}

.upload-demo {
  position: relative;
}

.el-upload__tip {
  /* margin-top: 0; */
}

.el-button--primary {
  background: linear-gradient(90deg, #36b0ff 0%, #0080d4 100%);
  padding: 0 20px;
}
</style>