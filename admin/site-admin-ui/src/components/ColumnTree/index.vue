<template>
    <div class="bg-white border shadow-md rounded " v-loading="loading">
        <div class="p-2 mt-2">
            <el-input v-model="filterText" clearable placeholder="筛选栏目"></el-input>
        </div>
        <div class="px-2 mt-0 h-[calc(100%_-_56px)]">
            <el-scrollbar height="100%">
                <el-tree ref="treeRef" :data="dataList" default-expand-all :expand-on-click-node="false"
                    highlight-current @node-click="handleNodeClick" :filter-node-method="filterNode">
                    <template #default="scope">
                        <el-icon class="text-sm">
                            <component :is="scope.data.icon ?? 'document'"></component>
                        </el-icon>
                        <span class="text-.5xs pl-1">{{ scope.data.columnName }}</span>
                    </template>
                </el-tree>
            </el-scrollbar>

        </div>

    </div>

</template>

<script lang="ts" setup>
import { getColumnList, publishedList } from '@/api/modules/column';
import { ElTree } from 'element-plus';
import { reactive, ref, watch } from 'vue';
import { toTreeList, filterColumnTree } from '@/utils/common';
import { showInReleaseModules } from '@/utils/release';

const emit = defineEmits(['node-clicked', 'filterColumn'])

const props = withDefaults(defineProps<{
    type: 'all' | 'published'
}>(), {
    type: 'all'
})

const handleNodeClick = (data: IColumn) => {
    emit('node-clicked', data)
}

const loading = ref(false)
const dataList = reactive<IColumn[]>([])
const search = () => {
    loading.value = true
    let getList = getColumnList
    // if (props.type == 'published') {
    //     getList = publishedList
    // }
    getList().then(res => {
        dataList.length = 0
        dataList.push(...filterColumnTree(toTreeList(res.data), (node) => {
            return node
        }))
    }).finally(() => {
        loading.value = false
    })
}

search()


const filterText = ref('')
const treeRef = ref<InstanceType<typeof ElTree>>()

const columnIds = ref([] as string[])
watch(filterText, (val) => {
    columnIds.value.length = 0
    treeRef.value!.filter(val)
    emit('filterColumn', columnIds.value)

})

const filterNode = (value: string, data: any) => {
    if (!value) return true
    if (data.columnName.includes(value)) {
        columnIds.value.push(data.id)
        return true
    }
    return false
}
</script>