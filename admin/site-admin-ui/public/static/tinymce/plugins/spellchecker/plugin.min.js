/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.4.1 (2020-07-08)
 */
!function(N){"use strict";var k=function(e){var t=e;return{get:function(){return t},set:function(e){t=e}}},y=tinymce.util.Tools.resolve("tinymce.PluginManager"),l=Object.hasOwnProperty,h=tinymce.util.Tools.resolve("tinymce.util.Tools"),d=tinymce.util.Tools.resolve("tinymce.util.URI"),f=tinymce.util.Tools.resolve("tinymce.util.XHR"),g=function(e){return e.getParam("spellchecker_rpc_url")},S=function(e){var t=new RegExp('[^\\s!"#$%&()*+,-./:;<=>?@[\\]^_{|}`\xa7\xa9\xab\xae\xb1\xb6\xb7\xb8\xbb\xbc\xbd\xbe\xbf\xd7\xf7\xa4\u201d\u201c\u201e\xa0\u2002\u2003\u2009]+',"g");return e.getParam("spellchecker_wordchar_pattern",t)};function T(e){return e&&1===e.nodeType&&"false"===e.contentEditable}var r=function(a,r){var n,i=[],v=r.dom,g=r.schema.getBlockElements(),h=r.schema.getWhiteSpaceElements(),p=r.schema.getShortEndedElements();function o(e,t){if(!e[0])throw new Error("findAndReplaceDOMText cannot handle zero-length matches");return{start:e.index,end:e.index+e[0].length,text:e[0],data:t}}function c(e){for(var t=e.parentNode;0<e.childNodes.length;)t.insertBefore(e.childNodes[0],e);t.removeChild(e)}function s(e){var t=a.getElementsByTagName("*"),n=[];e="number"==typeof e?""+e:null;for(var r=0;r<t.length;r++){var o=t[r],i=o.getAttribute("data-mce-index");null!==i&&i.length&&-1!==o.className.indexOf("mce-spellchecker-word")&&(i!==e&&null!==e||n.push(o))}return n}function l(e){for(var t=i.length;t--;)if(i[t]===e)return t;return-1}function e(e){for(var t=0,n=i.length;t<n&&!1!==e(i[t],t);t++);return this}function t(e){var t,n=s(e?l(e):null);for(t=n.length;t--;)c(n[t]);return this}function u(e){var t=s(l(e)),n=r.dom.createRng();return n.setStartBefore(t[0]),n.setEndAfter(t[t.length-1]),n}var d=function f(e){var t;if(3===e.nodeType)return e.data;if(h[e.nodeName]&&!g[e.nodeName])return"";if(T(e))return"\n";if(t="",(g[e.nodeName]||p[e.nodeName])&&(t+="\n"),e=e.firstChild)for(;t+=f(e),e=e.nextSibling;);return t}(a);return{text:d,matches:i,each:e,filter:function m(n){var r=[];return e(function(e,t){n(e,t)&&r.push(e)}),i=r,this},reset:function x(){return i.splice(0,i.length),t(),this},matchFromElement:function N(e){return i[e.getAttribute("data-mce-index")]},elementFromMatch:function k(e){return s(l(e))[0]},find:function y(e,t){if(d&&e.global)for(;n=e.exec(d);)i.push(o(n,t));return this},add:function S(e,t,n){return i.push({start:e,end:e+t,text:d.substr(e,t),data:n}),this},wrap:function w(e){return i.length&&function f(e,t,n){var r,o,i,a,c,s=[],l=0,u=e,d=0;(t=t.slice(0)).sort(function(e,t){return e.start-t.start}),c=t.shift();e:for(;;){if((g[u.nodeName]||p[u.nodeName]||T(u))&&l++,3===u.nodeType&&(!o&&u.length+l>=c.end?(o=u,a=c.end-l):r&&s.push(u),!r&&u.length+l>c.start&&(r=u,i=c.start-l),l+=u.length),r&&o){if(u=n({startNode:r,startNodeIndex:i,endNode:o,endNodeIndex:a,innerNodes:s,match:c.text,matchIndex:d}),l-=o.length-a,o=r=null,s=[],d++,!(c=t.shift()))break}else if(h[u.nodeName]&&!g[u.nodeName]||!u.firstChild){if(u.nextSibling){u=u.nextSibling;continue}}else if(!T(u)){u=u.firstChild;continue}for(;;){if(u.nextSibling){u=u.nextSibling;break}if(u.parentNode===e)break e;u=u.parentNode}}}(a,i,function t(o){function m(e,t){var n=i[t];n.stencil||(n.stencil=o(n));var r=n.stencil.cloneNode(!1);return r.setAttribute("data-mce-index",t),e&&r.appendChild(v.doc.createTextNode(e)),r}return function(e){var t,n,r,o=e.startNode,i=e.endNode,a=e.matchIndex,c=v.doc;if(o===i){var s=o;r=s.parentNode,0<e.startNodeIndex&&(t=c.createTextNode(s.data.substring(0,e.startNodeIndex)),r.insertBefore(t,s));var l=m(e.match,a);return r.insertBefore(l,s),e.endNodeIndex<s.length&&(n=c.createTextNode(s.data.substring(e.endNodeIndex)),r.insertBefore(n,s)),s.parentNode.removeChild(s),l}t=c.createTextNode(o.data.substring(0,e.startNodeIndex)),n=c.createTextNode(i.data.substring(e.endNodeIndex));for(var u=m(o.data.substring(e.startNodeIndex),a),d=0,f=e.innerNodes.length;d<f;++d){var g=e.innerNodes[d],h=m(g.data,a);g.parentNode.replaceChild(h,g)}var p=m(i.data.substring(0,e.endNodeIndex),a);return(r=o.parentNode).insertBefore(t,o),r.insertBefore(u,o),r.removeChild(o),(r=i.parentNode).insertBefore(p,i),r.insertBefore(n,i),r.removeChild(i),p}}(e)),this},unwrap:t,replace:function b(e,t){var n=u(e);return n.deleteContents(),0<t.length&&n.insertNode(r.dom.doc.createTextNode(t)),n},rangeFromMatch:u,indexOf:l}},u=function(e,t){if(!t.get()){var n=r(e.getBody(),e);t.set(n)}return t.get()},p=function(e,t,n,r,o,i,a){var c,s,l,u=e.getParam("spellchecker_callback");(u||(c=e,s=t,l=n,function(e,t,r,o){var n={method:e,lang:l.get()},i="";n["addToDictionary"===e?"word":"text"]=t,h.each(n,function(e,t){i&&(i+="&"),i+=t+"="+encodeURIComponent(e)}),f.send({url:new d(s).toAbsolute(g(c)),type:"post",content_type:"application/x-www-form-urlencoded",data:i,success:function(e){var t=JSON.parse(e);if(t)t.error?o(t.error):r(t);else{var n=c.translate("Server response wasn't proper JSON.");o(n)}},error:function(){var e=c.translate("The spelling service was not found: (")+g(c)+c.translate(")");o(e)}})})).call(e.plugins.spellchecker,r,o,i,a)},w=function(t,e,n,r,o,i){if(!a(t,n,r)){t.setProgressState(!0),p(t,e,i,"spellcheck",u(t,r).text,function(e){b(t,n,r,o,e)},function(e){t.notificationManager.open({text:e,type:"error"}),t.setProgressState(!1),a(t,n,r)}),t.focus()}},m=function(e,t,n){e.dom.select("span.mce-spellchecker-word").length||a(e,t,n)},o=function(t,e,n,r,o,i){t.selection.collapse(),i?h.each(t.dom.select("span.mce-spellchecker-word"),function(e){e.getAttribute("data-mce-word")===r&&t.dom.remove(e,!0)}):t.dom.remove(o,!0),m(t,e,n)},a=function(e,t,n){var r=e.selection.getBookmark();if(u(e,n).reset(),e.selection.moveToBookmark(r),n.set(null),t.get())return t.set(!1),e.fire("SpellcheckEnd"),!0},v=function(e){var t=e.getAttribute("data-mce-index");return"number"==typeof t?""+t:t},b=function(t,e,n,r,o){var i=!!o.dictionary,a=o.words;if(t.setProgressState(!1),function(e){for(var t in e)if(l.call(e,t))return!1;return!0}(a)){var c=t.translate("No misspellings found.");return t.notificationManager.open({text:c,type:"info"}),void e.set(!1)}r.set({suggestions:a,hasDictionarySupport:i});var s=t.selection.getBookmark();u(t,n).find(S(t)).filter(function(e){return!!a[e.text]}).wrap(function(e){return t.dom.create("span",{"class":"mce-spellchecker-word","aria-invalid":"spelling","data-mce-bogus":1,"data-mce-word":e.text})}),t.selection.moveToBookmark(s),e.set(!0),t.fire("SpellcheckStart")},x=function(){return(x=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},A="SpellcheckStart SpellcheckEnd",B=function(n,e,r,t,o,i){var a,c,s,l=(s=n,a=h.map(s.getParam("spellchecker_languages","English=en,Danish=da,Dutch=nl,Finnish=fi,French=fr_FR,German=de,Italian=it,Polish=pl,Portuguese=pt_BR,Spanish=es,Swedish=sv").split(","),function(e){return{name:(e=e.split("="))[0],value:e[1]}}),c=[],h.each(a,function(e){c.push({selectable:!0,text:e.name,data:e.value})}),c),u=function(){w(n,e,r,t,i,o)},d={tooltip:"Spellcheck",onAction:u,icon:"spell-check",onSetup:function(e){var t=function(){e.setActive(r.get())};return n.on(A,t),function(){n.off(A,t)}}},f=x(x({},d),{type:"splitbutton",select:function(e){return e===o.get()},fetch:function(e){e(h.map(l,function(e){return{type:"choiceitem",value:e.data,text:e.text}}))},onItemAction:function(e,t){o.set(t)}});1<l.length?n.ui.registry.addSplitButton("spellchecker",f):n.ui.registry.addToggleButton("spellchecker",d),n.ui.registry.addToggleMenuItem("spellchecker",{text:"Spellcheck",icon:"spell-check",onSetup:function(e){e.setActive(r.get());var t=function(){e.setActive(r.get())};return n.on(A,t),function(){n.off(A,t)}},onAction:u})},P=function(c,s,e,l,u,d,f,g){var t=[],n=e.get().suggestions[f];return h.each(n,function(e){t.push({text:e,onAction:function(){c.insertContent(c.dom.encode(e)),c.dom.remove(g),m(c,l,u)}})}),e.get().hasDictionarySupport&&(t.push({type:"separator"}),t.push({text:"Add to dictionary",onAction:function(){var t,e,n,r,o,i,a;e=s,n=l,r=u,o=d,i=f,a=g,(t=c).setProgressState(!0),p(t,e,o,"addToDictionary",i,function(){t.setProgressState(!1),t.dom.remove(a,!0),m(t,n,r)},function(e){t.notificationManager.open({text:e,type:"error"}),t.setProgressState(!1)})}})),t.push.apply(t,[{type:"separator"},{text:"Ignore",onAction:function(){o(c,l,u,f,g)}},{text:"Ignore all",onAction:function(){o(c,l,u,f,g,!0)}}]),t},I=function(o,i,a,c,s,l){o.ui.registry.addContextMenu("spellchecker",{update:function(e){var t=e;if("mce-spellchecker-word"!==t.className)return[];var n=function(e,t){var n=[],r=h.toArray(e.getBody().getElementsByTagName("span"));if(r.length)for(var o=0;o<r.length;o++){var i=v(r[o]);null!==i&&i.length&&i===t.toString()&&n.push(r[o])}return n}(o,v(t));if(0<n.length){var r=o.dom.createRng();return r.setStartBefore(n[0]),r.setEndAfter(n[n.length-1]),o.selection.setRng(r),P(o,i,a,c,s,l,t.getAttribute("data-mce-word"),n)}}})};!function e(){y.add("spellchecker",function(e,t){if(!1==!(!/(^|[ ,])tinymcespellchecker([, ]|$)/.test(e.getParam("plugins"))||!y.get("tinymcespellchecker")||("undefined"!=typeof N.window.console&&N.window.console.log&&N.window.console.log("Spell Checker Pro is incompatible with Spell Checker plugin! Remove 'spellchecker' from the 'plugins' option."),0))){var n=k(!1),r=k((x=(v=e).getParam("language","en"),v.getParam("spellchecker_language",x))),o=k(null),i=k(null);return B(e,t,n,o,r,i),I(e,t,i,n,o,r),f=t,g=n,h=o,p=i,m=r,(d=e).addCommand("mceSpellCheck",function(){w(d,f,g,h,p,m)}),a=e,c=n,s=i,l=o,u=r,{getTextMatcher:function(){return l.get()},getWordCharPattern:function(){return S(a)},markErrors:function(e){b(a,c,l,s,e)},getLanguage:function(){return u.get()}}}var a,c,s,l,u,d,f,g,h,p,m,v,x})}()}(window);