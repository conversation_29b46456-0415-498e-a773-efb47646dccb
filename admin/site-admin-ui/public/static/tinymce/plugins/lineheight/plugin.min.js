(function(e){e.PluginManager.add("lineheight",function(t,n,r){t.on("init",function(){t.formatter.register({lineheight:{inline:"span",styles:{"line-height":"%value"}}})});t.addButton("lineheightselect",function(){var n=[],r="8pt 10pt 12pt 14pt 18pt 24pt 36pt";var i=t.settings.lineheight_formats||r;i.split(" ").forEach(function(e){var t=e,r=e;var i=e.split("=");if(i.length>1){t=i[0];r=i[1]}n.push({text:t,value:r})});return{type:"listbox",text:"Line Height",tooltip:"Line Height",values:n,fixedWidth:true,onPostRender:function(){var e=this;t.on("nodeChange",function(r){var i="lineheight";var s=t.formatter;var o=null;r.parents.forEach(function(e){n.forEach(function(t){if(i){if(s.matchNode(e,i,{value:t.value})){o=t.value}}else{if(s.matchNode(e,t.value)){o=t.value}}if(o){return false}});if(o){return false}});e.value(o)})},onselect:function(t){e.activeEditor.formatter.apply("lineheight",{value:this.value()})}}})});e.PluginManager.requireLangPack("lineheight","de")})(tinymce)