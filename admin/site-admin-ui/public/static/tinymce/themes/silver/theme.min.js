/**
 * TinyMCE version 6.2.0 (2022-09-08)
 */
!function(){"use strict";const e=Object.getPrototypeOf,t=(e,t,o)=>{var n;return!!o(e,t.prototype)||(null===(n=e.constructor)||void 0===n?void 0:n.name)===t.name},o=e=>o=>(e=>{const o=typeof e;return null===e?"null":"object"===o&&Array.isArray(e)?"array":"object"===o&&t(e,String,((e,t)=>t.isPrototypeOf(e)))?"string":o})(o)===e,n=e=>t=>typeof t===e,s=e=>t=>e===t,r=o("string"),a=o("object"),i=o=>((o,n)=>a(o)&&t(o,n,((t,o)=>e(t)===o)))(o,Object),l=o("array"),c=s(null),d=n("boolean"),u=s(void 0),m=e=>null==e,g=e=>!m(e),p=n("function"),h=n("number"),f=(e,t)=>{if(l(e)){for(let o=0,n=e.length;o<n;++o)if(!t(e[o]))return!1;return!0}return!1},b=()=>{},v=(e,t)=>(...o)=>e(t.apply(null,o)),y=e=>()=>e,x=e=>e,w=(e,t)=>e===t;function S(e,...t){return(...o)=>{const n=t.concat(o);return e.apply(null,n)}}const C=e=>t=>!e(t),k=e=>()=>{throw new Error(e)},O=e=>e(),_=y(!1),T=y(!0);var E=tinymce.util.Tools.resolve("tinymce.ThemeManager");class M{constructor(e,t){this.tag=e,this.value=t}static some(e){return new M(!0,e)}static none(){return M.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?M.some(e(this.value)):M.none()}bind(e){return this.tag?e(this.value):M.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:M.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return g(e)?M.some(e):M.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}M.singletonNone=new M(!1);const B=Array.prototype.slice,A=Array.prototype.indexOf,D=Array.prototype.push,F=(e,t)=>A.call(e,t),I=(e,t)=>{const o=F(e,t);return-1===o?M.none():M.some(o)},V=(e,t)=>F(e,t)>-1,R=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return!0;return!1},z=(e,t)=>{const o=[];for(let n=0;n<e;n++)o.push(t(n));return o},H=(e,t)=>{const o=[];for(let n=0;n<e.length;n+=t){const s=B.call(e,n,n+t);o.push(s)}return o},P=(e,t)=>{const o=e.length,n=new Array(o);for(let s=0;s<o;s++){const o=e[s];n[s]=t(o,s)}return n},N=(e,t)=>{for(let o=0,n=e.length;o<n;o++)t(e[o],o)},L=(e,t)=>{const o=[],n=[];for(let s=0,r=e.length;s<r;s++){const r=e[s];(t(r,s)?o:n).push(r)}return{pass:o,fail:n}},W=(e,t)=>{const o=[];for(let n=0,s=e.length;n<s;n++){const s=e[n];t(s,n)&&o.push(s)}return o},U=(e,t,o)=>(((e,t)=>{for(let o=e.length-1;o>=0;o--)t(e[o],o)})(e,((e,n)=>{o=t(o,e,n)})),o),j=(e,t,o)=>(N(e,((e,n)=>{o=t(o,e,n)})),o),G=(e,t)=>((e,t,o)=>{for(let n=0,s=e.length;n<s;n++){const s=e[n];if(t(s,n))return M.some(s);if(o(s,n))break}return M.none()})(e,t,_),$=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return M.some(o);return M.none()},q=e=>{const t=[];for(let o=0,n=e.length;o<n;++o){if(!l(e[o]))throw new Error("Arr.flatten item "+o+" was not an array, input: "+e);D.apply(t,e[o])}return t},X=(e,t)=>q(P(e,t)),J=(e,t)=>{for(let o=0,n=e.length;o<n;++o)if(!0!==t(e[o],o))return!1;return!0},Y=e=>{const t=B.call(e,0);return t.reverse(),t},K=(e,t)=>W(e,(e=>!V(t,e))),Z=(e,t)=>{const o={};for(let n=0,s=e.length;n<s;n++){const s=e[n];o[String(s)]=t(s,n)}return o},Q=e=>[e],ee=(e,t)=>{const o=B.call(e,0);return o.sort(t),o},te=(e,t)=>t>=0&&t<e.length?M.some(e[t]):M.none(),oe=e=>te(e,0),ne=e=>te(e,e.length-1),se=p(Array.from)?Array.from:e=>B.call(e),re=(e,t)=>{for(let o=0;o<e.length;o++){const n=t(e[o],o);if(n.isSome())return n}return M.none()},ae=Object.keys,ie=Object.hasOwnProperty,le=(e,t)=>{const o=ae(e);for(let n=0,s=o.length;n<s;n++){const s=o[n];t(e[s],s)}},ce=(e,t)=>de(e,((e,o)=>({k:o,v:t(e,o)}))),de=(e,t)=>{const o={};return le(e,((e,n)=>{const s=t(e,n);o[s.k]=s.v})),o},ue=e=>(t,o)=>{e[o]=t},me=(e,t,o,n)=>{le(e,((e,s)=>{(t(e,s)?o:n)(e,s)}))},ge=(e,t)=>{const o={};return me(e,t,ue(o),b),o},pe=(e,t)=>{const o=[];return le(e,((e,n)=>{o.push(t(e,n))})),o},he=(e,t)=>{const o=ae(e);for(let n=0,s=o.length;n<s;n++){const s=o[n],r=e[s];if(t(r,s,e))return M.some(r)}return M.none()},fe=e=>pe(e,x),be=(e,t)=>ve(e,t)?M.from(e[t]):M.none(),ve=(e,t)=>ie.call(e,t),ye=(e,t)=>ve(e,t)&&void 0!==e[t]&&null!==e[t],xe=(e,t,o=w)=>e.exists((e=>o(e,t))),we=e=>{const t=[],o=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(o);return t},Se=(e,t,o)=>e.isSome()&&t.isSome()?M.some(o(e.getOrDie(),t.getOrDie())):M.none(),Ce=(e,t)=>e?M.some(t):M.none(),ke=(e,t,o)=>""===t||e.length>=t.length&&e.substr(o,o+t.length)===t,Oe=(e,t,o=0,n)=>{const s=e.indexOf(t,o);return-1!==s&&(!!u(n)||s+t.length<=n)},_e=(e,t)=>ke(e,t,e.length-t.length),Te=(ko=/^\s+|\s+$/g,e=>e.replace(ko,"")),Ee=e=>e.length>0,Me=e=>void 0!==e.style&&p(e.style.getPropertyValue),Be=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},Ae=(e,t)=>{const o=(t||document).createElement("div");if(o.innerHTML=e,!o.hasChildNodes()||o.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return Be(o.childNodes[0])},De=(e,t)=>{const o=(t||document).createElement(e);return Be(o)},Fe=(e,t)=>{const o=(t||document).createTextNode(e);return Be(o)},Ie=Be,Ve="undefined"!=typeof window?window:Function("return this;")(),Re=(e,t)=>((e,t)=>{let o=null!=t?t:Ve;for(let t=0;t<e.length&&null!=o;++t)o=o[e[t]];return o})(e.split("."),t),ze=Object.getPrototypeOf,He=e=>{const t=Re("ownerDocument.defaultView",e);return a(e)&&((e=>((e,t)=>{const o=((e,t)=>Re(e,t))(e,t);if(null==o)throw new Error(e+" not available on this browser");return o})("HTMLElement",e))(t).prototype.isPrototypeOf(e)||/^HTML\w*Element$/.test(ze(e).constructor.name))},Pe=e=>e.dom.nodeName.toLowerCase(),Ne=e=>t=>(e=>e.dom.nodeType)(t)===e,Le=Ne(1),We=Ne(3),Ue=Ne(9),je=Ne(11),Ge=e=>t=>Le(t)&&Pe(t)===e,$e=(e,t)=>{const o=e.dom;if(1!==o.nodeType)return!1;{const e=o;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},qe=e=>1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount,Xe=(e,t)=>e.dom===t.dom,Je=(e,t)=>{const o=e.dom,n=t.dom;return o!==n&&o.contains(n)},Ye=e=>Ie(e.dom.ownerDocument),Ke=e=>Ue(e)?e:Ye(e),Ze=e=>Ie(Ke(e).dom.documentElement),Qe=e=>Ie(Ke(e).dom.defaultView),et=e=>M.from(e.dom.parentNode).map(Ie),tt=e=>M.from(e.dom.parentElement).map(Ie),ot=e=>M.from(e.dom.offsetParent).map(Ie),nt=e=>P(e.dom.childNodes,Ie),st=(e,t)=>{const o=e.dom.childNodes;return M.from(o[t]).map(Ie)},rt=(e,t)=>({element:e,offset:t}),at=(e,t)=>{const o=nt(e);return o.length>0&&t<o.length?rt(o[t],0):rt(e,t)},it=e=>je(e)&&g(e.dom.host),lt=p(Element.prototype.attachShadow)&&p(Node.prototype.getRootNode),ct=y(lt),dt=lt?e=>Ie(e.dom.getRootNode()):Ke,ut=e=>it(e)?e:Ie(Ke(e).dom.body),mt=e=>{const t=dt(e);return it(t)?M.some(t):M.none()},gt=e=>Ie(e.dom.host),pt=e=>{const t=We(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const o=t.ownerDocument;return mt(Ie(t)).fold((()=>o.body.contains(t)),(n=pt,s=gt,e=>n(s(e))));var n,s},ht=()=>ft(Ie(document)),ft=e=>{const t=e.dom.body;if(null==t)throw new Error("Body is not available yet");return Ie(t)},bt=(e,t,o)=>{if(!(r(o)||d(o)||h(o)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",o,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,o+"")},vt=(e,t,o)=>{bt(e.dom,t,o)},yt=(e,t)=>{const o=e.dom;le(t,((e,t)=>{bt(o,t,e)}))},xt=(e,t)=>{const o=e.dom.getAttribute(t);return null===o?void 0:o},wt=(e,t)=>M.from(xt(e,t)),St=(e,t)=>{const o=e.dom;return!(!o||!o.hasAttribute)&&o.hasAttribute(t)},Ct=(e,t)=>{e.dom.removeAttribute(t)},kt=(e,t,o)=>{if(!r(o))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",o,":: Element ",e),new Error("CSS value must be a string: "+o);Me(e)&&e.style.setProperty(t,o)},Ot=(e,t)=>{Me(e)&&e.style.removeProperty(t)},_t=(e,t,o)=>{const n=e.dom;kt(n,t,o)},Tt=(e,t)=>{const o=e.dom;le(t,((e,t)=>{kt(o,t,e)}))},Et=(e,t)=>{const o=e.dom;le(t,((e,t)=>{e.fold((()=>{Ot(o,t)}),(e=>{kt(o,t,e)}))}))},Mt=(e,t)=>{const o=e.dom,n=window.getComputedStyle(o).getPropertyValue(t);return""!==n||pt(e)?n:Bt(o,t)},Bt=(e,t)=>Me(e)?e.style.getPropertyValue(t):"",At=(e,t)=>{const o=e.dom,n=Bt(o,t);return M.from(n).filter((e=>e.length>0))},Dt=e=>{const t={},o=e.dom;if(Me(o))for(let e=0;e<o.style.length;e++){const n=o.style.item(e);t[n]=o.style[n]}return t},Ft=(e,t,o)=>{const n=De(e);return _t(n,t,o),At(n,t).isSome()},It=(e,t)=>{const o=e.dom;Ot(o,t),xe(wt(e,"style").map(Te),"")&&Ct(e,"style")},Vt=e=>e.dom.offsetWidth,Rt=(e,t)=>{const o=o=>{const n=t(o);if(n<=0||null===n){const t=Mt(o,e);return parseFloat(t)||0}return n},n=(e,t)=>j(t,((t,o)=>{const n=Mt(e,o),s=void 0===n?0:parseInt(n,10);return isNaN(s)?t:t+s}),0);return{set:(t,o)=>{if(!h(o)&&!o.match(/^[0-9]+$/))throw new Error(e+".set accepts only positive integer values. Value was "+o);const n=t.dom;Me(n)&&(n.style[e]=o+"px")},get:o,getOuter:o,aggregate:n,max:(e,t,o)=>{const s=n(e,o);return t>s?t-s:0}}},zt=Rt("height",(e=>{const t=e.dom;return pt(e)?t.getBoundingClientRect().height:t.offsetHeight})),Ht=e=>zt.get(e),Pt=e=>zt.getOuter(e),Nt=(e,t)=>({left:e,top:t,translate:(o,n)=>Nt(e+o,t+n)}),Lt=Nt,Wt=(e,t)=>void 0!==e?e:void 0!==t?t:0,Ut=e=>{const t=e.dom.ownerDocument,o=t.body,n=t.defaultView,s=t.documentElement;if(o===e.dom)return Lt(o.offsetLeft,o.offsetTop);const r=Wt(null==n?void 0:n.pageYOffset,s.scrollTop),a=Wt(null==n?void 0:n.pageXOffset,s.scrollLeft),i=Wt(s.clientTop,o.clientTop),l=Wt(s.clientLeft,o.clientLeft);return jt(e).translate(a-l,r-i)},jt=e=>{const t=e.dom,o=t.ownerDocument.body;return o===t?Lt(o.offsetLeft,o.offsetTop):pt(e)?(e=>{const t=e.getBoundingClientRect();return Lt(t.left,t.top)})(t):Lt(0,0)},Gt=Rt("width",(e=>e.dom.offsetWidth)),$t=e=>Gt.get(e),qt=e=>Gt.getOuter(e),Xt=e=>{let t,o=!1;return(...n)=>(o||(o=!0,t=e.apply(null,n)),t)},Jt=()=>Yt(0,0),Yt=(e,t)=>({major:e,minor:t}),Kt={nu:Yt,detect:(e,t)=>{const o=String(t).toLowerCase();return 0===e.length?Jt():((e,t)=>{const o=((e,t)=>{for(let o=0;o<e.length;o++){const n=e[o];if(n.test(t))return n}})(e,t);if(!o)return{major:0,minor:0};const n=e=>Number(t.replace(o,"$"+e));return Yt(n(1),n(2))})(e,o)},unknown:Jt},Zt=(e,t)=>{const o=String(t).toLowerCase();return G(e,(e=>e.search(o)))},Qt=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,eo=e=>t=>Oe(t,e),to=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>Oe(e,"edge/")&&Oe(e,"chrome")&&Oe(e,"safari")&&Oe(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Qt],search:e=>Oe(e,"chrome")&&!Oe(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>Oe(e,"msie")||Oe(e,"trident")},{name:"Opera",versionRegexes:[Qt,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:eo("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:eo("firefox")},{name:"Safari",versionRegexes:[Qt,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(Oe(e,"safari")||Oe(e,"mobile/"))&&Oe(e,"applewebkit")}],oo=[{name:"Windows",search:eo("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>Oe(e,"iphone")||Oe(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:eo("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:eo("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:eo("linux"),versionRegexes:[]},{name:"Solaris",search:eo("sunos"),versionRegexes:[]},{name:"FreeBSD",search:eo("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:eo("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],no={browsers:y(to),oses:y(oo)},so="Edge",ro="Chromium",ao="Opera",io="Firefox",lo="Safari",co=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isEdge:n(so),isChromium:n(ro),isIE:n("IE"),isOpera:n(ao),isFirefox:n(io),isSafari:n(lo)}},uo=()=>co({current:void 0,version:Kt.unknown()}),mo=co,go=(y(so),y(ro),y("IE"),y(ao),y(io),y(lo),"Windows"),po="Android",ho="Linux",fo="macOS",bo="Solaris",vo="FreeBSD",yo="ChromeOS",xo=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isWindows:n(go),isiOS:n("iOS"),isAndroid:n(po),isMacOS:n(fo),isLinux:n(ho),isSolaris:n(bo),isFreeBSD:n(vo),isChromeOS:n(yo)}},wo=()=>xo({current:void 0,version:Kt.unknown()}),So=xo,Co=(y(go),y("iOS"),y(po),y(ho),y(fo),y(bo),y(vo),y(yo),e=>window.matchMedia(e).matches);var ko;let Oo=Xt((()=>((e,t,o)=>{const n=no.browsers(),s=no.oses(),r=t.bind((e=>((e,t)=>re(t.brands,(t=>{const o=t.brand.toLowerCase();return G(e,(e=>{var t;return o===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:Kt.nu(parseInt(t.version,10),0)})))})))(n,e))).orThunk((()=>((e,t)=>Zt(e,t).map((e=>{const o=Kt.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(n,e))).fold(uo,mo),a=((e,t)=>Zt(e,t).map((e=>{const o=Kt.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(s,e).fold(wo,So),i=((e,t,o,n)=>{const s=e.isiOS()&&!0===/ipad/i.test(o),r=e.isiOS()&&!s,a=e.isiOS()||e.isAndroid(),i=a||n("(pointer:coarse)"),l=s||!r&&a&&n("(min-device-width:768px)"),c=r||a&&!l,d=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(o),u=!c&&!l&&!d;return{isiPad:y(s),isiPhone:y(r),isTablet:y(l),isPhone:y(c),isTouch:y(i),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:y(d),isDesktop:y(u)}})(a,r,e,o);return{browser:r,os:a,deviceType:i}})(navigator.userAgent,M.from(navigator.userAgentData),Co)));const _o=()=>Oo(),To=e=>{const t=Ie((e=>{if(ct()&&g(e.target)){const t=Ie(e.target);if(Le(t)&&(e=>g(e.dom.shadowRoot))(t)&&e.composed&&e.composedPath){const t=e.composedPath();if(t)return oe(t)}}return M.from(e.target)})(e).getOr(e.target)),o=()=>e.stopPropagation(),n=()=>e.preventDefault(),s=v(n,o);return((e,t,o,n,s,r,a)=>({target:e,x:t,y:o,stop:n,prevent:s,kill:r,raw:a}))(t,e.clientX,e.clientY,o,n,s,e)},Eo=(e,t,o,n,s)=>{const r=((e,t)=>o=>{e(o)&&t(To(o))})(o,n);return e.dom.addEventListener(t,r,s),{unbind:S(Mo,e,t,r,s)}},Mo=(e,t,o,n)=>{e.dom.removeEventListener(t,o,n)},Bo=(e,t)=>{et(e).each((o=>{o.dom.insertBefore(t.dom,e.dom)}))},Ao=(e,t)=>{const o=(e=>M.from(e.dom.nextSibling).map(Ie))(e);o.fold((()=>{et(e).each((e=>{Fo(e,t)}))}),(e=>{Bo(e,t)}))},Do=(e,t)=>{const o=(e=>st(e,0))(e);o.fold((()=>{Fo(e,t)}),(o=>{e.dom.insertBefore(t.dom,o.dom)}))},Fo=(e,t)=>{e.dom.appendChild(t.dom)},Io=(e,t)=>{N(t,(t=>{Fo(e,t)}))},Vo=e=>{e.dom.textContent="",N(nt(e),(e=>{Ro(e)}))},Ro=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},zo=e=>{const t=void 0!==e?e.dom:document,o=t.body.scrollLeft||t.documentElement.scrollLeft,n=t.body.scrollTop||t.documentElement.scrollTop;return Lt(o,n)},Ho=(e,t,o)=>{const n=(void 0!==o?o.dom:document).defaultView;n&&n.scrollTo(e,t)},Po=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),No=e=>{const t=void 0===e?window:e,o=t.document,n=zo(Ie(o));return(e=>{const t=void 0===e?window:e;return _o().browser.isFirefox()?M.none():M.from(t.visualViewport)})(t).fold((()=>{const e=t.document.documentElement,o=e.clientWidth,s=e.clientHeight;return Po(n.left,n.top,o,s)}),(e=>Po(Math.max(e.pageLeft,n.left),Math.max(e.pageTop,n.top),e.width,e.height)))},Lo=()=>Ie(document),Wo=(e,t)=>e.view(t).fold(y([]),(t=>{const o=e.owner(t),n=Wo(e,o);return[t].concat(n)}));var Uo=Object.freeze({__proto__:null,view:e=>{var t;return(e.dom===document?M.none():M.from(null===(t=e.dom.defaultView)||void 0===t?void 0:t.frameElement)).map(Ie)},owner:e=>Ye(e)});const jo=e=>{const t=Lo(),o=zo(t),n=((e,t)=>{const o=t.owner(e),n=Wo(t,o);return M.some(n)})(e,Uo);return n.fold(S(Ut,e),(t=>{const n=jt(e),s=U(t,((e,t)=>{const o=jt(t);return{left:e.left+o.left,top:e.top+o.top}}),{left:0,top:0});return Lt(s.left+n.left+o.left,s.top+n.top+o.top)}))},Go=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),$o=e=>{const t=Ut(e),o=qt(e),n=Pt(e);return Go(t.left,t.top,o,n)},qo=e=>{const t=jo(e),o=qt(e),n=Pt(e);return Go(t.left,t.top,o,n)},Xo=()=>No(window),Jo=e=>{const t=t=>t(e),o=y(e),n=()=>s,s={tag:!0,inner:e,fold:(t,o)=>o(e),isValue:T,isError:_,map:t=>Ko.value(t(e)),mapError:n,bind:t,exists:t,forall:t,getOr:o,or:n,getOrThunk:o,orThunk:n,getOrDie:o,each:t=>{t(e)},toOptional:()=>M.some(e)};return s},Yo=e=>{const t=()=>o,o={tag:!1,inner:e,fold:(t,o)=>t(e),isValue:_,isError:T,map:t,mapError:t=>Ko.error(t(e)),bind:t,exists:_,forall:T,getOr:x,or:x,getOrThunk:O,orThunk:O,getOrDie:k(String(e)),each:b,toOptional:M.none};return o},Ko={value:Jo,error:Yo,fromOption:(e,t)=>e.fold((()=>Yo(t)),Jo)};var Zo;!function(e){e[e.Error=0]="Error",e[e.Value=1]="Value"}(Zo||(Zo={}));const Qo=(e,t,o)=>e.stype===Zo.Error?t(e.serror):o(e.svalue),en=e=>({stype:Zo.Value,svalue:e}),tn=e=>({stype:Zo.Error,serror:e}),on=en,nn=tn,sn=Qo,rn=(e,t,o,n)=>({tag:"field",key:e,newKey:t,presence:o,prop:n}),an=(e,t,o)=>{switch(e.tag){case"field":return t(e.key,e.newKey,e.presence,e.prop);case"custom":return o(e.newKey,e.instantiator)}},ln=e=>(...t)=>{if(0===t.length)throw new Error("Can't merge zero objects");const o={};for(let n=0;n<t.length;n++){const s=t[n];for(const t in s)ve(s,t)&&(o[t]=e(o[t],s[t]))}return o},cn=ln(((e,t)=>i(e)&&i(t)?cn(e,t):t)),dn=ln(((e,t)=>t)),un=e=>({tag:"defaultedThunk",process:e}),mn=e=>un(y(e)),gn=e=>({tag:"mergeWithThunk",process:e}),pn=e=>{const t=(e=>{const t=[],o=[];return N(e,(e=>{Qo(e,(e=>o.push(e)),(e=>t.push(e)))})),{values:t,errors:o}})(e);return t.errors.length>0?(o=t.errors,v(nn,q)(o)):on(t.values);var o},hn=e=>a(e)&&ae(e).length>100?" removed due to size":JSON.stringify(e,null,2),fn=(e,t)=>nn([{path:e,getErrorInfo:t}]),bn=e=>({extract:(t,o)=>{return n=e(o),s=e=>((e,t)=>fn(e,y(t)))(t,e),n.stype===Zo.Error?s(n.serror):n;var n,s},toString:y("val")}),vn=bn(on),yn=(e,t,o,n)=>n(be(e,t).getOrThunk((()=>o(e)))),xn=(e,t,o,n,s)=>{const r=e=>s.extract(t.concat([n]),e),a=e=>e.fold((()=>on(M.none())),(e=>{const o=s.extract(t.concat([n]),e);return r=o,a=M.some,r.stype===Zo.Value?{stype:Zo.Value,svalue:a(r.svalue)}:r;var r,a}));switch(e.tag){case"required":return((e,t,o,n)=>be(t,o).fold((()=>((e,t,o)=>fn(e,(()=>'Could not find valid *required* value for "'+t+'" in '+hn(o))))(e,o,t)),n))(t,o,n,r);case"defaultedThunk":return yn(o,n,e.process,r);case"option":return((e,t,o)=>o(be(e,t)))(o,n,a);case"defaultedOptionThunk":return((e,t,o,n)=>n(be(e,t).map((t=>!0===t?o(e):t))))(o,n,e.process,a);case"mergeWithThunk":return yn(o,n,y({}),(t=>{const n=cn(e.process(o),t);return r(n)}))}},wn=e=>({extract:(t,o)=>e().extract(t,o),toString:()=>e().toString()}),Sn=e=>ae(ge(e,g)),Cn=e=>{const t=kn(e),o=U(e,((e,t)=>an(t,(t=>cn(e,{[t]:!0})),y(e))),{});return{extract:(e,n)=>{const s=d(n)?[]:Sn(n),r=W(s,(e=>!ye(o,e)));return 0===r.length?t.extract(e,n):((e,t)=>fn(e,(()=>"There are unsupported fields: ["+t.join(", ")+"] specified")))(e,r)},toString:t.toString}},kn=e=>({extract:(t,o)=>((e,t,o)=>{const n={},s=[];for(const r of o)an(r,((o,r,a,i)=>{const l=xn(a,e,t,o,i);sn(l,(e=>{s.push(...e)}),(e=>{n[r]=e}))}),((e,o)=>{n[e]=o(t)}));return s.length>0?nn(s):on(n)})(t,o,e),toString:()=>{const t=P(e,(e=>an(e,((e,t,o,n)=>e+" -> "+n.toString()),((e,t)=>"state("+e+")"))));return"obj{\n"+t.join("\n")+"}"}}),On=e=>({extract:(t,o)=>{const n=P(o,((o,n)=>e.extract(t.concat(["["+n+"]"]),o)));return pn(n)},toString:()=>"array("+e.toString()+")"}),_n=(e,t)=>{const o=void 0!==t?t:x;return{extract:(t,n)=>{const s=[];for(const r of e){const e=r.extract(t,n);if(e.stype===Zo.Value)return{stype:Zo.Value,svalue:o(e.svalue)};s.push(e)}return pn(s)},toString:()=>"oneOf("+P(e,(e=>e.toString())).join(", ")+")"}},Tn=(e,t)=>({extract:(o,n)=>{const s=ae(n),r=((t,o)=>On(bn(e)).extract(t,o))(o,s);return i=e=>{const s=P(e,(e=>rn(e,e,{tag:"required",process:{}},t)));return kn(s).extract(o,n)},(a=r).stype===Zo.Value?i(a.svalue):a;var a,i},toString:()=>"setOf("+t.toString()+")"}),En=v(On,kn),Mn=y(vn),Bn=(e,t)=>bn((o=>{const n=typeof o;return e(o)?on(o):nn(`Expected type: ${t} but got: ${n}`)})),An=Bn(h,"number"),Dn=Bn(r,"string"),Fn=Bn(d,"boolean"),In=Bn(p,"function"),Vn=e=>{if(Object(e)!==e)return!0;switch({}.toString.call(e).slice(8,-1)){case"Boolean":case"Number":case"String":case"Date":case"RegExp":case"Blob":case"FileList":case"ImageData":case"ImageBitmap":case"ArrayBuffer":return!0;case"Array":case"Object":return Object.keys(e).every((t=>Vn(e[t])));default:return!1}},Rn=bn((e=>Vn(e)?on(e):nn("Expected value to be acceptable for sending via postMessage"))),zn=(e,t)=>({extract:(o,n)=>be(n,e).fold((()=>((e,t)=>fn(e,(()=>'Choice schema did not contain choice key: "'+t+'"')))(o,e)),(e=>((e,t,o,n)=>be(o,n).fold((()=>((e,t,o)=>fn(e,(()=>'The chosen schema: "'+o+'" did not exist in branches: '+hn(t))))(e,o,n)),(o=>o.extract(e.concat(["branch: "+n]),t))))(o,n,t,e))),toString:()=>"chooseOn("+e+"). Possible values: "+ae(t)}),Hn=e=>bn((t=>e(t).fold(nn,on))),Pn=(e,t)=>Tn((t=>e(t).fold(tn,en)),t),Nn=(e,t,o)=>{return n=((e,t,o)=>((e,t)=>e.stype===Zo.Error?{stype:Zo.Error,serror:t(e.serror)}:e)(t.extract([e],o),(e=>({input:o,errors:e}))))(e,t,o),Qo(n,Ko.error,Ko.value);var n},Ln=e=>e.fold((e=>{throw new Error(Un(e))}),x),Wn=(e,t,o)=>Ln(Nn(e,t,o)),Un=e=>"Errors: \n"+(e=>{const t=e.length>10?e.slice(0,10).concat([{path:[],getErrorInfo:y("... (only showing first ten failures)")}]):e;return P(t,(e=>"Failed path: ("+e.path.join(" > ")+")\n"+e.getErrorInfo()))})(e.errors).join("\n")+"\n\nInput object: "+hn(e.input),jn=(e,t)=>zn(e,ce(t,kn)),Gn=rn,$n=(e,t)=>({tag:"custom",newKey:e,instantiator:t}),qn=e=>Hn((t=>V(e,t)?Ko.value(t):Ko.error(`Unsupported value: "${t}", choose one of "${e.join(", ")}".`))),Xn=e=>Gn(e,e,{tag:"required",process:{}},Mn()),Jn=(e,t)=>Gn(e,e,{tag:"required",process:{}},t),Yn=e=>Jn(e,An),Kn=e=>Jn(e,Dn),Zn=(e,t)=>Gn(e,e,{tag:"required",process:{}},qn(t)),Qn=e=>Jn(e,In),es=(e,t)=>Gn(e,e,{tag:"required",process:{}},kn(t)),ts=(e,t)=>Gn(e,e,{tag:"required",process:{}},En(t)),os=(e,t)=>Gn(e,e,{tag:"required",process:{}},On(t)),ns=e=>Gn(e,e,{tag:"option",process:{}},Mn()),ss=(e,t)=>Gn(e,e,{tag:"option",process:{}},t),rs=e=>ss(e,An),as=e=>ss(e,Dn),is=(e,t)=>ss(e,qn(t)),ls=e=>ss(e,In),cs=(e,t)=>ss(e,On(t)),ds=(e,t)=>ss(e,kn(t)),us=(e,t)=>Gn(e,e,mn(t),Mn()),ms=(e,t,o)=>Gn(e,e,mn(t),o),gs=(e,t)=>ms(e,t,An),ps=(e,t)=>ms(e,t,Dn),hs=(e,t,o)=>ms(e,t,qn(o)),fs=(e,t)=>ms(e,t,Fn),bs=(e,t)=>ms(e,t,In),vs=(e,t,o)=>ms(e,t,On(o)),ys=(e,t,o)=>ms(e,t,kn(o)),xs=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},ws=e=>{if(!l(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");const t=[],o={};return N(e,((n,s)=>{const r=ae(n);if(1!==r.length)throw new Error("one and only one name per case");const a=r[0],i=n[a];if(void 0!==o[a])throw new Error("duplicate key detected:"+a);if("cata"===a)throw new Error("cannot have a case named cata (sorry)");if(!l(i))throw new Error("case arguments must be an array");t.push(a),o[a]=(...o)=>{const n=o.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+a+". Expected "+i.length+" ("+i+"), got "+n);return{fold:(...t)=>{if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);return t[s].apply(null,o)},match:e=>{const n=ae(e);if(t.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+n.join(","));if(!J(t,(e=>V(n,e))))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+t.join(", "));return e[a].apply(null,o)},log:e=>{console.log(e,{constructors:t,constructor:a,params:o})}}}})),o};ws([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]);const Ss=(e,t)=>((e,t)=>({[e]:t}))(e,t),Cs=e=>(e=>{const t={};return N(e,(e=>{t[e.key]=e.value})),t})(e),ks=e=>p(e)?e:_,Os=(e,t,o)=>{let n=e.dom;const s=ks(o);for(;n.parentNode;){n=n.parentNode;const e=Ie(n),o=t(e);if(o.isSome())return o;if(s(e))break}return M.none()},_s=(e,t,o)=>{const n=t(e),s=ks(o);return n.orThunk((()=>s(e)?M.none():Os(e,t,s)))},Ts=(e,t)=>Xe(e.element,t.event.target),Es={can:T,abort:_,run:b},Ms=e=>{if(!ye(e,"can")&&!ye(e,"abort")&&!ye(e,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(e,null,2)+" does not have can, abort, or run!");return{...Es,...e}},Bs=y,As=Bs("touchstart"),Ds=Bs("touchmove"),Fs=Bs("touchend"),Is=Bs("touchcancel"),Vs=Bs("mousedown"),Rs=Bs("mousemove"),zs=Bs("mouseout"),Hs=Bs("mouseup"),Ps=Bs("mouseover"),Ns=Bs("focusin"),Ls=Bs("focusout"),Ws=Bs("keydown"),Us=Bs("keyup"),js=Bs("input"),Gs=Bs("change"),$s=Bs("click"),qs=Bs("transitioncancel"),Xs=Bs("transitionend"),Js=Bs("transitionstart"),Ys=Bs("selectstart"),Ks=e=>y("alloy."+e),Zs={tap:Ks("tap")},Qs=Ks("focus"),er=Ks("blur.post"),tr=Ks("paste.post"),or=Ks("receive"),nr=Ks("execute"),sr=Ks("focus.item"),rr=Zs.tap,ar=Ks("longpress"),ir=Ks("sandbox.close"),lr=Ks("typeahead.cancel"),cr=Ks("system.init"),dr=Ks("system.touchmove"),ur=Ks("system.touchend"),mr=Ks("system.scroll"),gr=Ks("system.resize"),pr=Ks("system.attached"),hr=Ks("system.detached"),fr=Ks("system.dismissRequested"),br=Ks("system.repositionRequested"),vr=Ks("focusmanager.shifted"),yr=Ks("slotcontainer.visibility"),xr=Ks("change.tab"),wr=Ks("dismiss.tab"),Sr=Ks("highlight"),Cr=Ks("dehighlight"),kr=(e,t)=>{Er(e,e.element,t,{})},Or=(e,t,o)=>{Er(e,e.element,t,o)},_r=e=>{kr(e,nr())},Tr=(e,t,o)=>{Er(e,t,o,{})},Er=(e,t,o,n)=>{const s={target:t,...n};e.getSystem().triggerEvent(o,t,s)},Mr=(e,t,o,n)=>{e.getSystem().triggerEvent(o,t,n.event)},Br=e=>Cs(e),Ar=(e,t)=>({key:e,value:Ms({abort:t})}),Dr=e=>({key:e,value:Ms({run:(e,t)=>{t.event.prevent()}})}),Fr=(e,t)=>({key:e,value:Ms({run:t})}),Ir=(e,t,o)=>({key:e,value:Ms({run:(e,n)=>{t.apply(void 0,[e,n].concat(o))}})}),Vr=e=>t=>({key:e,value:Ms({run:(e,o)=>{Ts(e,o)&&t(e,o)}})}),Rr=(e,t,o)=>((e,t)=>Fr(e,((o,n)=>{o.getSystem().getByUid(t).each((t=>{Mr(t,t.element,e,n)}))})))(e,t.partUids[o]),zr=(e,t)=>Fr(e,((e,o)=>{const n=o.event,s=e.getSystem().getByDom(n.target).getOrThunk((()=>_s(n.target,(t=>e.getSystem().getByDom(t).toOptional()),_).getOr(e)));t(e,s,o)})),Hr=e=>Fr(e,((e,t)=>{t.cut()})),Pr=e=>Fr(e,((e,t)=>{t.stop()})),Nr=(e,t)=>Vr(e)(t),Lr=Vr(pr()),Wr=Vr(hr()),Ur=Vr(cr()),jr=(Yr=nr(),e=>Fr(Yr,e)),Gr=e=>e.dom.innerHTML,$r=(e,t)=>{const o=Ye(e).dom,n=Ie(o.createDocumentFragment()),s=((e,t)=>{const o=(t||document).createElement("div");return o.innerHTML=e,nt(Ie(o))})(t,o);Io(n,s),Vo(e),Fo(e,n)},qr=e=>it(e)?"#shadow-root":(e=>{const t=De("div"),o=Ie(e.dom.cloneNode(!0));return Fo(t,o),Gr(t)})((e=>((e,t)=>Ie(e.dom.cloneNode(!1)))(e))(e)),Xr=e=>qr(e),Jr=Br([((e,t)=>({key:e,value:Ms({can:(e,t)=>{const o=t.event,n=o.originator,s=o.target;return!((e,t,o)=>Xe(t,e.element)&&!Xe(t,o))(e,n,s)||(console.warn(Qs()+" did not get interpreted by the desired target. \nOriginator: "+Xr(n)+"\nTarget: "+Xr(s)+"\nCheck the "+Qs()+" event handlers"),!1)}})}))(Qs())]);var Yr,Kr=Object.freeze({__proto__:null,events:Jr});let Zr=0;const Qr=e=>{const t=(new Date).getTime(),o=Math.floor(1e9*Math.random());return Zr++,e+"_"+o+Zr+String(t)},ea=y("alloy-id-"),ta=y("data-alloy-id"),oa=ea(),na=ta(),sa=(e,t)=>{Object.defineProperty(e.dom,na,{value:t,writable:!0})},ra=e=>{const t=Le(e)?e.dom[na]:null;return M.from(t)},aa=e=>Qr(e),ia=x,la=e=>{const t=t=>`The component must be in a context to execute: ${t}`+(e?"\n"+Xr(e().element)+" is not in context.":""),o=e=>()=>{throw new Error(t(e))},n=e=>()=>{console.warn(t(e))};return{debugInfo:y("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn"),broadcastEvent:n("broadcastEvent"),build:o("build"),buildOrPatch:o("buildOrPatch"),addToWorld:o("addToWorld"),removeFromWorld:o("removeFromWorld"),addToGui:o("addToGui"),removeFromGui:o("removeFromGui"),getByUid:o("getByUid"),getByDom:o("getByDom"),isConnected:_}},ca=la(),da=e=>P(e,(e=>_e(e,"/*")?e.substring(0,e.length-"/*".length):e)),ua=(e,t)=>{const o=e.toString(),n=o.indexOf(")")+1,s=o.indexOf("("),r=o.substring(s+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:da(r)}),e},ma=Qr("alloy-premade"),ga=e=>(Object.defineProperty(e.element.dom,ma,{value:e.uid,writable:!0}),Ss(ma,e)),pa=e=>be(e,ma),ha=e=>((e,t)=>{const o=t.toString(),n=o.indexOf(")")+1,s=o.indexOf("("),r=o.substring(s+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:"OVERRIDE",parameters:da(r.slice(1))}),e})(((t,...o)=>e(t.getApis(),t,...o)),e),fa={init:()=>ba({readState:y("No State required")})},ba=e=>e,va=(e,t)=>{const o={};return le(e,((e,n)=>{le(e,((e,s)=>{const r=be(o,s).getOr([]);o[s]=r.concat([t(n,e)])}))})),o},ya=e=>({classes:u(e.classes)?[]:e.classes,attributes:u(e.attributes)?{}:e.attributes,styles:u(e.styles)?{}:e.styles}),xa=e=>e.cHandler,wa=(e,t)=>({name:e,handler:t}),Sa=(e,t)=>{const o={};return N(e,(e=>{o[e.name()]=e.handlers(t)})),o},Ca=(e,t,o)=>{const n=t[o];return n?((e,t,o,n)=>{try{const t=ee(o,((t,o)=>{const s=t.name,r=o.name,a=n.indexOf(s),i=n.indexOf(r);if(-1===a)throw new Error("The ordering for "+e+" does not have an entry for "+s+".\nOrder specified: "+JSON.stringify(n,null,2));if(-1===i)throw new Error("The ordering for "+e+" does not have an entry for "+r+".\nOrder specified: "+JSON.stringify(n,null,2));return a<i?-1:i<a?1:0}));return Ko.value(t)}catch(e){return Ko.error([e])}})("Event: "+o,0,e,n).map((e=>(e=>{const t=((e,t)=>(...t)=>j(e,((e,o)=>e&&(e=>e.can)(o).apply(void 0,t)),!0))(e),o=((e,t)=>(...t)=>j(e,((e,o)=>e||(e=>e.abort)(o).apply(void 0,t)),!1))(e);return{can:t,abort:o,run:(...t)=>{N(e,(e=>{e.run.apply(void 0,t)}))}}})(P(e,(e=>e.handler))))):((e,t)=>Ko.error(["The event ("+e+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(P(t,(e=>e.name)),null,2)]))(o,e)},ka=(e,t)=>((e,t)=>{const o=(e=>{const t=[],o=[];return N(e,(e=>{e.fold((e=>{t.push(e)}),(e=>{o.push(e)}))})),{errors:t,values:o}})(e);return o.errors.length>0?(n=o.errors,Ko.error(q(n))):((e,t)=>0===e.length?Ko.value(t):Ko.value(cn(t,dn.apply(void 0,e))))(o.values,t);var n})(pe(e,((e,o)=>(1===e.length?Ko.value(e[0].handler):Ca(e,t,o)).map((n=>{const s=(e=>{const t=(e=>p(e)?{can:T,abort:_,run:e}:e)(e);return(e,o,...n)=>{const s=[e,o].concat(n);t.abort.apply(void 0,s)?o.stop():t.can.apply(void 0,s)&&t.run.apply(void 0,s)}})(n),r=e.length>1?W(t[o],(t=>R(e,(e=>e.name===t)))).join(" > "):e[0].name;return Ss(o,((e,t)=>({handler:e,purpose:t}))(s,r))})))),{}),Oa="alloy.base.behaviour",_a=kn([Gn("dom","dom",{tag:"required",process:{}},kn([Xn("tag"),us("styles",{}),us("classes",[]),us("attributes",{}),ns("value"),ns("innerHtml")])),Xn("components"),Xn("uid"),us("events",{}),us("apis",{}),Gn("eventOrder","eventOrder",(Ka={[nr()]:["disabling",Oa,"toggling","typeaheadevents"],[Qs()]:[Oa,"focusing","keying"],[cr()]:[Oa,"disabling","toggling","representing"],[js()]:[Oa,"representing","streaming","invalidating"],[hr()]:[Oa,"representing","item-events","tooltipping"],[Vs()]:["focusing",Oa,"item-type-events"],[As()]:["focusing",Oa,"item-type-events"],[Ps()]:["item-type-events","tooltipping"],[or()]:["receiving","reflecting","tooltipping"]},gn(y(Ka))),Mn()),ns("domModification")]),Ta=e=>e.events,Ea=(e,t)=>{const o=xt(e,t);return void 0===o||""===o?[]:o.split(" ")},Ma=e=>void 0!==e.dom.classList,Ba=e=>Ea(e,"class"),Aa=(e,t)=>{Ma(e)?e.dom.classList.add(t):((e,t)=>{((e,t,o)=>{const n=Ea(e,t).concat([o]);vt(e,t,n.join(" "))})(e,"class",t)})(e,t)},Da=(e,t)=>{Ma(e)?e.dom.classList.remove(t):((e,t)=>{((e,t,o)=>{const n=W(Ea(e,t),(e=>e!==o));n.length>0?vt(e,t,n.join(" ")):Ct(e,t)})(e,"class",t)})(e,t),(e=>{0===(Ma(e)?e.dom.classList:Ba(e)).length&&Ct(e,"class")})(e)},Fa=(e,t)=>Ma(e)&&e.dom.classList.contains(t),Ia=(e,t)=>{N(t,(t=>{Aa(e,t)}))},Va=(e,t)=>{N(t,(t=>{Da(e,t)}))},Ra=e=>e.dom.value,za=(e,t)=>{if(void 0===t)throw new Error("Value.set was undefined");e.dom.value=t},Ha=(e,t,o)=>{o.fold((()=>Fo(e,t)),(e=>{Xe(e,t)||(Bo(e,t),Ro(e))}))},Pa=(e,t,o)=>{const n=P(t,o),s=nt(e);return N(s.slice(n.length),Ro),n},Na=(e,t,o,n)=>{const s=st(e,t),r=n(o,s),a=((e,t,o)=>st(e,t).map((e=>{if(o.exists((t=>!Xe(t,e)))){const t=o.map(Pe).getOr("span"),n=De(t);return Bo(e,n),n}return e})))(e,t,s);return Ha(e,r.element,a),r},La=(e,t)=>{const o=ae(e),n=ae(t);return{toRemove:K(n,o),toSet:((e,o)=>{const n={},s={};return me(e,((e,o)=>!ve(t,o)||e!==t[o]),ue(n),ue(s)),{t:n,f:s}})(e).t}},Wa=(e,t)=>{const{class:o,style:n,...s}=(e=>j(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}))(t),{toSet:r,toRemove:a}=La(e.attributes,s),i=Dt(t),{toSet:l,toRemove:c}=La(e.styles,i),d=(e=>Ma(e)?(e=>{const t=e.dom.classList,o=new Array(t.length);for(let e=0;e<t.length;e++){const n=t.item(e);null!==n&&(o[e]=n)}return o})(e):Ba(e))(t),u=K(d,e.classes),m=K(e.classes,d);return N(a,(e=>Ct(t,e))),yt(t,r),Ia(t,m),Va(t,u),N(c,(e=>It(t,e))),Tt(t,l),e.innerHtml.fold((()=>{const o=e.domChildren;((e,t)=>{Pa(e,t,((t,o)=>{const n=st(e,o);return Ha(e,t,n),t}))})(t,o)}),(e=>{$r(t,e)})),(()=>{const o=t,n=e.value.getOrUndefined();n!==Ra(o)&&za(o,null!=n?n:"")})(),t},Ua=e=>{const t=(e=>{const t=be(e,"behaviours").getOr({});return X(ae(t),(e=>{const o=t[e];return g(o)?[o.me]:[]}))})(e);return((e,t)=>((e,t)=>{const o=P(t,(e=>ds(e.name(),[Xn("config"),us("state",fa)]))),n=Nn("component.behaviours",kn(o),e.behaviours).fold((t=>{throw new Error(Un(t)+"\nComplete spec:\n"+JSON.stringify(e,null,2))}),x);return{list:t,data:ce(n,(e=>{const t=e.map((e=>({config:e.config,state:e.state.init(e.config)})));return y(t)}))}})(e,t))(e,t)},ja=(e,t)=>{const o=()=>m,n=xs(ca),s=Ln((e=>Nn("custom.definition",_a,e))(e)),r=Ua(e),a=(e=>e.list)(r),i=(e=>e.data)(r),l=((e,t,o)=>{const n={...(s=e).dom,uid:s.uid,domChildren:P(s.components,(e=>e.element))};var s;const r=(e=>e.domModification.fold((()=>ya({})),ya))(e),a={"alloy.base.modification":r},i=t.length>0?((e,t,o,n)=>{const s={...t};N(o,(t=>{s[t.name()]=t.exhibit(e,n)}));const r=va(s,((e,t)=>({name:e,modification:t}))),a=e=>U(e,((e,t)=>({...t.modification,...e})),{}),i=U(r.classes,((e,t)=>t.modification.concat(e)),[]),l=a(r.attributes),c=a(r.styles);return ya({classes:i,attributes:l,styles:c})})(o,a,t,n):r;return l=n,c=i,{...l,attributes:{...l.attributes,...c.attributes},styles:{...l.styles,...c.styles},classes:l.classes.concat(c.classes)};var l,c})(s,a,i),c=((e,t)=>{const o=t.filter((t=>Pe(t)===e.tag&&!(e=>e.innerHtml.isSome()&&e.domChildren.length>0)(e)&&!(e=>ve(e.dom,ma))(t))).bind((t=>((e,t)=>{try{const o=Wa(e,t);return M.some(o)}catch(e){return M.none()}})(e,t))).getOrThunk((()=>(e=>{const t=De(e.tag);yt(t,e.attributes),Ia(t,e.classes),Tt(t,e.styles),e.innerHtml.each((e=>$r(t,e)));const o=e.domChildren;return Io(t,o),e.value.each((e=>{za(t,e)})),t})(e)));return sa(o,e.uid),o})(l,t),d=((e,t,o)=>{const n={"alloy.base.behaviour":Ta(e)};return((e,t,o,n)=>{const s=((e,t,o)=>{const n={...o,...Sa(t,e)};return va(n,wa)})(e,o,n);return ka(s,t)})(o,e.eventOrder,t,n).getOrDie()})(s,a,i),u=xs(s.components),m={uid:e.uid,getSystem:n.get,config:t=>{const o=i;return(p(o[t.name()])?o[t.name()]:()=>{throw new Error("Could not find "+t.name()+" in "+JSON.stringify(e,null,2))})()},hasConfigured:e=>p(i[e.name()]),spec:e,readState:e=>i[e]().map((e=>e.state.readState())).getOr("not enabled"),getApis:()=>s.apis,connect:e=>{n.set(e)},disconnect:()=>{n.set(la(o))},element:c,syncComponents:()=>{const e=nt(c),t=X(e,(e=>n.get().getByDom(e).fold((()=>[]),Q)));u.set(t)},components:u.get,events:d};return m},Ga=e=>{const t=Fe(e);return $a({element:t})},$a=e=>{const t=Wn("external.component",Cn([Xn("element"),ns("uid")]),e),o=xs(la()),n=t.uid.getOrThunk((()=>aa("external")));sa(t.element,n);const s={uid:n,getSystem:o.get,config:M.none,hasConfigured:_,connect:e=>{o.set(e)},disconnect:()=>{o.set(la((()=>s)))},getApis:()=>({}),element:t.element,spec:e,readState:y("No state"),syncComponents:b,components:y([]),events:{}};return ga(s)},qa=aa,Xa=(e,t)=>pa(e).getOrThunk((()=>((e,t)=>{const{events:o,...n}=ia(e),s=((e,t)=>{const o=be(e,"components").getOr([]);return t.fold((()=>P(o,Ja)),(e=>P(o,((t,o)=>Xa(t,st(e,o))))))})(n,t),r={...n,events:{...Kr,...o},components:s};return Ko.value(ja(r,t))})((e=>ve(e,"uid"))(e)?e:{uid:qa(""),...e},t).getOrDie())),Ja=e=>Xa(e,M.none()),Ya=ga;var Ka,Za=(e,t,o,n,s)=>e(o,n)?M.some(o):p(s)&&s(o)?M.none():t(o,n,s);const Qa=(e,t,o)=>{let n=e.dom;const s=p(o)?o:_;for(;n.parentNode;){n=n.parentNode;const e=Ie(n);if(t(e))return M.some(e);if(s(e))break}return M.none()},ei=(e,t,o)=>Za(((e,t)=>t(e)),Qa,e,t,o),ti=(e,t,o)=>ei(e,t,o).isSome(),oi=(e,t,o)=>Qa(e,(e=>$e(e,t)),o),ni=(e,t)=>((e,o)=>G(e.dom.childNodes,(e=>{return o=Ie(e),$e(o,t);var o})).map(Ie))(e),si=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return qe(o)?M.none():M.from(o.querySelector(e)).map(Ie)})(t,e),ri=(e,t,o)=>Za(((e,t)=>$e(e,t)),oi,e,t,o),ai="aria-controls",ii=()=>{const e=Qr(ai);return{id:e,link:t=>{vt(t,ai,e)},unlink:e=>{Ct(e,ai)}}},li=(e,t)=>ti(t,(t=>Xe(t,e.element)),_)||((e,t)=>(e=>ei(e,(e=>{if(!Le(e))return!1;const t=xt(e,"id");return void 0!==t&&t.indexOf(ai)>-1})).bind((e=>{const t=xt(e,"id"),o=dt(e);return si(o,`[${ai}="${t}"]`)})))(t).exists((t=>li(e,t))))(e,t);var ci;!function(e){e[e.STOP=0]="STOP",e[e.NORMAL=1]="NORMAL",e[e.LOGGING=2]="LOGGING"}(ci||(ci={}));const di=xs({}),ui=["alloy/data/Fields","alloy/debugging/Debugging"],mi=(e,t,o)=>((e,t,o)=>{switch(be(di.get(),e).orThunk((()=>{const t=ae(di.get());return re(t,(t=>e.indexOf(t)>-1?M.some(di.get()[t]):M.none()))})).getOr(ci.NORMAL)){case ci.NORMAL:return o(gi());case ci.LOGGING:{const n=((e,t)=>{const o=[],n=(new Date).getTime();return{logEventCut:(e,t,n)=>{o.push({outcome:"cut",target:t,purpose:n})},logEventStopped:(e,t,n)=>{o.push({outcome:"stopped",target:t,purpose:n})},logNoParent:(e,t,n)=>{o.push({outcome:"no-parent",target:t,purpose:n})},logEventNoHandlers:(e,t)=>{o.push({outcome:"no-handlers-left",target:t})},logEventResponse:(e,t,n)=>{o.push({outcome:"response",purpose:n,target:t})},write:()=>{const s=(new Date).getTime();V(["mousemove","mouseover","mouseout",cr()],e)||console.log(e,{event:e,time:s-n,target:t.dom,sequence:P(o,(e=>V(["cut","stopped","response"],e.outcome)?"{"+e.purpose+"} "+e.outcome+" at ("+Xr(e.target)+")":e.outcome))})}}})(e,t),s=o(n);return n.write(),s}case ci.STOP:return!0}})(e,t,o),gi=y({logEventCut:b,logEventStopped:b,logNoParent:b,logEventNoHandlers:b,logEventResponse:b,write:b}),pi=y([Xn("menu"),Xn("selectedMenu")]),hi=y([Xn("item"),Xn("selectedItem")]);y(kn(hi().concat(pi())));const fi=y(kn(hi())),bi=es("initSize",[Xn("numColumns"),Xn("numRows")]),vi=()=>es("markers",[Xn("backgroundMenu")].concat(pi()).concat(hi())),yi=e=>es("markers",P(e,Xn)),xi=(e,t,o)=>((()=>{const e=new Error;if(void 0!==e.stack){const t=e.stack.split("\n");G(t,(e=>e.indexOf("alloy")>0&&!R(ui,(t=>e.indexOf(t)>-1)))).getOr("unknown")}})(),Gn(t,t,o,Hn((e=>Ko.value(((...t)=>e.apply(void 0,t))))))),wi=e=>xi(0,e,mn(b)),Si=e=>xi(0,e,mn(M.none)),Ci=e=>xi(0,e,{tag:"required",process:{}}),ki=e=>xi(0,e,{tag:"required",process:{}}),Oi=(e,t)=>$n(e,y(t)),_i=e=>$n(e,x),Ti=y(bi),Ei=(e,t,o,n,s,r,a,i=!1)=>({x:e,y:t,bubble:o,direction:n,placement:s,restriction:r,label:`${a}-${s}`,alwaysFit:i}),Mi=ws([{southeast:[]},{southwest:[]},{northeast:[]},{northwest:[]},{south:[]},{north:[]},{east:[]},{west:[]}]),Bi=Mi.southeast,Ai=Mi.southwest,Di=Mi.northeast,Fi=Mi.northwest,Ii=Mi.south,Vi=Mi.north,Ri=Mi.east,zi=Mi.west,Hi=(e,t,o,n)=>{const s=e+t;return s>n?o:s<o?n:s},Pi=(e,t,o)=>Math.min(Math.max(e,t),o),Ni=(e,t)=>Z(["left","right","top","bottom"],(o=>be(t,o).map((t=>((e,t)=>{switch(t){case 1:return e.x;case 0:return e.x+e.width;case 2:return e.y;case 3:return e.y+e.height}})(e,t))))),Li="layout",Wi=e=>e.x,Ui=(e,t)=>e.x+e.width/2-t.width/2,ji=(e,t)=>e.x+e.width-t.width,Gi=(e,t)=>e.y-t.height,$i=e=>e.y+e.height,qi=(e,t)=>e.y+e.height/2-t.height/2,Xi=(e,t,o)=>Ei(Wi(e),$i(e),o.southeast(),Bi(),"southeast",Ni(e,{left:1,top:3}),Li),Ji=(e,t,o)=>Ei(ji(e,t),$i(e),o.southwest(),Ai(),"southwest",Ni(e,{right:0,top:3}),Li),Yi=(e,t,o)=>Ei(Wi(e),Gi(e,t),o.northeast(),Di(),"northeast",Ni(e,{left:1,bottom:2}),Li),Ki=(e,t,o)=>Ei(ji(e,t),Gi(e,t),o.northwest(),Fi(),"northwest",Ni(e,{right:0,bottom:2}),Li),Zi=(e,t,o)=>Ei(Ui(e,t),Gi(e,t),o.north(),Vi(),"north",Ni(e,{bottom:2}),Li),Qi=(e,t,o)=>Ei(Ui(e,t),$i(e),o.south(),Ii(),"south",Ni(e,{top:3}),Li),el=(e,t,o)=>Ei((e=>e.x+e.width)(e),qi(e,t),o.east(),Ri(),"east",Ni(e,{left:0}),Li),tl=(e,t,o)=>Ei(((e,t)=>e.x-t.width)(e,t),qi(e,t),o.west(),zi(),"west",Ni(e,{right:1}),Li),ol=()=>[Xi,Ji,Yi,Ki,Qi,Zi,el,tl],nl=()=>[Ji,Xi,Ki,Yi,Qi,Zi,el,tl],sl=()=>[Yi,Ki,Xi,Ji,Zi,Qi],rl=()=>[Ki,Yi,Ji,Xi,Zi,Qi],al=()=>[Xi,Ji,Yi,Ki,Qi,Zi],il=()=>[Ji,Xi,Ki,Yi,Qi,Zi];var ll=Object.freeze({__proto__:null,events:e=>Br([Fr(or(),((t,o)=>{const n=e.channels,s=ae(n),r=o,a=((e,t)=>t.universal?e:W(e,(e=>V(t.channels,e))))(s,r);N(a,(e=>{const o=n[e],s=o.schema,a=Wn("channel["+e+"] data\nReceiver: "+Xr(t.element),s,r.data);o.onReceive(t,a)}))}))])}),cl=[Jn("channels",Pn(Ko.value,Cn([Ci("onReceive"),us("schema",Mn())])))];const dl=(e,t,o)=>Ur(((n,s)=>{o(n,e,t)})),ul=e=>({key:e,value:void 0}),ml=(e,t,o,n,s,r,a)=>{const i=e=>ye(e,o)?e[o]():M.none(),l=ce(s,((e,t)=>((e,t,o)=>((e,t,o)=>{const n=o.toString(),s=n.indexOf(")")+1,r=n.indexOf("("),a=n.substring(r+1,s-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:da(a.slice(0,1).concat(a.slice(3)))}),e})(((n,...s)=>{const r=[n].concat(s);return n.config({name:y(e)}).fold((()=>{throw new Error("We could not find any behaviour configuration for: "+e+". Using API: "+o)}),(e=>{const o=Array.prototype.slice.call(r,1);return t.apply(void 0,[n,e.config,e.state].concat(o))}))}),o,t))(o,e,t))),c={...ce(r,((e,t)=>ua(e,t))),...l,revoke:S(ul,o),config:t=>{const n=Wn(o+"-config",e,t);return{key:o,value:{config:n,me:c,configAsRaw:Xt((()=>Wn(o+"-config",e,t))),initialConfig:t,state:a}}},schema:y(t),exhibit:(e,t)=>Se(i(e),be(n,"exhibit"),((e,o)=>o(t,e.config,e.state))).getOrThunk((()=>ya({}))),name:y(o),handlers:e=>i(e).map((e=>be(n,"events").getOr((()=>({})))(e.config,e.state))).getOr({})};return c},gl=e=>Cs(e),pl=Cn([Xn("fields"),Xn("name"),us("active",{}),us("apis",{}),us("state",fa),us("extra",{})]),hl=e=>{const t=Wn("Creating behaviour: "+e.name,pl,e);return((e,t,o,n,s,r)=>{const a=Cn(e),i=ds(t,[("config",l=e,ss("config",Cn(l)))]);var l;return ml(a,i,t,o,n,s,r)})(t.fields,t.name,t.active,t.apis,t.extra,t.state)},fl=Cn([Xn("branchKey"),Xn("branches"),Xn("name"),us("active",{}),us("apis",{}),us("state",fa),us("extra",{})]),bl=e=>{const t=Wn("Creating behaviour: "+e.name,fl,e);return((e,t,o,n,s,r)=>{const a=e,i=ds(t,[ss("config",e)]);return ml(a,i,t,o,n,s,r)})(jn(t.branchKey,t.branches),t.name,t.active,t.apis,t.extra,t.state)},vl=y(void 0),yl=hl({fields:cl,name:"receiving",active:ll});var xl=Object.freeze({__proto__:null,exhibit:(e,t)=>ya({classes:[],styles:t.useFixed()?{}:{position:"relative"}})});const wl=e=>e.dom.focus(),Sl=e=>{const t=dt(e).dom;return e.dom===t.activeElement},Cl=(e=Lo())=>M.from(e.dom.activeElement).map(Ie),kl=e=>Cl(dt(e)).filter((t=>e.dom.contains(t.dom))),Ol=(e,t)=>{const o=dt(t),n=Cl(o).bind((e=>{const o=t=>Xe(e,t);return o(t)?M.some(t):((e,t)=>{const o=e=>{for(let n=0;n<e.childNodes.length;n++){const s=Ie(e.childNodes[n]);if(t(s))return M.some(s);const r=o(e.childNodes[n]);if(r.isSome())return r}return M.none()};return o(e.dom)})(t,o)})),s=e(t);return n.each((e=>{Cl(o).filter((t=>Xe(t,e))).fold((()=>{wl(e)}),b)})),s},_l=(e,t,o,n,s)=>{const r=e=>e+"px";return{position:e,left:t.map(r),top:o.map(r),right:n.map(r),bottom:s.map(r)}},Tl=(e,t)=>{Et(e,(e=>({...e,position:M.some(e.position)}))(t))},El=ws([{none:[]},{relative:["x","y","width","height"]},{fixed:["x","y","width","height"]}]),Ml=(e,t,o,n,s,r)=>{const a=t.rect,i=a.x-o,l=a.y-n,c=s-(i+a.width),d=r-(l+a.height),u=M.some(i),m=M.some(l),g=M.some(c),p=M.some(d),h=M.none();return t.direction.fold((()=>_l(e,u,m,h,h)),(()=>_l(e,h,m,g,h)),(()=>_l(e,u,h,h,p)),(()=>_l(e,h,h,g,p)),(()=>_l(e,u,m,h,h)),(()=>_l(e,u,h,h,p)),(()=>_l(e,u,m,h,h)),(()=>_l(e,h,m,g,h)))},Bl=(e,t)=>e.fold((()=>{const e=t.rect;return _l("absolute",M.some(e.x),M.some(e.y),M.none(),M.none())}),((e,o,n,s)=>Ml("absolute",t,e,o,n,s)),((e,o,n,s)=>Ml("fixed",t,e,o,n,s))),Al=(e,t)=>{const o=S(jo,t),n=e.fold(o,o,(()=>{const e=zo();return jo(t).translate(-e.left,-e.top)})),s=qt(t),r=Pt(t);return Go(n.left,n.top,s,r)},Dl=(e,t)=>t.fold((()=>e.fold(Xo,Xo,Go)),(t=>e.fold(t,t,(()=>{const o=t(),n=Fl(e,o.x,o.y);return Go(n.left,n.top,o.width,o.height)})))),Fl=(e,t,o)=>{const n=Lt(t,o);return e.fold(y(n),y(n),(()=>{const e=zo();return n.translate(-e.left,-e.top)}))};El.none;const Il=El.relative,Vl=El.fixed,Rl="data-alloy-placement",zl=e=>wt(e,Rl),Hl=ws([{fit:["reposition"]},{nofit:["reposition","visibleW","visibleH","isVisible"]}]),Pl=(e,t,o,n)=>{const s=e.bubble,r=s.offset,a=((e,t,o)=>{const n=(n,s)=>t[n].map((t=>{const r="top"===n||"bottom"===n,a=r?o.top:o.left,i=("left"===n||"top"===n?Math.max:Math.min)(t,s)+a;return r?Pi(i,e.y,e.bottom):Pi(i,e.x,e.right)})).getOr(s),s=n("left",e.x),r=n("top",e.y),a=n("right",e.right),i=n("bottom",e.bottom);return Go(s,r,a-s,i-r)})(n,e.restriction,r),i=e.x+r.left,l=e.y+r.top,c=Go(i,l,t,o),{originInBounds:d,sizeInBounds:u,visibleW:m,visibleH:g}=((e,t)=>{const{x:o,y:n,right:s,bottom:r}=t,{x:a,y:i,right:l,bottom:c,width:d,height:u}=e;return{originInBounds:a>=o&&a<=s&&i>=n&&i<=r,sizeInBounds:l<=s&&l>=o&&c<=r&&c>=n,visibleW:Math.min(d,a>=o?s-a:l-o),visibleH:Math.min(u,i>=n?r-i:c-n)}})(c,a),p=d&&u,h=p?c:((e,t)=>{const{x:o,y:n,right:s,bottom:r}=t,{x:a,y:i,width:l,height:c}=e,d=Math.max(o,s-l),u=Math.max(n,r-c),m=Pi(a,o,d),g=Pi(i,n,u),p=Math.min(m+l,s)-m,h=Math.min(g+c,r)-g;return Go(m,g,p,h)})(c,a),f=h.width>0&&h.height>0,{maxWidth:b,maxHeight:v}=((e,t,o)=>{const n=y(t.bottom-o.y),s=y(o.bottom-t.y),r=((e,t,o,n)=>e.fold(t,t,n,n,t,n,o,o))(e,s,s,n),a=y(t.right-o.x),i=y(o.right-t.x),l=((e,t,o,n)=>e.fold(t,n,t,n,o,o,t,n))(e,i,i,a);return{maxWidth:l,maxHeight:r}})(e.direction,h,n),x={rect:h,maxHeight:v,maxWidth:b,direction:e.direction,placement:e.placement,classes:{on:s.classesOn,off:s.classesOff},layout:e.label,testY:l};return p||e.alwaysFit?Hl.fit(x):Hl.nofit(x,m,g,f)},Nl=e=>{const t=xs(M.none()),o=()=>t.get().each(e);return{clear:()=>{o(),t.set(M.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{o(),t.set(M.some(e))}}},Ll=()=>Nl((e=>e.unbind())),Wl=()=>{const e=Nl(b);return{...e,on:t=>e.get().each(t)}},Ul=T,jl=(e,t,o)=>((e,t,o,n)=>Eo(e,t,o,n,!1))(e,t,Ul,o),Gl=(e,t,o)=>((e,t,o,n)=>Eo(e,t,o,n,!0))(e,t,Ul,o),$l=To,ql=["top","bottom","right","left"],Xl="data-alloy-transition-timer",Jl=(e,t,o,n,s,a)=>{const i=((e,t,o)=>o.exists((o=>{const n=e.mode;return"all"===n||o[n]!==t[n]})))(n,s,a);if(i||((e,t)=>((e,t)=>J(t,(t=>Fa(e,t))))(e,t.classes))(e,n)){_t(e,"position",o.position);const a=Al(t,e),l=Bl(t,{...s,rect:a}),c=Z(ql,(e=>l[e]));((e,t)=>{const o=e=>parseFloat(e).toFixed(3);return he(t,((t,n)=>!((e,t,o=w)=>Se(e,t,o).getOr(e.isNone()&&t.isNone()))(e[n].map(o),t.map(o)))).isSome()})(o,c)&&(Et(e,c),i&&((e,t)=>{Ia(e,t.classes),wt(e,Xl).each((t=>{clearTimeout(parseInt(t,10)),Ct(e,Xl)})),((e,t)=>{const o=Ll(),n=Ll();let s;const a=t=>{var o;const n=null!==(o=t.raw.pseudoElement)&&void 0!==o?o:"";return Xe(t.target,e)&&!Ee(n)&&V(ql,t.raw.propertyName)},i=r=>{if(m(r)||a(r)){o.clear(),n.clear();const a=null==r?void 0:r.raw.type;(m(a)||a===Xs())&&(clearTimeout(s),Ct(e,Xl),Va(e,t.classes))}},l=jl(e,Js(),(t=>{a(t)&&(l.unbind(),o.set(jl(e,Xs(),i)),n.set(jl(e,qs(),i)))})),c=(e=>{const t=t=>{const o=Mt(e,t).split(/\s*,\s*/);return W(o,Ee)},o=e=>{if(r(e)&&/^[\d.]+/.test(e)){const t=parseFloat(e);return _e(e,"ms")?t:1e3*t}return 0},n=t("transition-delay"),s=t("transition-duration");return j(s,((e,t,s)=>{const r=o(n[s])+o(t);return Math.max(e,r)}),0)})(e);requestAnimationFrame((()=>{s=setTimeout(i,c+17),vt(e,Xl,s)}))})(e,t)})(e,n),Vt(e))}else Va(e,n.classes)},Yl=(e,t)=>{((e,t)=>{const o=zt.max(e,t,["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"]);_t(e,"max-height",o+"px")})(e,Math.floor(t))},Kl=y(((e,t)=>{Yl(e,t),Tt(e,{"overflow-x":"hidden","overflow-y":"auto"})})),Zl=y(((e,t)=>{Yl(e,t)})),Ql=(e,t,o)=>void 0===e[t]?o:e[t],ec=(e,t,o,n)=>{const s=((e,t,o,n)=>{It(t,"max-height"),It(t,"max-width");const s={width:qt(r=t),height:Pt(r)};var r;return((e,t,o,n,s,r)=>{const a=n.width,i=n.height,l=(t,l,c,d,u)=>{const m=t(o,n,s,e,r),g=Pl(m,a,i,r);return g.fold(y(g),((e,t,o,n)=>(u===n?o>d||t>c:!u&&n)?g:Hl.nofit(l,c,d,u)))};return j(t,((e,t)=>{const o=S(l,t);return e.fold(y(e),o)}),Hl.nofit({rect:o,maxHeight:n.height,maxWidth:n.width,direction:Bi(),placement:"southeast",classes:{on:[],off:[]},layout:"none",testY:o.y},-1,-1,!1)).fold(x,x)})(t,n.preference,e,s,o,n.bounds)})(e,t,o,n);return((e,t,o)=>{const n=Bl(o.origin,t);o.transition.each((s=>{Jl(e,o.origin,n,s,t,o.lastPlacement)})),Tl(e,n)})(t,s,n),((e,t)=>{((e,t)=>{vt(e,Rl,t)})(e,t.placement)})(t,s),((e,t)=>{const o=t.classes;Va(e,o.off),Ia(e,o.on)})(t,s),((e,t,o)=>{(0,o.maxHeightFunction)(e,t.maxHeight)})(t,s,n),((e,t,o)=>{(0,o.maxWidthFunction)(e,t.maxWidth)})(t,s,n),{layout:s.layout,placement:s.placement}},tc=["valignCentre","alignLeft","alignRight","alignCentre","top","bottom","left","right","inset"],oc=(e,t,o,n=1)=>{const s=e*n,r=t*n,a=e=>be(o,e).getOr([]),i=(e,t,o)=>{const n=K(tc,o);return{offset:Lt(e,t),classesOn:X(o,a),classesOff:X(n,a)}};return{southeast:()=>i(-e,t,["top","alignLeft"]),southwest:()=>i(e,t,["top","alignRight"]),south:()=>i(-e/2,t,["top","alignCentre"]),northeast:()=>i(-e,-t,["bottom","alignLeft"]),northwest:()=>i(e,-t,["bottom","alignRight"]),north:()=>i(-e/2,-t,["bottom","alignCentre"]),east:()=>i(e,-t/2,["valignCentre","left"]),west:()=>i(-e,-t/2,["valignCentre","right"]),insetNortheast:()=>i(s,r,["top","alignLeft","inset"]),insetNorthwest:()=>i(-s,r,["top","alignRight","inset"]),insetNorth:()=>i(-s/2,r,["top","alignCentre","inset"]),insetSoutheast:()=>i(s,-r,["bottom","alignLeft","inset"]),insetSouthwest:()=>i(-s,-r,["bottom","alignRight","inset"]),insetSouth:()=>i(-s/2,-r,["bottom","alignCentre","inset"]),insetEast:()=>i(-s,-r/2,["valignCentre","right","inset"]),insetWest:()=>i(s,-r/2,["valignCentre","left","inset"])}},nc=()=>oc(0,0,{}),sc=x,rc=(e,t)=>o=>"rtl"===ac(o)?t:e,ac=e=>"rtl"===Mt(e,"direction")?"rtl":"ltr";var ic;!function(e){e.TopToBottom="toptobottom",e.BottomToTop="bottomtotop"}(ic||(ic={}));const lc="data-alloy-vertical-dir",cc=e=>ti(e,(e=>Le(e)&&xt(e,"data-alloy-vertical-dir")===ic.BottomToTop)),dc=()=>ds("layouts",[Xn("onLtr"),Xn("onRtl"),ns("onBottomLtr"),ns("onBottomRtl")]),uc=(e,t,o,n,s,r,a)=>{const i=a.map(cc).getOr(!1),l=t.layouts.map((t=>t.onLtr(e))),c=t.layouts.map((t=>t.onRtl(e))),d=i?t.layouts.bind((t=>t.onBottomLtr.map((t=>t(e))))).or(l).getOr(s):l.getOr(o),u=i?t.layouts.bind((t=>t.onBottomRtl.map((t=>t(e))))).or(c).getOr(r):c.getOr(n);return rc(d,u)(e)};var mc=[Xn("hotspot"),ns("bubble"),us("overrides",{}),dc(),Oi("placement",((e,t,o)=>{const n=t.hotspot,s=Al(o,n.element),r=uc(e.element,t,al(),il(),sl(),rl(),M.some(t.hotspot.element));return M.some(sc({anchorBox:s,bubble:t.bubble.getOr(nc()),overrides:t.overrides,layouts:r,placer:M.none()}))}))],gc=[Xn("x"),Xn("y"),us("height",0),us("width",0),us("bubble",nc()),us("overrides",{}),dc(),Oi("placement",((e,t,o)=>{const n=Fl(o,t.x,t.y),s=Go(n.left,n.top,t.width,t.height),r=uc(e.element,t,ol(),nl(),ol(),nl(),M.none());return M.some(sc({anchorBox:s,bubble:t.bubble,overrides:t.overrides,layouts:r,placer:M.none()}))}))];const pc=ws([{screen:["point"]},{absolute:["point","scrollLeft","scrollTop"]}]),hc=e=>e.fold(x,((e,t,o)=>e.translate(-t,-o))),fc=e=>e.fold(x,x),bc=e=>j(e,((e,t)=>e.translate(t.left,t.top)),Lt(0,0)),vc=e=>{const t=P(e,fc);return bc(t)},yc=pc.screen,xc=pc.absolute,wc=(e,t,o)=>{const n=Ye(e.element),s=zo(n),r=((e,t,o)=>{const n=Qe(o.root).dom;return M.from(n.frameElement).map(Ie).filter((t=>{const o=Ye(t),n=Ye(e.element);return Xe(o,n)})).map(Ut)})(e,0,o).getOr(s);return xc(r,s.left,s.top)},Sc=(e,t,o,n)=>{const s=yc(Lt(e,t));return M.some(((e,t,o)=>({point:e,width:t,height:o}))(s,o,n))},Cc=(e,t,o,n,s)=>e.map((e=>{const r=[t,e.point],a=(i=()=>vc(r),l=()=>vc(r),c=()=>(e=>{const t=P(e,hc);return bc(t)})(r),n.fold(i,l,c));var i,l,c;const d=(p=a.left,h=a.top,f=e.width,b=e.height,{x:p,y:h,width:f,height:b}),u=o.showAbove?sl():al(),m=o.showAbove?rl():il(),g=uc(s,o,u,m,u,m,M.none());var p,h,f,b;return sc({anchorBox:d,bubble:o.bubble.getOr(nc()),overrides:o.overrides,layouts:g,placer:M.none()})}));var kc=[Xn("node"),Xn("root"),ns("bubble"),dc(),us("overrides",{}),us("showAbove",!1),Oi("placement",((e,t,o)=>{const n=wc(e,0,t);return t.node.filter(pt).bind((s=>{const r=s.dom.getBoundingClientRect(),a=Sc(r.left,r.top,r.width,r.height),i=t.node.getOr(e.element);return Cc(a,n,t,o,i)}))}))];const Oc=(e,t,o,n)=>({start:e,soffset:t,finish:o,foffset:n}),_c=ws([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Tc=(_c.before,_c.on,_c.after,e=>e.fold(x,x,x)),Ec=ws([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Mc={domRange:Ec.domRange,relative:Ec.relative,exact:Ec.exact,exactFromRange:e=>Ec.exact(e.start,e.soffset,e.finish,e.foffset),getWin:e=>{const t=(e=>e.match({domRange:e=>Ie(e.startContainer),relative:(e,t)=>Tc(e),exact:(e,t,o,n)=>e}))(e);return Qe(t)},range:Oc},Bc=(e,t,o)=>{const n=e.document.createRange();var s;return s=n,t.fold((e=>{s.setStartBefore(e.dom)}),((e,t)=>{s.setStart(e.dom,t)}),(e=>{s.setStartAfter(e.dom)})),((e,t)=>{t.fold((t=>{e.setEndBefore(t.dom)}),((t,o)=>{e.setEnd(t.dom,o)}),(t=>{e.setEndAfter(t.dom)}))})(n,o),n},Ac=(e,t,o,n,s)=>{const r=e.document.createRange();return r.setStart(t.dom,o),r.setEnd(n.dom,s),r},Dc=e=>({left:e.left,top:e.top,right:e.right,bottom:e.bottom,width:e.width,height:e.height}),Fc=ws([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Ic=(e,t,o)=>t(Ie(o.startContainer),o.startOffset,Ie(o.endContainer),o.endOffset),Vc=(e,t)=>((e,t)=>{const o=((e,t)=>t.match({domRange:e=>({ltr:y(e),rtl:M.none}),relative:(t,o)=>({ltr:Xt((()=>Bc(e,t,o))),rtl:Xt((()=>M.some(Bc(e,o,t))))}),exact:(t,o,n,s)=>({ltr:Xt((()=>Ac(e,t,o,n,s))),rtl:Xt((()=>M.some(Ac(e,n,s,t,o))))})}))(e,t);return((e,t)=>{const o=t.ltr();return o.collapsed?t.rtl().filter((e=>!1===e.collapsed)).map((e=>Fc.rtl(Ie(e.endContainer),e.endOffset,Ie(e.startContainer),e.startOffset))).getOrThunk((()=>Ic(0,Fc.ltr,o))):Ic(0,Fc.ltr,o)})(0,o)})(e,t).match({ltr:(t,o,n,s)=>{const r=e.document.createRange();return r.setStart(t.dom,o),r.setEnd(n.dom,s),r},rtl:(t,o,n,s)=>{const r=e.document.createRange();return r.setStart(n.dom,s),r.setEnd(t.dom,o),r}});Fc.ltr,Fc.rtl;const Rc=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return qe(o)?[]:P(o.querySelectorAll(e),Ie)})(t,e),zc=e=>{if(e.rangeCount>0){const t=e.getRangeAt(0),o=e.getRangeAt(e.rangeCount-1);return M.some(Oc(Ie(t.startContainer),t.startOffset,Ie(o.endContainer),o.endOffset))}return M.none()},Hc=e=>{if(null===e.anchorNode||null===e.focusNode)return zc(e);{const t=Ie(e.anchorNode),o=Ie(e.focusNode);return((e,t,o,n)=>{const s=((e,t,o,n)=>{const s=Ye(e).dom.createRange();return s.setStart(e.dom,t),s.setEnd(o.dom,n),s})(e,t,o,n),r=Xe(e,o)&&t===n;return s.collapsed&&!r})(t,e.anchorOffset,o,e.focusOffset)?M.some(Oc(t,e.anchorOffset,o,e.focusOffset)):zc(e)}},Pc=(e,t)=>(e=>{const t=e.getClientRects(),o=t.length>0?t[0]:e.getBoundingClientRect();return o.width>0||o.height>0?M.some(o).map(Dc):M.none()})(Vc(e,t)),Nc=((e,t)=>{const o=t=>e(t)?M.from(t.dom.nodeValue):M.none();return{get:t=>{if(!e(t))throw new Error("Can only get text value of a text node");return o(t).getOr("")},getOption:o,set:(t,o)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=o}}})(We),Lc=(e,t)=>({element:e,offset:t}),Wc=(e,t)=>We(e)?Lc(e,t):((e,t)=>{const o=nt(e);if(0===o.length)return Lc(e,t);if(t<o.length)return Lc(o[t],0);{const e=o[o.length-1],t=We(e)?(e=>Nc.get(e))(e).length:nt(e).length;return Lc(e,t)}})(e,t),Uc=(e,t)=>t.getSelection.getOrThunk((()=>()=>(e=>(e=>M.from(e.getSelection()))(e).filter((e=>e.rangeCount>0)).bind(Hc))(e)))().map((e=>{const t=Wc(e.start,e.soffset),o=Wc(e.finish,e.foffset);return Mc.range(t.element,t.offset,o.element,o.offset)}));var jc=[ns("getSelection"),Xn("root"),ns("bubble"),dc(),us("overrides",{}),us("showAbove",!1),Oi("placement",((e,t,o)=>{const n=Qe(t.root).dom,s=wc(e,0,t),r=Uc(n,t).bind((e=>{const t=((e,t)=>(e=>{const t=e.getBoundingClientRect();return t.width>0||t.height>0?M.some(t).map(Dc):M.none()})(Vc(e,t)))(n,Mc.exactFromRange(e)).orThunk((()=>{const t=Fe("\ufeff");Bo(e.start,t);const o=Pc(n,Mc.exact(t,0,t,1));return Ro(t),o}));return t.bind((e=>Sc(e.left,e.top,e.width,e.height)))})),a=Uc(n,t).bind((e=>Le(e.start)?M.some(e.start):tt(e.start))).getOr(e.element);return Cc(r,s,t,o,a)}))];const Gc="link-layout",$c=e=>e.x+e.width,qc=(e,t)=>e.x-t.width,Xc=(e,t)=>e.y-t.height+e.height,Jc=e=>e.y,Yc=(e,t,o)=>Ei($c(e),Jc(e),o.southeast(),Bi(),"southeast",Ni(e,{left:0,top:2}),Gc),Kc=(e,t,o)=>Ei(qc(e,t),Jc(e),o.southwest(),Ai(),"southwest",Ni(e,{right:1,top:2}),Gc),Zc=(e,t,o)=>Ei($c(e),Xc(e,t),o.northeast(),Di(),"northeast",Ni(e,{left:0,bottom:3}),Gc),Qc=(e,t,o)=>Ei(qc(e,t),Xc(e,t),o.northwest(),Fi(),"northwest",Ni(e,{right:1,bottom:3}),Gc),ed=()=>[Yc,Kc,Zc,Qc],td=()=>[Kc,Yc,Qc,Zc];var od=[Xn("item"),dc(),us("overrides",{}),Oi("placement",((e,t,o)=>{const n=Al(o,t.item.element),s=uc(e.element,t,ed(),td(),ed(),td(),M.none());return M.some(sc({anchorBox:n,bubble:nc(),overrides:t.overrides,layouts:s,placer:M.none()}))}))],nd=jn("type",{selection:jc,node:kc,hotspot:mc,submenu:od,makeshift:gc});const sd=[os("classes",Dn),hs("mode","all",["all","layout","placement"])],rd=[us("useFixed",_),ns("getBounds")],ad=[Jn("anchor",nd),ds("transition",sd)],id=(e,t,o,n,s,r,a)=>((e,t,o,n,s,r,a,i)=>{const l=Ql(a,"maxHeightFunction",Kl()),c=Ql(a,"maxWidthFunction",b),d=e.anchorBox,u=e.origin,m={bounds:Dl(u,r),origin:u,preference:n,maxHeightFunction:l,maxWidthFunction:c,lastPlacement:s,transition:i};return ec(d,t,o,m)})(((e,t)=>((e,t)=>({anchorBox:e,origin:t}))(e,t))(o.anchorBox,t),s.element,o.bubble,o.layouts,r,n,o.overrides,a),ld=(e,t,o,n,s,r)=>{const a=r.map($o);return cd(e,t,o,n,s,a)},cd=(e,t,o,n,s,r)=>{const a=Wn("placement.info",kn(ad),s),i=a.anchor,l=n.element,c=o.get(n.uid);Ol((()=>{_t(l,"position","fixed");const s=At(l,"visibility");_t(l,"visibility","hidden");const d=t.useFixed()?(()=>{const e=document.documentElement;return Vl(0,0,e.clientWidth,e.clientHeight)})():(e=>{const t=Ut(e.element),o=e.element.dom.getBoundingClientRect();return Il(t.left,t.top,o.width,o.height)})(e),u=i.placement,m=r.map(y).or(t.getBounds);u(e,i,d).each((t=>{const s=t.placer.getOr(id)(e,d,t,m,n,c,a.transition);o.set(n.uid,s)})),s.fold((()=>{It(l,"visibility")}),(e=>{_t(l,"visibility",e)})),At(l,"left").isNone()&&At(l,"top").isNone()&&At(l,"right").isNone()&&At(l,"bottom").isNone()&&xe(At(l,"position"),"fixed")&&It(l,"position")}),l)};var dd=Object.freeze({__proto__:null,position:(e,t,o,n,s)=>{ld(e,t,o,n,s,M.none())},positionWithin:ld,positionWithinBounds:cd,getMode:(e,t,o)=>t.useFixed()?"fixed":"absolute",reset:(e,t,o,n)=>{const s=n.element;N(["position","left","right","top","bottom"],(e=>It(s,e))),(e=>{Ct(e,Rl)})(s),o.clear(n.uid)}});const ud=hl({fields:rd,name:"positioning",active:xl,apis:dd,state:Object.freeze({__proto__:null,init:()=>{let e={};return ba({readState:()=>e,clear:t=>{g(t)?delete e[t]:e={}},set:(t,o)=>{e[t]=o},get:t=>be(e,t)})}})}),md=e=>e.getSystem().isConnected(),gd=e=>{kr(e,hr());const t=e.components();N(t,gd)},pd=e=>{const t=e.components();N(t,pd),kr(e,pr())},hd=(e,t)=>{e.getSystem().addToWorld(t),pt(e.element)&&pd(t)},fd=e=>{gd(e),e.getSystem().removeFromWorld(e)},bd=(e,t)=>{Fo(e.element,t.element)},vd=(e,t)=>{yd(e,t,Fo)},yd=(e,t,o)=>{e.getSystem().addToWorld(t),o(e.element,t.element),pt(e.element)&&pd(t),e.syncComponents()},xd=e=>{gd(e),Ro(e.element),e.getSystem().removeFromWorld(e)},wd=e=>{const t=et(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()));xd(e),t.each((e=>{e.syncComponents()}))},Sd=e=>{const t=e.components();N(t,xd),Vo(e.element),e.syncComponents()},Cd=(e,t)=>{kd(e,t,Fo)},kd=(e,t,o)=>{o(e,t.element);const n=nt(t.element);N(n,(e=>{t.getByDom(e).each(pd)}))},Od=e=>{const t=nt(e.element);N(t,(t=>{e.getByDom(t).each(gd)})),Ro(e.element)},_d=(e,t,o,n)=>{o.get().each((t=>{Sd(e)}));const s=t.getAttachPoint(e);vd(s,e);const r=e.getSystem().build(n);return vd(e,r),o.set(r),r},Td=(e,t,o,n)=>{const s=_d(e,t,o,n);return t.onOpen(e,s),s},Ed=(e,t,o)=>{o.get().each((n=>{Sd(e),wd(e),t.onClose(e,n),o.clear()}))},Md=(e,t,o)=>o.isOpen(),Bd=(e,t,o)=>{const n=t.getAttachPoint(e);_t(e.element,"position",ud.getMode(n)),((e,t,o,n)=>{At(e.element,t).fold((()=>{Ct(e.element,o)}),(t=>{vt(e.element,o,t)})),_t(e.element,t,"hidden")})(e,"visibility",t.cloakVisibilityAttr)},Ad=(e,t,o)=>{(e=>R(["top","left","right","bottom"],(t=>At(e,t).isSome())))(e.element)||It(e.element,"position"),((e,t,o)=>{wt(e.element,o).fold((()=>It(e.element,t)),(o=>_t(e.element,t,o)))})(e,"visibility",t.cloakVisibilityAttr)};var Dd=Object.freeze({__proto__:null,cloak:Bd,decloak:Ad,open:Td,openWhileCloaked:(e,t,o,n,s)=>{Bd(e,t),Td(e,t,o,n),s(),Ad(e,t)},close:Ed,isOpen:Md,isPartOf:(e,t,o,n)=>Md(0,0,o)&&o.get().exists((o=>t.isPartOf(e,o,n))),getState:(e,t,o)=>o.get(),setContent:(e,t,o,n)=>o.get().map((()=>_d(e,t,o,n)))}),Fd=Object.freeze({__proto__:null,events:(e,t)=>Br([Fr(ir(),((o,n)=>{Ed(o,e,t)}))])}),Id=[wi("onOpen"),wi("onClose"),Xn("isPartOf"),Xn("getAttachPoint"),us("cloakVisibilityAttr","data-precloak-visibility")],Vd=Object.freeze({__proto__:null,init:()=>{const e=Wl(),t=y("not-implemented");return ba({readState:t,isOpen:e.isSet,clear:e.clear,set:e.set,get:e.get})}});const Rd=hl({fields:Id,name:"sandboxing",active:Fd,apis:Dd,state:Vd}),zd=y("dismiss.popups"),Hd=y("reposition.popups"),Pd=y("mouse.released"),Nd=Cn([us("isExtraPart",_),ds("fireEventInstead",[us("event",fr())])]),Ld=e=>{const t=Wn("Dismissal",Nd,e);return{[zd()]:{schema:Cn([Xn("target")]),onReceive:(e,o)=>{Rd.isOpen(e)&&(Rd.isPartOf(e,o.target)||t.isExtraPart(e,o.target)||t.fireEventInstead.fold((()=>Rd.close(e)),(t=>kr(e,t.event))))}}}},Wd=Cn([ds("fireEventInstead",[us("event",br())]),Qn("doReposition")]),Ud=e=>{const t=Wn("Reposition",Wd,e);return{[Hd()]:{onReceive:e=>{Rd.isOpen(e)&&t.fireEventInstead.fold((()=>t.doReposition(e)),(t=>kr(e,t.event)))}}}},jd=(e,t,o)=>{t.store.manager.onLoad(e,t,o)},Gd=(e,t,o)=>{t.store.manager.onUnload(e,t,o)};var $d=Object.freeze({__proto__:null,onLoad:jd,onUnload:Gd,setValue:(e,t,o,n)=>{t.store.manager.setValue(e,t,o,n)},getValue:(e,t,o)=>t.store.manager.getValue(e,t,o),getState:(e,t,o)=>o}),qd=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.resetOnDom?[Lr(((o,n)=>{jd(o,e,t)})),Wr(((o,n)=>{Gd(o,e,t)}))]:[dl(e,t,jd)];return Br(o)}});const Xd=()=>{const e=xs(null);return ba({set:e.set,get:e.get,isNotSet:()=>null===e.get(),clear:()=>{e.set(null)},readState:()=>({mode:"memory",value:e.get()})})},Jd=()=>{const e=xs({}),t=xs({});return ba({readState:()=>({mode:"dataset",dataByValue:e.get(),dataByText:t.get()}),lookup:o=>be(e.get(),o).orThunk((()=>be(t.get(),o))),update:o=>{const n=e.get(),s=t.get(),r={},a={};N(o,(e=>{r[e.value]=e,be(e,"meta").each((t=>{be(t,"text").each((t=>{a[t]=e}))}))})),e.set({...n,...r}),t.set({...s,...a})},clear:()=>{e.set({}),t.set({})}})};var Yd=Object.freeze({__proto__:null,memory:Xd,dataset:Jd,manual:()=>ba({readState:b}),init:e=>e.store.manager.state(e)});const Kd=(e,t,o,n)=>{const s=t.store;o.update([n]),s.setValue(e,n),t.onSetValue(e,n)};var Zd=[ns("initialValue"),Xn("getFallbackEntry"),Xn("getDataKey"),Xn("setValue"),Oi("manager",{setValue:Kd,getValue:(e,t,o)=>{const n=t.store,s=n.getDataKey(e);return o.lookup(s).getOrThunk((()=>n.getFallbackEntry(s)))},onLoad:(e,t,o)=>{t.store.initialValue.each((n=>{Kd(e,t,o,n)}))},onUnload:(e,t,o)=>{o.clear()},state:Jd})],Qd=[Xn("getValue"),us("setValue",b),ns("initialValue"),Oi("manager",{setValue:(e,t,o,n)=>{t.store.setValue(e,n),t.onSetValue(e,n)},getValue:(e,t,o)=>t.store.getValue(e),onLoad:(e,t,o)=>{t.store.initialValue.each((o=>{t.store.setValue(e,o)}))},onUnload:b,state:fa.init})],eu=[ns("initialValue"),Oi("manager",{setValue:(e,t,o,n)=>{o.set(n),t.onSetValue(e,n)},getValue:(e,t,o)=>o.get(),onLoad:(e,t,o)=>{t.store.initialValue.each((e=>{o.isNotSet()&&o.set(e)}))},onUnload:(e,t,o)=>{o.clear()},state:Xd})],tu=[ms("store",{mode:"memory"},jn("mode",{memory:eu,manual:Qd,dataset:Zd})),wi("onSetValue"),us("resetOnDom",!1)];const ou=hl({fields:tu,name:"representing",active:qd,apis:$d,extra:{setValueFrom:(e,t)=>{const o=ou.getValue(t);ou.setValue(e,o)}},state:Yd}),nu=(e,t)=>ys(e,{},P(t,(t=>{return o=t.name(),n="Cannot configure "+t.name()+" for "+e,Gn(o,o,{tag:"option",process:{}},bn((e=>nn("The field: "+o+" is forbidden. "+n))));var o,n})).concat([$n("dump",x)])),su=e=>e.dump,ru=(e,t)=>({...gl(t),...e.dump}),au=nu,iu=ru,lu="placeholder",cu=ws([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),du=e=>ve(e,"uiType"),uu=(e,t,o,n)=>((e,t,o,n)=>du(o)&&o.uiType===lu?((e,t,o,n)=>e.exists((e=>e!==o.owner))?cu.single(!0,y(o)):be(n,o.name).fold((()=>{throw new Error("Unknown placeholder component: "+o.name+"\nKnown: ["+ae(n)+"]\nNamespace: "+e.getOr("none")+"\nSpec: "+JSON.stringify(o,null,2))}),(e=>e.replace())))(e,0,o,n):cu.single(!1,y(o)))(e,0,o,n).fold(((s,r)=>{const a=du(o)?r(t,o.config,o.validated):r(t),i=be(a,"components").getOr([]),l=X(i,(o=>uu(e,t,o,n)));return[{...a,components:l}]}),((e,n)=>{if(du(o)){const e=n(t,o.config,o.validated);return o.validated.preprocess.getOr(x)(e)}return n(t)})),mu=cu.single,gu=cu.multiple,pu=y(lu),hu=ws([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),fu=us("factory",{sketch:x}),bu=us("schema",[]),vu=Xn("name"),yu=Gn("pname","pname",un((e=>"<alloy."+Qr(e.name)+">")),Mn()),xu=$n("schema",(()=>[ns("preprocess")])),wu=us("defaults",y({})),Su=us("overrides",y({})),Cu=kn([fu,bu,vu,yu,wu,Su]),ku=kn([fu,bu,vu,wu,Su]),Ou=kn([fu,bu,vu,yu,wu,Su]),_u=kn([fu,xu,vu,Xn("unit"),yu,wu,Su]),Tu=e=>e.fold(M.some,M.none,M.some,M.some),Eu=e=>{const t=e=>e.name;return e.fold(t,t,t,t)},Mu=(e,t)=>o=>{const n=Wn("Converting part type",t,o);return e(n)},Bu=Mu(hu.required,Cu),Au=Mu(hu.external,ku),Du=Mu(hu.optional,Ou),Fu=Mu(hu.group,_u),Iu=y("entirety");var Vu=Object.freeze({__proto__:null,required:Bu,external:Au,optional:Du,group:Fu,asNamedPart:Tu,name:Eu,asCommon:e=>e.fold(x,x,x,x),original:Iu});const Ru=(e,t,o,n)=>cn(t.defaults(e,o,n),o,{uid:e.partUids[t.name]},t.overrides(e,o,n)),zu=(e,t)=>{const o={};return N(t,(t=>{Tu(t).each((t=>{const n=Hu(e,t.pname);o[t.name]=o=>{const s=Wn("Part: "+t.name+" in "+e,kn(t.schema),o);return{...n,config:o,validated:s}}}))})),o},Hu=(e,t)=>({uiType:pu(),owner:e,name:t}),Pu=(e,t,o)=>({uiType:pu(),owner:e,name:t,config:o,validated:{}}),Nu=e=>X(e,(e=>e.fold(M.none,M.some,M.none,M.none).map((e=>es(e.name,e.schema.concat([_i(Iu())])))).toArray())),Lu=e=>P(e,Eu),Wu=(e,t,o)=>((e,t,o)=>{const n={},s={};return N(o,(e=>{e.fold((e=>{n[e.pname]=mu(!0,((t,o,n)=>e.factory.sketch(Ru(t,e,o,n))))}),(e=>{const o=t.parts[e.name];s[e.name]=y(e.factory.sketch(Ru(t,e,o[Iu()]),o))}),(e=>{n[e.pname]=mu(!1,((t,o,n)=>e.factory.sketch(Ru(t,e,o,n))))}),(e=>{n[e.pname]=gu(!0,((t,o,n)=>{const s=t[e.name];return P(s,(o=>e.factory.sketch(cn(e.defaults(t,o,n),o,e.overrides(t,o)))))}))}))})),{internals:y(n),externals:y(s)}})(0,t,o),Uu=(e,t,o)=>((e,t,o,n)=>{const s=ce(n,((e,t)=>((e,t)=>{let o=!1;return{name:y(e),required:()=>t.fold(((e,t)=>e),((e,t)=>e)),used:()=>o,replace:()=>{if(o)throw new Error("Trying to use the same placeholder more than once: "+e);return o=!0,t}}})(t,e))),r=((e,t,o,n)=>X(o,(o=>uu(e,t,o,n))))(e,t,o,s);return le(s,(o=>{if(!1===o.used()&&o.required())throw new Error("Placeholder: "+o.name()+" was not found in components list\nNamespace: "+e.getOr("none")+"\nComponents: "+JSON.stringify(t.components,null,2))})),r})(M.some(e),t,t.components,o),ju=(e,t,o)=>{const n=t.partUids[o];return e.getSystem().getByUid(n).toOptional()},Gu=(e,t,o)=>ju(e,t,o).getOrDie("Could not find part: "+o),$u=(e,t,o)=>{const n={},s=t.partUids,r=e.getSystem();return N(o,(e=>{n[e]=y(r.getByUid(s[e]))})),n},qu=(e,t)=>{const o=e.getSystem();return ce(t.partUids,((e,t)=>y(o.getByUid(e))))},Xu=e=>ae(e.partUids),Ju=(e,t,o)=>{const n={},s=t.partUids,r=e.getSystem();return N(o,(e=>{n[e]=y(r.getByUid(s[e]).getOrDie())})),n},Yu=(e,t)=>{const o=Lu(t);return Cs(P(o,(t=>({key:t,value:e+"-"+t}))))},Ku=e=>Gn("partUids","partUids",gn((t=>Yu(t.uid,e))),Mn());var Zu=Object.freeze({__proto__:null,generate:zu,generateOne:Pu,schemas:Nu,names:Lu,substitutes:Wu,components:Uu,defaultUids:Yu,defaultUidsSchema:Ku,getAllParts:qu,getAllPartNames:Xu,getPart:ju,getPartOrDie:Gu,getParts:$u,getPartsOrDie:Ju});const Qu=(e,t,o,n,s)=>{const r=((e,t)=>(e.length>0?[es("parts",e)]:[]).concat([Xn("uid"),us("dom",{}),us("components",[]),_i("originalSpec"),us("debug.sketcher",{})]).concat(t))(n,s);return Wn(e+" [SpecSchema]",Cn(r.concat(t)),o)},em=(e,t,o,n,s)=>{const r=tm(s),a=Nu(o),i=Ku(o),l=Qu(e,t,r,a,[i]),c=Wu(0,l,o);return n(l,Uu(e,l,c.internals()),r,c.externals())},tm=e=>(e=>ve(e,"uid"))(e)?e:{...e,uid:aa("uid")},om=Cn([Xn("name"),Xn("factory"),Xn("configFields"),us("apis",{}),us("extraApis",{})]),nm=Cn([Xn("name"),Xn("factory"),Xn("configFields"),Xn("partFields"),us("apis",{}),us("extraApis",{})]),sm=e=>{const t=Wn("Sketcher for "+e.name,om,e),o=ce(t.apis,ha),n=ce(t.extraApis,((e,t)=>ua(e,t)));return{name:t.name,configFields:t.configFields,sketch:e=>((e,t,o,n)=>{const s=tm(n);return o(Qu(e,t,s,[],[]),s)})(t.name,t.configFields,t.factory,e),...o,...n}},rm=e=>{const t=Wn("Sketcher for "+e.name,nm,e),o=zu(t.name,t.partFields),n=ce(t.apis,ha),s=ce(t.extraApis,((e,t)=>ua(e,t)));return{name:t.name,partFields:t.partFields,configFields:t.configFields,sketch:e=>em(t.name,t.configFields,t.partFields,t.factory,e),parts:o,...n,...s}},am=e=>Ge("input")(e)&&"radio"!==xt(e,"type")||Ge("textarea")(e);var im=Object.freeze({__proto__:null,getCurrent:(e,t,o)=>t.find(e)});const lm=[Xn("find")],cm=hl({fields:lm,name:"composing",apis:im}),dm=["input","button","textarea","select"],um=(e,t,o)=>{(t.disabled()?bm:vm)(e,t)},mm=(e,t)=>!0===t.useNative&&V(dm,Pe(e.element)),gm=e=>{vt(e.element,"disabled","disabled")},pm=e=>{Ct(e.element,"disabled")},hm=e=>{vt(e.element,"aria-disabled","true")},fm=e=>{vt(e.element,"aria-disabled","false")},bm=(e,t,o)=>{t.disableClass.each((t=>{Aa(e.element,t)})),(mm(e,t)?gm:hm)(e),t.onDisabled(e)},vm=(e,t,o)=>{t.disableClass.each((t=>{Da(e.element,t)})),(mm(e,t)?pm:fm)(e),t.onEnabled(e)},ym=(e,t)=>mm(e,t)?(e=>St(e.element,"disabled"))(e):(e=>"true"===xt(e.element,"aria-disabled"))(e);var xm=Object.freeze({__proto__:null,enable:vm,disable:bm,isDisabled:ym,onLoad:um,set:(e,t,o,n)=>{(n?bm:vm)(e,t)}}),wm=Object.freeze({__proto__:null,exhibit:(e,t)=>ya({classes:t.disabled()?t.disableClass.toArray():[]}),events:(e,t)=>Br([Ar(nr(),((t,o)=>ym(t,e))),dl(e,t,um)])}),Sm=[bs("disabled",_),us("useNative",!0),ns("disableClass"),wi("onDisabled"),wi("onEnabled")];const Cm=hl({fields:Sm,name:"disabling",active:wm,apis:xm}),km=(e,t,o,n)=>{const s=Rc(e.element,"."+t.highlightClass);N(s,(o=>{R(n,(e=>Xe(e.element,o)))||(Da(o,t.highlightClass),e.getSystem().getByDom(o).each((o=>{t.onDehighlight(e,o),kr(o,Cr())})))}))},Om=(e,t,o,n)=>{km(e,t,0,[n]),_m(e,t,o,n)||(Aa(n.element,t.highlightClass),t.onHighlight(e,n),kr(n,Sr()))},_m=(e,t,o,n)=>Fa(n.element,t.highlightClass),Tm=(e,t,o)=>si(e.element,"."+t.itemClass).bind((t=>e.getSystem().getByDom(t).toOptional())),Em=(e,t,o)=>{const n=Rc(e.element,"."+t.itemClass);return(n.length>0?M.some(n[n.length-1]):M.none()).bind((t=>e.getSystem().getByDom(t).toOptional()))},Mm=(e,t,o,n)=>{const s=Rc(e.element,"."+t.itemClass);return $(s,(e=>Fa(e,t.highlightClass))).bind((t=>{const o=Hi(t,n,0,s.length-1);return e.getSystem().getByDom(s[o]).toOptional()}))},Bm=(e,t,o)=>{const n=Rc(e.element,"."+t.itemClass);return we(P(n,(t=>e.getSystem().getByDom(t).toOptional())))};var Am=Object.freeze({__proto__:null,dehighlightAll:(e,t,o)=>km(e,t,0,[]),dehighlight:(e,t,o,n)=>{_m(e,t,o,n)&&(Da(n.element,t.highlightClass),t.onDehighlight(e,n),kr(n,Cr()))},highlight:Om,highlightFirst:(e,t,o)=>{Tm(e,t).each((n=>{Om(e,t,o,n)}))},highlightLast:(e,t,o)=>{Em(e,t).each((n=>{Om(e,t,o,n)}))},highlightAt:(e,t,o,n)=>{((e,t,o,n)=>{const s=Rc(e.element,"."+t.itemClass);return M.from(s[n]).fold((()=>Ko.error(new Error("No element found with index "+n))),e.getSystem().getByDom)})(e,t,0,n).fold((e=>{throw e}),(n=>{Om(e,t,o,n)}))},highlightBy:(e,t,o,n)=>{const s=Bm(e,t);G(s,n).each((n=>{Om(e,t,o,n)}))},isHighlighted:_m,getHighlighted:(e,t,o)=>si(e.element,"."+t.highlightClass).bind((t=>e.getSystem().getByDom(t).toOptional())),getFirst:Tm,getLast:Em,getPrevious:(e,t,o)=>Mm(e,t,0,-1),getNext:(e,t,o)=>Mm(e,t,0,1),getCandidates:Bm}),Dm=[Xn("highlightClass"),Xn("itemClass"),wi("onHighlight"),wi("onDehighlight")];const Fm=hl({fields:Dm,name:"highlighting",apis:Am}),Im=[8],Vm=[9],Rm=[13],zm=[27],Hm=[32],Pm=[37],Nm=[38],Lm=[39],Wm=[40],Um=(e,t,o)=>{const n=Y(e.slice(0,t)),s=Y(e.slice(t+1));return G(n.concat(s),o)},jm=(e,t,o)=>{const n=Y(e.slice(0,t));return G(n,o)},Gm=(e,t,o)=>{const n=e.slice(0,t),s=e.slice(t+1);return G(s.concat(n),o)},$m=(e,t,o)=>{const n=e.slice(t+1);return G(n,o)},qm=e=>t=>{const o=t.raw;return V(e,o.which)},Xm=e=>t=>J(e,(e=>e(t))),Jm=e=>!0===e.raw.shiftKey,Ym=e=>!0===e.raw.ctrlKey,Km=C(Jm),Zm=(e,t)=>({matches:e,classification:t}),Qm=(e,t,o)=>{t.exists((e=>o.exists((t=>Xe(t,e)))))||Or(e,vr(),{prevFocus:t,newFocus:o})},eg=()=>{const e=e=>kl(e.element);return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().triggerFocus(o,t.element);const s=e(t);Qm(t,n,s)}}},tg=()=>{const e=e=>Fm.getHighlighted(e).map((e=>e.element));return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().getByDom(o).fold(b,(e=>{Fm.highlight(t,e)}));const s=e(t);Qm(t,n,s)}}};var og;!function(e){e.OnFocusMode="onFocus",e.OnEnterOrSpaceMode="onEnterOrSpace",e.OnApiMode="onApi"}(og||(og={}));const ng=(e,t,o,n,s)=>{const r=(e,t,o,n,s)=>{return(r=o(e,t,n,s),a=t.event,G(r,(e=>e.matches(a))).map((e=>e.classification))).bind((o=>o(e,t,n,s)));var r,a},a={schema:()=>e.concat([us("focusManager",eg()),ms("focusInside","onFocus",Hn((e=>V(["onFocus","onEnterOrSpace","onApi"],e)?Ko.value(e):Ko.error("Invalid value for focusInside")))),Oi("handler",a),Oi("state",t),Oi("sendFocusIn",s)]),processKey:r,toEvents:(e,t)=>{const a=e.focusInside!==og.OnFocusMode?M.none():s(e).map((o=>Fr(Qs(),((n,s)=>{o(n,e,t),s.stop()})))),i=[Fr(Ws(),((n,a)=>{r(n,a,o,e,t).fold((()=>{((o,n)=>{const r=qm(Hm.concat(Rm))(n.event);e.focusInside===og.OnEnterOrSpaceMode&&r&&Ts(o,n)&&s(e).each((s=>{s(o,e,t),n.stop()}))})(n,a)}),(e=>{a.stop()}))})),Fr(Us(),((o,s)=>{r(o,s,n,e,t).each((e=>{s.stop()}))}))];return Br(a.toArray().concat(i))}};return a},sg=e=>{const t=[ns("onEscape"),ns("onEnter"),us("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),us("firstTabstop",0),us("useTabstopAt",T),ns("visibilitySelector")].concat([e]),o=(e,t)=>{const o=e.visibilitySelector.bind((e=>ri(t,e))).getOr(t);return Ht(o)>0},n=(e,t,n)=>{((e,t)=>{const n=Rc(e.element,t.selector),s=W(n,(e=>o(t,e)));return M.from(s[t.firstTabstop])})(e,t).each((o=>{t.focusManager.set(e,o)}))},s=(e,t,n,s)=>{const r=Rc(e.element,n.selector);return((e,t)=>t.focusManager.get(e).bind((e=>ri(e,t.selector))))(e,n).bind((t=>$(r,S(Xe,t)).bind((t=>((e,t,n,s,r)=>r(t,n,(e=>((e,t)=>o(e,t)&&e.useTabstopAt(t))(s,e))).fold((()=>s.cyclic?M.some(!0):M.none()),(t=>(s.focusManager.set(e,t),M.some(!0)))))(e,r,t,n,s)))))},r=y([Zm(Xm([Jm,qm(Vm)]),((e,t,o)=>{const n=o.cyclic?Um:jm;return s(e,0,o,n)})),Zm(qm(Vm),((e,t,o)=>{const n=o.cyclic?Gm:$m;return s(e,0,o,n)})),Zm(Xm([Km,qm(Rm)]),((e,t,o)=>o.onEnter.bind((o=>o(e,t)))))]),a=y([Zm(qm(zm),((e,t,o)=>o.onEscape.bind((o=>o(e,t)))))]);return ng(t,fa.init,r,a,(()=>M.some(n)))};var rg=sg($n("cyclic",_)),ag=sg($n("cyclic",T));const ig=(e,t,o)=>am(o)&&qm(Hm)(t.event)?M.none():((e,t,o)=>(Tr(e,o,nr()),M.some(!0)))(e,0,o),lg=(e,t)=>M.some(!0),cg=[us("execute",ig),us("useSpace",!1),us("useEnter",!0),us("useControlEnter",!1),us("useDown",!1)],dg=(e,t,o)=>o.execute(e,t,e.element);var ug=ng(cg,fa.init,((e,t,o,n)=>{const s=o.useSpace&&!am(e.element)?Hm:[],r=o.useEnter?Rm:[],a=o.useDown?Wm:[],i=s.concat(r).concat(a);return[Zm(qm(i),dg)].concat(o.useControlEnter?[Zm(Xm([Ym,qm(Rm)]),dg)]:[])}),((e,t,o,n)=>o.useSpace&&!am(e.element)?[Zm(qm(Hm),lg)]:[]),(()=>M.none()));const mg=()=>{const e=Wl();return ba({readState:()=>e.get().map((e=>({numRows:String(e.numRows),numColumns:String(e.numColumns)}))).getOr({numRows:"?",numColumns:"?"}),setGridSize:(t,o)=>{e.set({numRows:t,numColumns:o})},getNumRows:()=>e.get().map((e=>e.numRows)),getNumColumns:()=>e.get().map((e=>e.numColumns))})};var gg=Object.freeze({__proto__:null,flatgrid:mg,init:e=>e.state(e)});const pg=e=>(t,o,n,s)=>{const r=e(t.element);return vg(r,t,o,n,s)},hg=(e,t)=>{const o=rc(e,t);return pg(o)},fg=(e,t)=>{const o=rc(t,e);return pg(o)},bg=e=>(t,o,n,s)=>vg(e,t,o,n,s),vg=(e,t,o,n,s)=>n.focusManager.get(t).bind((o=>e(t.element,o,n,s))).map((e=>(n.focusManager.set(t,e),!0))),yg=bg,xg=bg,wg=bg,Sg=e=>!(e=>e.offsetWidth<=0&&e.offsetHeight<=0)(e.dom),Cg=(e,t,o)=>{const n=Rc(e,o);return((e,o)=>$(e,(e=>Xe(e,t))).map((t=>({index:t,candidates:e}))))(W(n,Sg))},kg=(e,t)=>$(e,(e=>Xe(t,e))),Og=(e,t,o,n)=>n(Math.floor(t/o),t%o).bind((t=>{const n=t.row*o+t.column;return n>=0&&n<e.length?M.some(e[n]):M.none()})),_g=(e,t,o,n,s)=>Og(e,t,n,((t,r)=>{const a=t===o-1?e.length-t*n:n,i=Hi(r,s,0,a-1);return M.some({row:t,column:i})})),Tg=(e,t,o,n,s)=>Og(e,t,n,((t,r)=>{const a=Hi(t,s,0,o-1),i=a===o-1?e.length-a*n:n,l=Pi(r,0,i-1);return M.some({row:a,column:l})})),Eg=[Xn("selector"),us("execute",ig),Si("onEscape"),us("captureTab",!1),Ti()],Mg=(e,t,o)=>{si(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},Bg=e=>(t,o,n,s)=>Cg(t,o,n.selector).bind((t=>e(t.candidates,t.index,s.getNumRows().getOr(n.initSize.numRows),s.getNumColumns().getOr(n.initSize.numColumns)))),Ag=(e,t,o)=>o.captureTab?M.some(!0):M.none(),Dg=Bg(((e,t,o,n)=>_g(e,t,o,n,-1))),Fg=Bg(((e,t,o,n)=>_g(e,t,o,n,1))),Ig=Bg(((e,t,o,n)=>Tg(e,t,o,n,-1))),Vg=Bg(((e,t,o,n)=>Tg(e,t,o,n,1))),Rg=y([Zm(qm(Pm),hg(Dg,Fg)),Zm(qm(Lm),fg(Dg,Fg)),Zm(qm(Nm),yg(Ig)),Zm(qm(Wm),xg(Vg)),Zm(Xm([Jm,qm(Vm)]),Ag),Zm(Xm([Km,qm(Vm)]),Ag),Zm(qm(Hm.concat(Rm)),((e,t,o,n)=>((e,t)=>t.focusManager.get(e).bind((e=>ri(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n)))))]),zg=y([Zm(qm(zm),((e,t,o)=>o.onEscape(e,t))),Zm(qm(Hm),lg)]);var Hg=ng(Eg,mg,Rg,zg,(()=>M.some(Mg)));const Pg=(e,t,o,n)=>{const s=(e,t,o)=>{const r=Hi(t,n,0,o.length-1);return r===e?M.none():(a=o[r],"button"===Pe(a)&&"disabled"===xt(a,"disabled")?s(e,r,o):M.from(o[r]));var a};return Cg(e,o,t).bind((e=>{const t=e.index,o=e.candidates;return s(t,t,o)}))},Ng=[Xn("selector"),us("getInitial",M.none),us("execute",ig),Si("onEscape"),us("executeOnMove",!1),us("allowVertical",!0)],Lg=(e,t,o)=>((e,t)=>t.focusManager.get(e).bind((e=>ri(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n))),Wg=(e,t,o)=>{t.getInitial(e).orThunk((()=>si(e.element,t.selector))).each((o=>{t.focusManager.set(e,o)}))},Ug=(e,t,o)=>Pg(e,o.selector,t,-1),jg=(e,t,o)=>Pg(e,o.selector,t,1),Gg=e=>(t,o,n,s)=>e(t,o,n,s).bind((()=>n.executeOnMove?Lg(t,o,n):M.some(!0))),$g=y([Zm(qm(Hm),lg),Zm(qm(zm),((e,t,o)=>o.onEscape(e,t)))]);var qg=ng(Ng,fa.init,((e,t,o,n)=>{const s=Pm.concat(o.allowVertical?Nm:[]),r=Lm.concat(o.allowVertical?Wm:[]);return[Zm(qm(s),Gg(hg(Ug,jg))),Zm(qm(r),Gg(fg(Ug,jg))),Zm(qm(Rm),Lg),Zm(qm(Hm),Lg)]}),$g,(()=>M.some(Wg)));const Xg=(e,t,o)=>M.from(e[t]).bind((e=>M.from(e[o]).map((e=>({rowIndex:t,columnIndex:o,cell:e}))))),Jg=(e,t,o,n)=>{const s=e[t].length,r=Hi(o,n,0,s-1);return Xg(e,t,r)},Yg=(e,t,o,n)=>{const s=Hi(o,n,0,e.length-1),r=e[s].length,a=Pi(t,0,r-1);return Xg(e,s,a)},Kg=(e,t,o,n)=>{const s=e[t].length,r=Pi(o+n,0,s-1);return Xg(e,t,r)},Zg=(e,t,o,n)=>{const s=Pi(o+n,0,e.length-1),r=e[s].length,a=Pi(t,0,r-1);return Xg(e,s,a)},Qg=[es("selectors",[Xn("row"),Xn("cell")]),us("cycles",!0),us("previousSelector",M.none),us("execute",ig)],ep=(e,t,o)=>{t.previousSelector(e).orThunk((()=>{const o=t.selectors;return si(e.element,o.cell)})).each((o=>{t.focusManager.set(e,o)}))},tp=(e,t)=>(o,n,s)=>{const r=s.cycles?e:t;return ri(n,s.selectors.row).bind((e=>{const t=Rc(e,s.selectors.cell);return kg(t,n).bind((t=>{const n=Rc(o,s.selectors.row);return kg(n,e).bind((e=>{const o=((e,t)=>P(e,(e=>Rc(e,t.selectors.cell))))(n,s);return r(o,e,t).map((e=>e.cell))}))}))}))},op=tp(((e,t,o)=>Jg(e,t,o,-1)),((e,t,o)=>Kg(e,t,o,-1))),np=tp(((e,t,o)=>Jg(e,t,o,1)),((e,t,o)=>Kg(e,t,o,1))),sp=tp(((e,t,o)=>Yg(e,o,t,-1)),((e,t,o)=>Zg(e,o,t,-1))),rp=tp(((e,t,o)=>Yg(e,o,t,1)),((e,t,o)=>Zg(e,o,t,1))),ap=y([Zm(qm(Pm),hg(op,np)),Zm(qm(Lm),fg(op,np)),Zm(qm(Nm),yg(sp)),Zm(qm(Wm),xg(rp)),Zm(qm(Hm.concat(Rm)),((e,t,o)=>kl(e.element).bind((n=>o.execute(e,t,n)))))]),ip=y([Zm(qm(Hm),lg)]);var lp=ng(Qg,fa.init,ap,ip,(()=>M.some(ep)));const cp=[Xn("selector"),us("execute",ig),us("moveOnTab",!1)],dp=(e,t,o)=>o.focusManager.get(e).bind((n=>o.execute(e,t,n))),up=(e,t,o)=>{si(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},mp=(e,t,o)=>Pg(e,o.selector,t,-1),gp=(e,t,o)=>Pg(e,o.selector,t,1),pp=y([Zm(qm(Nm),wg(mp)),Zm(qm(Wm),wg(gp)),Zm(Xm([Jm,qm(Vm)]),((e,t,o,n)=>o.moveOnTab?wg(mp)(e,t,o,n):M.none())),Zm(Xm([Km,qm(Vm)]),((e,t,o,n)=>o.moveOnTab?wg(gp)(e,t,o,n):M.none())),Zm(qm(Rm),dp),Zm(qm(Hm),dp)]),hp=y([Zm(qm(Hm),lg)]);var fp=ng(cp,fa.init,pp,hp,(()=>M.some(up)));const bp=[Si("onSpace"),Si("onEnter"),Si("onShiftEnter"),Si("onLeft"),Si("onRight"),Si("onTab"),Si("onShiftTab"),Si("onUp"),Si("onDown"),Si("onEscape"),us("stopSpaceKeyup",!1),ns("focusIn")];var vp=ng(bp,fa.init,((e,t,o)=>[Zm(qm(Hm),o.onSpace),Zm(Xm([Km,qm(Rm)]),o.onEnter),Zm(Xm([Jm,qm(Rm)]),o.onShiftEnter),Zm(Xm([Jm,qm(Vm)]),o.onShiftTab),Zm(Xm([Km,qm(Vm)]),o.onTab),Zm(qm(Nm),o.onUp),Zm(qm(Wm),o.onDown),Zm(qm(Pm),o.onLeft),Zm(qm(Lm),o.onRight),Zm(qm(Hm),o.onSpace)]),((e,t,o)=>[...o.stopSpaceKeyup?[Zm(qm(Hm),lg)]:[],Zm(qm(zm),o.onEscape)]),(e=>e.focusIn));const yp=rg.schema(),xp=ag.schema(),wp=qg.schema(),Sp=Hg.schema(),Cp=lp.schema(),kp=ug.schema(),Op=fp.schema(),_p=vp.schema(),Tp=bl({branchKey:"mode",branches:Object.freeze({__proto__:null,acyclic:yp,cyclic:xp,flow:wp,flatgrid:Sp,matrix:Cp,execution:kp,menu:Op,special:_p}),name:"keying",active:{events:(e,t)=>e.handler.toEvents(e,t)},apis:{focusIn:(e,t,o)=>{t.sendFocusIn(t).fold((()=>{e.getSystem().triggerFocus(e.element,e.element)}),(n=>{n(e,t,o)}))},setGridSize:(e,t,o,n,s)=>{(e=>ye(e,"setGridSize"))(o)?o.setGridSize(n,s):console.error("Layout does not support setGridSize")}},state:gg}),Ep=(e,t)=>{Ol((()=>{((e,t,o)=>{const n=e.components();(e=>{N(e.components(),(e=>Ro(e.element))),Vo(e.element),e.syncComponents()})(e);const s=o(t),r=K(n,s);N(r,(t=>{gd(t),e.getSystem().removeFromWorld(t)})),N(s,(t=>{md(t)?bd(e,t):(e.getSystem().addToWorld(t),bd(e,t),pt(e.element)&&pd(t))})),e.syncComponents()})(e,t,(()=>P(t,e.getSystem().build)))}),e.element)},Mp=(e,t)=>{Ol((()=>{((o,n,s)=>{const r=o.components(),a=X(n,(e=>pa(e).toArray()));N(r,(e=>{V(a,e)||fd(e)}));const i=((e,t,o)=>Pa(e,t,((t,n)=>Na(e,n,t,o))))(e.element,t,e.getSystem().buildOrPatch),l=K(r,i);N(l,(e=>{md(e)&&fd(e)})),N(i,(e=>{md(e)||hd(o,e)})),o.syncComponents()})(e,t)}),e.element)},Bp=(e,t,o,n)=>{fd(t);const s=Na(e.element,o,n,e.getSystem().buildOrPatch);hd(e,s),e.syncComponents()},Ap=(e,t,o)=>{const n=e.getSystem().build(o);yd(e,n,t)},Dp=(e,t,o,n)=>{wd(t),Ap(e,((e,t)=>((e,t,o)=>{st(e,o).fold((()=>{Fo(e,t)}),(e=>{Bo(e,t)}))})(e,t,o)),n)},Fp=(e,t)=>e.components(),Ip=(e,t,o,n,s)=>{const r=Fp(e);return M.from(r[n]).map((o=>(s.fold((()=>wd(o)),(s=>{(t.reuseDom?Bp:Dp)(e,o,n,s)})),o)))};var Vp=Object.freeze({__proto__:null,append:(e,t,o,n)=>{Ap(e,Fo,n)},prepend:(e,t,o,n)=>{Ap(e,Do,n)},remove:(e,t,o,n)=>{const s=Fp(e),r=G(s,(e=>Xe(n.element,e.element)));r.each(wd)},replaceAt:Ip,replaceBy:(e,t,o,n,s)=>{const r=Fp(e);return $(r,n).bind((o=>Ip(e,t,0,o,s)))},set:(e,t,o,n)=>(t.reuseDom?Mp:Ep)(e,n),contents:Fp});const Rp=hl({fields:[fs("reuseDom",!0)],name:"replacing",apis:Vp}),zp=(e,t)=>{const o=((e,t)=>{const o=Br(t);return hl({fields:[Xn("enabled")],name:e,active:{events:y(o)}})})(e,t);return{key:e,value:{config:{},me:o,configAsRaw:y({}),initialConfig:{},state:fa}}},Hp=(e,t)=>{t.ignore||(wl(e.element),t.onFocus(e))};var Pp=Object.freeze({__proto__:null,focus:Hp,blur:(e,t)=>{t.ignore||(e=>{e.dom.blur()})(e.element)},isFocused:e=>Sl(e.element)}),Np=Object.freeze({__proto__:null,exhibit:(e,t)=>{const o=t.ignore?{}:{attributes:{tabindex:"-1"}};return ya(o)},events:e=>Br([Fr(Qs(),((t,o)=>{Hp(t,e),o.stop()}))].concat(e.stopMousedown?[Fr(Vs(),((e,t)=>{t.event.prevent()}))]:[]))}),Lp=[wi("onFocus"),us("stopMousedown",!1),us("ignore",!1)];const Wp=hl({fields:Lp,name:"focusing",active:Np,apis:Pp}),Up=(e,t,o,n)=>{const s=o.get();o.set(n),((e,t,o)=>{t.toggleClass.each((t=>{o.get()?Aa(e.element,t):Da(e.element,t)}))})(e,t,o),((e,t,o)=>{const n=t.aria;n.update(e,n,o.get())})(e,t,o),s!==n&&t.onToggled(e,n)},jp=(e,t,o)=>{Up(e,t,o,!o.get())},Gp=(e,t,o)=>{Up(e,t,o,t.selected)};var $p=Object.freeze({__proto__:null,onLoad:Gp,toggle:jp,isOn:(e,t,o)=>o.get(),on:(e,t,o)=>{Up(e,t,o,!0)},off:(e,t,o)=>{Up(e,t,o,!1)},set:Up}),qp=Object.freeze({__proto__:null,exhibit:()=>ya({}),events:(e,t)=>{const o=(n=e,s=t,r=jp,jr((e=>{r(e,n,s)})));var n,s,r;const a=dl(e,t,Gp);return Br(q([e.toggleOnExecute?[o]:[],[a]]))}});const Xp=(e,t,o)=>{vt(e.element,"aria-expanded",o)};var Jp=[us("selected",!1),ns("toggleClass"),us("toggleOnExecute",!0),wi("onToggled"),ms("aria",{mode:"none"},jn("mode",{pressed:[us("syncWithExpanded",!1),Oi("update",((e,t,o)=>{vt(e.element,"aria-pressed",o),t.syncWithExpanded&&Xp(e,0,o)}))],checked:[Oi("update",((e,t,o)=>{vt(e.element,"aria-checked",o)}))],expanded:[Oi("update",Xp)],selected:[Oi("update",((e,t,o)=>{vt(e.element,"aria-selected",o)}))],none:[Oi("update",b)]}))];const Yp=hl({fields:Jp,name:"toggling",active:qp,apis:$p,state:(!1,{init:()=>{const e=xs(false);return{get:()=>e.get(),set:t=>e.set(t),clear:()=>e.set(false),readState:()=>e.get()}}})});const Kp=()=>{const e=(e,t)=>{t.stop(),_r(e)};return[Fr($s(),e),Fr(rr(),e),Hr(As()),Hr(Vs())]},Zp=e=>Br(q([e.map((e=>jr(((t,o)=>{e(t),o.stop()})))).toArray(),Kp()])),Qp="alloy.item-hover",eh="alloy.item-focus",th="alloy.item-toggled",oh=e=>{(kl(e.element).isNone()||Wp.isFocused(e))&&(Wp.isFocused(e)||Wp.focus(e),Or(e,Qp,{item:e}))},nh=e=>{Or(e,eh,{item:e})},sh=y(Qp),rh=y(eh),ah=y(th),ih=e=>e.toggling.map((e=>e.exclusive?"menuitemradio":"menuitemcheckbox")).getOr("menuitem"),lh=[Xn("data"),Xn("components"),Xn("dom"),us("hasSubmenu",!1),ns("toggling"),au("itemBehaviours",[Yp,Wp,Tp,ou]),us("ignoreFocus",!1),us("domModification",{}),Oi("builder",(e=>({dom:e.dom,domModification:{...e.domModification,attributes:{role:ih(e),...e.domModification.attributes,"aria-haspopup":e.hasSubmenu,...e.hasSubmenu?{"aria-expanded":!1}:{}}},behaviours:iu(e.itemBehaviours,[e.toggling.fold(Yp.revoke,(e=>Yp.config((e=>({aria:{mode:"checked"},...ge(e,((e,t)=>"exclusive"!==t)),onToggled:(t,o)=>{p(e.onToggled)&&e.onToggled(t,o),((e,t)=>{Or(e,th,{item:e,state:t})})(t,o)}}))(e)))),Wp.config({ignore:e.ignoreFocus,stopMousedown:e.ignoreFocus,onFocus:e=>{nh(e)}}),Tp.config({mode:"execution"}),ou.config({store:{mode:"memory",initialValue:e.data}}),zp("item-type-events",[...Kp(),Fr(Ps(),oh),Fr(sr(),Wp.focus)])]),components:e.components,eventOrder:e.eventOrder}))),us("eventOrder",{})],ch=[Xn("dom"),Xn("components"),Oi("builder",(e=>({dom:e.dom,components:e.components,events:Br([Pr(sr())])})))],dh=y("item-widget"),uh=y([Bu({name:"widget",overrides:e=>({behaviours:gl([ou.config({store:{mode:"manual",getValue:t=>e.data,setValue:b}})])})})]),mh=[Xn("uid"),Xn("data"),Xn("components"),Xn("dom"),us("autofocus",!1),us("ignoreFocus",!1),au("widgetBehaviours",[ou,Wp,Tp]),us("domModification",{}),Ku(uh()),Oi("builder",(e=>{const t=Wu(dh(),e,uh()),o=Uu(dh(),e,t.internals()),n=t=>ju(t,e,"widget").map((e=>(Tp.focusIn(e),e))),s=(t,o)=>am(o.event.target)?M.none():e.autofocus?(o.setSource(t.element),M.none()):M.none();return{dom:e.dom,components:o,domModification:e.domModification,events:Br([jr(((e,t)=>{n(e).each((e=>{t.stop()}))})),Fr(Ps(),oh),Fr(sr(),((t,o)=>{e.autofocus?n(t):Wp.focus(t)}))]),behaviours:iu(e.widgetBehaviours,[ou.config({store:{mode:"memory",initialValue:e.data}}),Wp.config({ignore:e.ignoreFocus,onFocus:e=>{nh(e)}}),Tp.config({mode:"special",focusIn:e.autofocus?e=>{n(e)}:vl(),onLeft:s,onRight:s,onEscape:(t,o)=>Wp.isFocused(t)||e.autofocus?e.autofocus?(o.setSource(t.element),M.none()):M.none():(Wp.focus(t),M.some(!0))})])}}))],gh=jn("type",{widget:mh,item:lh,separator:ch}),ph=y([Fu({factory:{sketch:e=>{const t=Wn("menu.spec item",gh,e);return t.builder(t)}},name:"items",unit:"item",defaults:(e,t)=>ve(t,"uid")?t:{...t,uid:aa("item")},overrides:(e,t)=>({type:t.type,ignoreFocus:e.fakeFocus,domModification:{classes:[e.markers.item]}})})]),hh=y([Xn("value"),Xn("items"),Xn("dom"),Xn("components"),us("eventOrder",{}),nu("menuBehaviours",[Fm,ou,cm,Tp]),ms("movement",{mode:"menu",moveOnTab:!0},jn("mode",{grid:[Ti(),Oi("config",((e,t)=>({mode:"flatgrid",selector:"."+e.markers.item,initSize:{numColumns:t.initSize.numColumns,numRows:t.initSize.numRows},focusManager:e.focusManager})))],matrix:[Oi("config",((e,t)=>({mode:"matrix",selectors:{row:t.rowSelector,cell:"."+e.markers.item},focusManager:e.focusManager}))),Xn("rowSelector")],menu:[us("moveOnTab",!0),Oi("config",((e,t)=>({mode:"menu",selector:"."+e.markers.item,moveOnTab:t.moveOnTab,focusManager:e.focusManager})))]})),Jn("markers",fi()),us("fakeFocus",!1),us("focusManager",eg()),wi("onHighlight"),wi("onDehighlight")]),fh=y("alloy.menu-focus"),bh=rm({name:"Menu",configFields:hh(),partFields:ph(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,markers:e.markers,behaviours:ru(e.menuBehaviours,[Fm.config({highlightClass:e.markers.selectedItem,itemClass:e.markers.item,onHighlight:e.onHighlight,onDehighlight:e.onDehighlight}),ou.config({store:{mode:"memory",initialValue:e.value}}),cm.config({find:M.some}),Tp.config(e.movement.config(e,e.movement))]),events:Br([Fr(rh(),((e,t)=>{const o=t.event;e.getSystem().getByDom(o.target).each((o=>{Fm.highlight(e,o),t.stop(),Or(e,fh(),{menu:e,item:o})}))})),Fr(sh(),((e,t)=>{const o=t.event.item;Fm.highlight(e,o)})),Fr(ah(),((e,t)=>{const{item:o,state:n}=t.event;n&&"menuitemradio"===xt(o.element,"role")&&((e,t)=>{const o=Rc(e.element,'[role="menuitemradio"][aria-checked="true"]');N(o,(o=>{Xe(o,t.element)||e.getSystem().getByDom(o).each((e=>{Yp.off(e)}))}))})(e,o)}))]),components:t,eventOrder:e.eventOrder,domModification:{attributes:{role:"menu"}}})}),vh=(e,t,o,n)=>be(o,n).bind((n=>be(e,n).bind((n=>{const s=vh(e,t,o,n);return M.some([n].concat(s))})))).getOr([]),yh=e=>"prepared"===e.type?M.some(e.menu):M.none(),xh=()=>{const e=xs({}),t=xs({}),o=xs({}),n=Wl(),s=xs({}),r=e=>a(e).bind(yh),a=e=>be(t.get(),e),i=t=>be(e.get(),t);return{setMenuBuilt:(e,o)=>{t.set({...t.get(),[e]:{type:"prepared",menu:o}})},setContents:(r,a,i,l)=>{n.set(r),e.set(i),t.set(a),s.set(l);const c=((e,t)=>{const o={};le(e,((e,t)=>{N(e,(e=>{o[e]=t}))}));const n=t,s=de(t,((e,t)=>({k:e,v:t}))),r=ce(s,((e,t)=>[t].concat(vh(o,n,s,t))));return ce(o,(e=>be(r,e).getOr([e])))})(l,i);o.set(c)},expand:t=>be(e.get(),t).map((e=>{const n=be(o.get(),t).getOr([]);return[e].concat(n)})),refresh:e=>be(o.get(),e),collapse:e=>be(o.get(),e).bind((e=>e.length>1?M.some(e.slice(1)):M.none())),lookupMenu:a,lookupItem:i,otherMenus:e=>{const t=s.get();return K(ae(t),e)},getPrimary:()=>n.get().bind(r),getMenus:()=>t.get(),clear:()=>{e.set({}),t.set({}),o.set({}),n.clear()},isClear:()=>n.get().isNone(),getTriggeringPath:(t,s)=>{const a=W(i(t).toArray(),(e=>r(e).isSome()));return be(o.get(),t).bind((t=>{const o=Y(a.concat(t));return(e=>{const t=[];for(let o=0;o<e.length;o++){const n=e[o];if(!n.isSome())return M.none();t.push(n.getOrDie())}return M.some(t)})(X(o,((t,a)=>((t,o,n)=>r(t).bind((s=>(t=>he(e.get(),((e,o)=>e===t)))(t).bind((e=>o(e).map((e=>({triggeredMenu:s,triggeringItem:e,triggeringPath:n}))))))))(t,s,o.slice(0,a+1)).fold((()=>xe(n.get(),t)?[]:[M.none()]),(e=>[M.some(e)])))))}))}}},wh=yh,Sh=Qr("tiered-menu-item-highlight"),Ch=Qr("tiered-menu-item-dehighlight");var kh;!function(e){e[e.HighlightMenuAndItem=0]="HighlightMenuAndItem",e[e.HighlightJustMenu=1]="HighlightJustMenu",e[e.HighlightNone=2]="HighlightNone"}(kh||(kh={}));const Oh=y("collapse-item"),_h=sm({name:"TieredMenu",configFields:[ki("onExecute"),ki("onEscape"),Ci("onOpenMenu"),Ci("onOpenSubmenu"),wi("onRepositionMenu"),wi("onCollapseMenu"),us("highlightOnOpen",kh.HighlightMenuAndItem),es("data",[Xn("primary"),Xn("menus"),Xn("expansions")]),us("fakeFocus",!1),wi("onHighlightItem"),wi("onDehighlightItem"),wi("onHover"),vi(),Xn("dom"),us("navigateOnHover",!0),us("stayInDom",!1),nu("tmenuBehaviours",[Tp,Fm,cm,Rp]),us("eventOrder",{})],apis:{collapseMenu:(e,t)=>{e.collapseMenu(t)},highlightPrimary:(e,t)=>{e.highlightPrimary(t)},repositionMenus:(e,t)=>{e.repositionMenus(t)}},factory:(e,t)=>{const o=Wl(),n=xh(),s=e=>ou.getValue(e).value,r=t=>ce(e.data.menus,((e,t)=>X(e.items,(e=>"separator"===e.type?[]:[e.data.value])))),a=Fm.highlight,i=(t,o)=>{a(t,o),Fm.getHighlighted(o).orThunk((()=>Fm.getFirst(o))).each((n=>{e.fakeFocus?Fm.highlight(o,n):Tr(t,n.element,sr())}))},l=(e,t)=>we(P(t,(t=>e.lookupMenu(t).bind((e=>"prepared"===e.type?M.some(e.menu):M.none()))))),c=(t,o,n)=>{const s=l(o,o.otherMenus(n));N(s,(o=>{Va(o.element,[e.markers.backgroundMenu]),e.stayInDom||Rp.remove(t,o)}))},d=(t,n)=>{const r=(t=>o.get().getOrThunk((()=>{const n={},r=Rc(t.element,`.${e.markers.item}`),a=W(r,(e=>"true"===xt(e,"aria-haspopup")));return N(a,(e=>{t.getSystem().getByDom(e).each((e=>{const t=s(e);n[t]=e}))})),o.set(n),n})))(t);le(r,((e,t)=>{const o=V(n,t);vt(e.element,"aria-expanded",o)}))},u=(t,o,n)=>M.from(n[0]).bind((s=>o.lookupMenu(s).bind((s=>{if("notbuilt"===s.type)return M.none();{const r=s.menu,a=l(o,n.slice(1));return N(a,(t=>{Aa(t.element,e.markers.backgroundMenu)})),pt(r.element)||Rp.append(t,Ya(r)),Va(r.element,[e.markers.backgroundMenu]),i(t,r),c(t,o,n),M.some(r)}}))));let m;!function(e){e[e.HighlightSubmenu=0]="HighlightSubmenu",e[e.HighlightParent=1]="HighlightParent"}(m||(m={}));const g=(t,o,r=m.HighlightSubmenu)=>{if(o.hasConfigured(Cm)&&Cm.isDisabled(o))return M.some(o);{const a=s(o);return n.expand(a).bind((s=>(d(t,s),M.from(s[0]).bind((a=>n.lookupMenu(a).bind((i=>{const l=((e,t,o)=>{if("notbuilt"===o.type){const s=e.getSystem().build(o.nbMenu());return n.setMenuBuilt(t,s),s}return o.menu})(t,a,i);return pt(l.element)||Rp.append(t,Ya(l)),e.onOpenSubmenu(t,o,l,Y(s)),r===m.HighlightSubmenu?(Fm.highlightFirst(l),u(t,n,s)):(Fm.dehighlightAll(l),M.some(o))})))))))}},p=(t,o)=>{const r=s(o);return n.collapse(r).bind((s=>(d(t,s),u(t,n,s).map((n=>(e.onCollapseMenu(t,o,n),n))))))},h=t=>(o,n)=>ri(n.getSource(),`.${e.markers.item}`).bind((e=>o.getSystem().getByDom(e).toOptional().bind((e=>t(o,e).map(T))))),f=Br([Fr(fh(),((e,t)=>{const o=t.event.item;n.lookupItem(s(o)).each((()=>{const o=t.event.menu;Fm.highlight(e,o);const r=s(t.event.item);n.refresh(r).each((t=>c(e,n,t)))}))})),jr(((t,o)=>{const n=o.event.target;t.getSystem().getByDom(n).each((o=>{0===s(o).indexOf("collapse-item")&&p(t,o),g(t,o,m.HighlightSubmenu).fold((()=>{e.onExecute(t,o)}),b)}))})),Lr(((t,o)=>{(t=>{const o=((t,o,n)=>ce(n,((n,s)=>{const r=()=>bh.sketch({...n,value:s,markers:e.markers,fakeFocus:e.fakeFocus,onHighlight:(e,t)=>{Or(e,Sh,{menuComp:e,itemComp:t})},onDehighlight:(e,t)=>{Or(e,Ch,{menuComp:e,itemComp:t})},focusManager:e.fakeFocus?tg():eg()});return s===o?{type:"prepared",menu:t.getSystem().build(r())}:{type:"notbuilt",nbMenu:r}})))(t,e.data.primary,e.data.menus),s=r();return n.setContents(e.data.primary,o,e.data.expansions,s),n.getPrimary()})(t).each((o=>{Rp.append(t,Ya(o)),e.onOpenMenu(t,o),e.highlightOnOpen===kh.HighlightMenuAndItem?i(t,o):e.highlightOnOpen===kh.HighlightJustMenu&&a(t,o)}))})),Fr(Sh,((t,o)=>{e.onHighlightItem(t,o.event.menuComp,o.event.itemComp)})),Fr(Ch,((t,o)=>{e.onDehighlightItem(t,o.event.menuComp,o.event.itemComp)})),...e.navigateOnHover?[Fr(sh(),((t,o)=>{const r=o.event.item;((e,t)=>{const o=s(t);n.refresh(o).bind((t=>(d(e,t),u(e,n,t))))})(t,r),g(t,r,m.HighlightParent),e.onHover(t,r)}))]:[]]),v=e=>Fm.getHighlighted(e).bind(Fm.getHighlighted),y={collapseMenu:e=>{v(e).each((t=>{p(e,t)}))},highlightPrimary:e=>{n.getPrimary().each((t=>{i(e,t)}))},repositionMenus:t=>{const o=n.getPrimary().bind((e=>v(t).bind((e=>{const t=s(e),o=fe(n.getMenus()),r=we(P(o,wh));return n.getTriggeringPath(t,(e=>((e,t,o)=>re(t,(e=>{if(!e.getSystem().isConnected())return M.none();const t=Fm.getCandidates(e);return G(t,(e=>s(e)===o))})))(0,r,e)))})).map((t=>({primary:e,triggeringPath:t})))));o.fold((()=>{(e=>M.from(e.components()[0]).filter((e=>"menu"===xt(e.element,"role"))))(t).each((o=>{e.onRepositionMenu(t,o,[])}))}),(({primary:o,triggeringPath:n})=>{e.onRepositionMenu(t,o,n)}))}};return{uid:e.uid,dom:e.dom,markers:e.markers,behaviours:ru(e.tmenuBehaviours,[Tp.config({mode:"special",onRight:h(((e,t)=>am(t.element)?M.none():g(e,t,m.HighlightSubmenu))),onLeft:h(((e,t)=>am(t.element)?M.none():p(e,t))),onEscape:h(((t,o)=>p(t,o).orThunk((()=>e.onEscape(t,o).map((()=>t)))))),focusIn:(e,t)=>{n.getPrimary().each((t=>{Tr(e,t.element,sr())}))}}),Fm.config({highlightClass:e.markers.selectedMenu,itemClass:e.markers.menu}),cm.config({find:e=>Fm.getHighlighted(e)}),Rp.config({})]),eventOrder:e.eventOrder,apis:y,events:f}},extraApis:{tieredData:(e,t,o)=>({primary:e,menus:t,expansions:o}),singleData:(e,t)=>({primary:e,menus:Ss(e,t),expansions:{}}),collapseItem:e=>({value:Qr(Oh()),meta:{text:e}})}}),Th=sm({name:"InlineView",configFields:[Xn("lazySink"),wi("onShow"),wi("onHide"),ls("onEscape"),nu("inlineBehaviours",[Rd,ou,yl]),ds("fireDismissalEventInstead",[us("event",fr())]),ds("fireRepositionEventInstead",[us("event",br())]),us("getRelated",M.none),us("isExtraPart",_),us("eventOrder",M.none)],factory:(e,t)=>{const o=(e,t,o,s)=>{n(e,t,o,(()=>s.map((e=>$o(e)))))},n=(t,o,n,s)=>{const r=e.lazySink(t).getOrDie();Rd.openWhileCloaked(t,o,(()=>ud.positionWithinBounds(r,t,n,s()))),ou.setValue(t,M.some({mode:"position",config:n,getBounds:s}))},s=(t,o,n,s)=>{const r=((e,t,o,n,s)=>{const r=()=>e.lazySink(t),a="horizontal"===n.type?{layouts:{onLtr:()=>al(),onRtl:()=>il()}}:{},i=e=>(e=>2===e.length)(e)?a:{};return _h.sketch({dom:{tag:"div"},data:n.data,markers:n.menu.markers,highlightOnOpen:n.menu.highlightOnOpen,fakeFocus:n.menu.fakeFocus,onEscape:()=>(Rd.close(t),e.onEscape.map((e=>e(t))),M.some(!0)),onExecute:()=>M.some(!0),onOpenMenu:(e,t)=>{ud.positionWithinBounds(r().getOrDie(),t,o,s())},onOpenSubmenu:(e,t,o,n)=>{const s=r().getOrDie();ud.position(s,o,{anchor:{type:"submenu",item:t,...i(n)}})},onRepositionMenu:(e,t,n)=>{const a=r().getOrDie();ud.positionWithinBounds(a,t,o,s()),N(n,(e=>{const t=i(e.triggeringPath);ud.position(a,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem,...t}})}))}})})(e,t,o,n,s);Rd.open(t,r),ou.setValue(t,M.some({mode:"menu",menu:r}))},r=t=>{Rd.isOpen(t)&&ou.getValue(t).each((o=>{switch(o.mode){case"menu":Rd.getState(t).each(_h.repositionMenus);break;case"position":const n=e.lazySink(t).getOrDie();ud.positionWithinBounds(n,t,o.config,o.getBounds())}}))},a={setContent:(e,t)=>{Rd.setContent(e,t)},showAt:(e,t,n)=>{o(e,t,n,M.none())},showWithin:o,showWithinBounds:n,showMenuAt:(e,t,o)=>{s(e,t,o,M.none)},showMenuWithinBounds:s,hide:e=>{Rd.isOpen(e)&&(ou.setValue(e,M.none()),Rd.close(e))},getContent:e=>Rd.getState(e),reposition:r,isOpen:Rd.isOpen};return{uid:e.uid,dom:e.dom,behaviours:ru(e.inlineBehaviours,[Rd.config({isPartOf:(t,o,n)=>li(o,n)||((t,o)=>e.getRelated(t).exists((e=>li(e,o))))(t,n),getAttachPoint:t=>e.lazySink(t).getOrDie(),onOpen:t=>{e.onShow(t)},onClose:t=>{e.onHide(t)}}),ou.config({store:{mode:"memory",initialValue:M.none()}}),yl.config({channels:{...Ld({isExtraPart:t.isExtraPart,...e.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...Ud({...e.fireRepositionEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({}),doReposition:r})}})]),eventOrder:e.eventOrder,apis:a}},apis:{showAt:(e,t,o,n)=>{e.showAt(t,o,n)},showWithin:(e,t,o,n,s)=>{e.showWithin(t,o,n,s)},showWithinBounds:(e,t,o,n,s)=>{e.showWithinBounds(t,o,n,s)},showMenuAt:(e,t,o,n)=>{e.showMenuAt(t,o,n)},showMenuWithinBounds:(e,t,o,n,s)=>{e.showMenuWithinBounds(t,o,n,s)},hide:(e,t)=>{e.hide(t)},isOpen:(e,t)=>e.isOpen(t),getContent:(e,t)=>e.getContent(t),setContent:(e,t,o)=>{e.setContent(t,o)},reposition:(e,t)=>{e.reposition(t)}}});var Eh=tinymce.util.Tools.resolve("tinymce.util.Delay");const Mh=sm({name:"Button",factory:e=>{const t=Zp(e.action),o=e.dom.tag,n=t=>be(e.dom,"attributes").bind((e=>be(e,t)));return{uid:e.uid,dom:e.dom,components:e.components,events:t,behaviours:iu(e.buttonBehaviours,[Wp.config({}),Tp.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:"button"===o?{type:n("type").getOr("button"),...n("role").map((e=>({role:e}))).getOr({})}:{role:n("role").getOr("button")}},eventOrder:e.eventOrder}},configFields:[us("uid",void 0),Xn("dom"),us("components",[]),au("buttonBehaviours",[Wp,Tp]),ns("action"),ns("role"),us("eventOrder",{})]}),Bh=e=>{const t=(e=>void 0!==e.uid)(e)&&ye(e,"uid")?e.uid:aa("memento");return{get:e=>e.getSystem().getByUid(t).getOrDie(),getOpt:e=>e.getSystem().getByUid(t).toOptional(),asSpec:()=>({...e,uid:t})}};var Ah=tinymce.util.Tools.resolve("tinymce.util.I18n");const Dh={indent:!0,outdent:!0,"table-insert-column-after":!0,"table-insert-column-before":!0,"paste-column-after":!0,"paste-column-before":!0,"unordered-list":!0,"list-bull-circle":!0,"list-bull-default":!0,"list-bull-square":!0},Fh="temporary-placeholder",Ih=e=>()=>be(e,Fh).getOr("!not found!"),Vh=(e,t)=>{const o=e.toLowerCase();if(Ah.isRtl()){const e=((e,t)=>_e(e,t)?e:((e,t)=>e+"-rtl")(e))(o,"-rtl");return ve(t,e)?e:o}return o},Rh=(e,t)=>be(t,Vh(e,t)),zh=(e,t)=>{const o=t();return Rh(e,o).getOrThunk(Ih(o))},Hh=()=>zp("add-focusable",[Lr((e=>{ni(e.element,"svg").each((e=>vt(e,"focusable","false")))}))]),Ph=(e,t,o,n)=>{var s,r;const a=(e=>!!Ah.isRtl()&&ve(Dh,e))(t)?["tox-icon--flip"]:[],i=be(o,Vh(t,o)).or(n).getOrThunk(Ih(o));return{dom:{tag:e.tag,attributes:null!==(s=e.attributes)&&void 0!==s?s:{},classes:e.classes.concat(a),innerHtml:i},behaviours:gl([...null!==(r=e.behaviours)&&void 0!==r?r:[],Hh()])}},Nh=(e,t,o,n=M.none())=>Ph(t,e,o(),n),Lh={success:"checkmark",error:"warning",err:"error",warning:"warning",warn:"warning",info:"info"},Wh=sm({name:"Notification",factory:e=>{const t=Bh({dom:{tag:"p",innerHtml:e.translationProvider(e.text)},behaviours:gl([Rp.config({})])}),o=e=>({dom:{tag:"div",classes:["tox-bar"],styles:{width:`${e}%`}}}),n=e=>({dom:{tag:"div",classes:["tox-text"],innerHtml:`${e}%`}}),s=Bh({dom:{tag:"div",classes:e.progress?["tox-progress-bar","tox-progress-indicator"]:["tox-progress-bar"]},components:[{dom:{tag:"div",classes:["tox-bar-container"]},components:[o(0)]},n(0)],behaviours:gl([Rp.config({})])}),r={updateProgress:(e,t)=>{e.getSystem().isConnected()&&s.getOpt(e).each((e=>{Rp.set(e,[{dom:{tag:"div",classes:["tox-bar-container"]},components:[o(t)]},n(t)])}))},updateText:(e,o)=>{if(e.getSystem().isConnected()){const n=t.get(e);Rp.set(n,[Ga(o)])}}},a=q([e.icon.toArray(),e.level.toArray(),e.level.bind((e=>M.from(Lh[e]))).toArray()]),i=Bh(Mh.sketch({dom:{tag:"button",classes:["tox-notification__dismiss","tox-button","tox-button--naked","tox-button--icon"]},components:[Nh("close",{tag:"div",classes:["tox-icon"],attributes:{"aria-label":e.translationProvider("Close")}},e.iconProvider)],action:t=>{e.onAction(t)}})),l=((e,t,o)=>{const n=o(),s=G(e,(e=>ve(n,Vh(e,n))));return Ph({tag:"div",classes:["tox-notification__icon"]},s.getOr(Fh),n,M.none())})(a,0,e.iconProvider),c=[l,{dom:{tag:"div",classes:["tox-notification__body"]},components:[t.asSpec()],behaviours:gl([Rp.config({})])}];return{uid:e.uid,dom:{tag:"div",attributes:{role:"alert"},classes:e.level.map((e=>["tox-notification","tox-notification--in",`tox-notification--${e}`])).getOr(["tox-notification","tox-notification--in"])},behaviours:gl([Wp.config({}),zp("notification-events",[Fr(Ns(),(e=>{i.getOpt(e).each(Wp.focus)}))])]),components:c.concat(e.progress?[s.asSpec()]:[]).concat(e.closeButton?[i.asSpec()]:[]),apis:r}},configFields:[ns("level"),Xn("progress"),ns("icon"),Xn("onAction"),Xn("text"),Xn("iconProvider"),Xn("translationProvider"),fs("closeButton",!0)],apis:{updateProgress:(e,t,o)=>{e.updateProgress(t,o)},updateText:(e,t,o)=>{e.updateText(t,o)}}});var Uh,jh,Gh=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),$h=tinymce.util.Tools.resolve("tinymce.EditorManager"),qh=tinymce.util.Tools.resolve("tinymce.Env");!function(e){e.default="wrap",e.floating="floating",e.sliding="sliding",e.scrolling="scrolling"}(Uh||(Uh={})),function(e){e.auto="auto",e.top="top",e.bottom="bottom"}(jh||(jh={}));const Xh=e=>t=>t.options.get(e),Jh=e=>t=>M.from(e(t)),Yh=e=>{const t=qh.deviceType.isPhone(),o=qh.deviceType.isTablet()||t,n=e.options.register,s=e=>r(e)||!1===e,a=e=>r(e)||h(e);n("skin",{processor:e=>r(e)||!1===e,default:"oxide"}),n("skin_url",{processor:"string"}),n("height",{processor:a,default:Math.max(e.getElement().offsetHeight,400)}),n("width",{processor:a,default:Gh.DOM.getStyle(e.getElement(),"width")}),n("min_height",{processor:"number",default:100}),n("min_width",{processor:"number"}),n("max_height",{processor:"number"}),n("max_width",{processor:"number"}),n("style_formats",{processor:"object[]"}),n("style_formats_merge",{processor:"boolean",default:!1}),n("style_formats_autohide",{processor:"boolean",default:!1}),n("line_height_formats",{processor:"string",default:"1 1.1 1.2 1.3 1.4 1.5 2"}),n("font_family_formats",{processor:"string",default:"Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats"}),n("font_size_formats",{processor:"string",default:"8pt 10pt 12pt 14pt 18pt 24pt 36pt"}),n("block_formats",{processor:"string",default:"Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre"}),n("content_langs",{processor:"object[]"}),n("removed_menuitems",{processor:"string",default:""}),n("menubar",{processor:e=>r(e)||d(e),default:!t}),n("menu",{processor:"object",default:{}}),n("toolbar",{processor:e=>d(e)||r(e)||l(e)?{value:e,valid:!0}:{valid:!1,message:"Must be a boolean, string or array."},default:!0}),z(9,(e=>{n("toolbar"+(e+1),{processor:"string"})})),n("toolbar_mode",{processor:"string",default:o?"scrolling":"floating"}),n("toolbar_groups",{processor:"object",default:{}}),n("toolbar_location",{processor:"string",default:jh.auto}),n("toolbar_persist",{processor:"boolean",default:!1}),n("toolbar_sticky",{processor:"boolean",default:e.inline}),n("toolbar_sticky_offset",{processor:"number",default:0}),n("fixed_toolbar_container",{processor:"string",default:""}),n("fixed_toolbar_container_target",{processor:"object"}),n("file_picker_callback",{processor:"function"}),n("file_picker_validator_handler",{processor:"function"}),n("file_picker_types",{processor:"string"}),n("typeahead_urls",{processor:"boolean",default:!0}),n("anchor_top",{processor:s,default:"#top"}),n("anchor_bottom",{processor:s,default:"#bottom"}),n("draggable_modal",{processor:"boolean",default:!1}),n("statusbar",{processor:"boolean",default:!0}),n("elementpath",{processor:"boolean",default:!0}),n("branding",{processor:"boolean",default:!0}),n("promotion",{processor:"boolean",default:!0}),n("resize",{processor:e=>"both"===e||d(e),default:!qh.deviceType.isTouch()}),n("sidebar_show",{processor:"string"})},Kh=Xh("readonly"),Zh=Xh("height"),Qh=Xh("width"),ef=Jh(Xh("min_width")),tf=Jh(Xh("min_height")),of=Jh(Xh("max_width")),nf=Jh(Xh("max_height")),sf=Jh(Xh("style_formats")),rf=Xh("style_formats_merge"),af=Xh("style_formats_autohide"),lf=Xh("content_langs"),cf=Xh("removed_menuitems"),df=Xh("toolbar_mode"),uf=Xh("toolbar_groups"),mf=Xh("toolbar_location"),gf=Xh("fixed_toolbar_container"),pf=Xh("fixed_toolbar_container_target"),hf=Xh("toolbar_persist"),ff=Xh("toolbar_sticky_offset"),bf=Xh("menubar"),vf=Xh("toolbar"),yf=Xh("file_picker_callback"),xf=Xh("file_picker_validator_handler"),wf=Xh("file_picker_types"),Sf=Xh("typeahead_urls"),Cf=Xh("anchor_top"),kf=Xh("anchor_bottom"),Of=Xh("draggable_modal"),_f=Xh("statusbar"),Tf=Xh("elementpath"),Ef=Xh("branding"),Mf=Xh("resize"),Bf=Xh("paste_as_text"),Af=Xh("sidebar_show"),Df=Xh("promotion"),Ff=e=>!1===e.options.get("skin"),If=e=>!1!==e.options.get("menubar"),Vf=e=>{const t=e.options.get("skin_url");if(Ff(e))return t;if(t)return e.documentBaseURI.toAbsolute(t);{const t=e.options.get("skin");return $h.baseURL+"/skins/ui/"+t}},Rf=e=>e.options.get("line_height_formats").split(" "),zf=e=>{const t=vf(e),o=r(t),n=l(t)&&t.length>0;return!Pf(e)&&(n||o||!0===t)},Hf=e=>{const t=z(9,(t=>e.options.get("toolbar"+(t+1)))),o=W(t,r);return Ce(o.length>0,o)},Pf=e=>Hf(e).fold((()=>{const t=vf(e);return f(t,r)&&t.length>0}),T),Nf=e=>mf(e)===jh.bottom,Lf=e=>{var t;if(!e.inline)return M.none();const o=null!==(t=gf(e))&&void 0!==t?t:"";if(o.length>0)return si(ht(),o);const n=pf(e);return g(n)?M.some(Ie(n)):M.none()},Wf=e=>e.inline&&Lf(e).isSome(),Uf=e=>Lf(e).getOrThunk((()=>ut(dt(Ie(e.getElement()))))),jf=e=>e.inline&&!If(e)&&!zf(e)&&!Pf(e),Gf=e=>(e.options.get("toolbar_sticky")||e.inline)&&!Wf(e)&&!jf(e),$f=e=>{const t=e.options.get("menu");return ce(t,(e=>({...e,items:e.items})))};var qf=Object.freeze({__proto__:null,get ToolbarMode(){return Uh},get ToolbarLocation(){return jh},register:Yh,getSkinUrl:Vf,isReadOnly:Kh,isSkinDisabled:Ff,getHeightOption:Zh,getWidthOption:Qh,getMinWidthOption:ef,getMinHeightOption:tf,getMaxWidthOption:of,getMaxHeightOption:nf,getUserStyleFormats:sf,shouldMergeStyleFormats:rf,shouldAutoHideStyleFormats:af,getLineHeightFormats:Rf,getContentLanguages:lf,getRemovedMenuItems:cf,isMenubarEnabled:If,isMultipleToolbars:Pf,isToolbarEnabled:zf,isToolbarPersist:hf,getMultipleToolbarsOption:Hf,getUiContainer:Uf,useFixedContainer:Wf,getToolbarMode:df,isDraggableModal:Of,isDistractionFree:jf,isStickyToolbar:Gf,getStickyToolbarOffset:ff,getToolbarLocation:mf,isToolbarLocationBottom:Nf,getToolbarGroups:uf,getMenus:$f,getMenubar:bf,getToolbar:vf,getFilePickerCallback:yf,getFilePickerTypes:wf,useTypeaheadUrls:Sf,getAnchorTop:Cf,getAnchorBottom:kf,getFilePickerValidatorHandler:xf,useStatusBar:_f,useElementPath:Tf,promotionEnabled:Df,useBranding:Ef,getResize:Mf,getPasteAsText:Bf,getSidebarShow:Af});const Xf="[data-mce-autocompleter]",Jf=e=>ri(e,Xf);var Yf;!function(e){e[e.CLOSE_ON_EXECUTE=0]="CLOSE_ON_EXECUTE",e[e.BUBBLE_TO_SANDBOX=1]="BUBBLE_TO_SANDBOX"}(Yf||(Yf={}));var Kf=Yf;const Zf="tox-menu-nav__js",Qf="tox-collection__item",eb={normal:Zf,color:"tox-swatch"},tb="tox-collection__item--enabled",ob="tox-collection__item-icon",nb="tox-collection__item-label",sb="tox-collection__item-caret",rb="tox-collection__item--active",ab="tox-collection__item-container",ib="tox-collection__item-container--row",lb=e=>be(eb,e).getOr(Zf),cb=e=>"color"===e?"tox-swatches":"tox-menu",db=e=>({backgroundMenu:"tox-background-menu",selectedMenu:"tox-selected-menu",selectedItem:"tox-collection__item--active",hasIcons:"tox-menu--has-icons",menu:cb(e),tieredMenu:"tox-tiered-menu"}),ub=e=>{const t=db(e);return{backgroundMenu:t.backgroundMenu,selectedMenu:t.selectedMenu,menu:t.menu,selectedItem:t.selectedItem,item:lb(e)}},mb=(e,t,o)=>{const n=db(o);return{tag:"div",classes:q([[n.menu,`tox-menu-${t}-column`],e?[n.hasIcons]:[]])}},gb=[bh.parts.items({})],pb=(e,t,o)=>{const n=db(o);return{dom:{tag:"div",classes:q([[n.tieredMenu]])},markers:ub(o)}},hb=y([ns("data"),us("inputAttributes",{}),us("inputStyles",{}),us("tag","input"),us("inputClasses",[]),wi("onSetValue"),us("styles",{}),us("eventOrder",{}),nu("inputBehaviours",[ou,Wp]),us("selectOnFocus",!0)]),fb=e=>gl([Wp.config({onFocus:e.selectOnFocus?e=>{const t=e.element,o=Ra(t);t.dom.setSelectionRange(0,o.length)}:b})]),bb=e=>({...fb(e),...ru(e.inputBehaviours,[ou.config({store:{mode:"manual",...e.data.map((e=>({initialValue:e}))).getOr({}),getValue:e=>Ra(e.element),setValue:(e,t)=>{Ra(e.element)!==t&&za(e.element,t)}},onSetValue:e.onSetValue})])}),vb=e=>({tag:e.tag,attributes:{type:"text",...e.inputAttributes},styles:e.inputStyles,classes:e.inputClasses}),yb=sm({name:"Input",configFields:hb(),factory:(e,t)=>({uid:e.uid,dom:vb(e),components:[],behaviours:bb(e),eventOrder:e.eventOrder})}),xb=Qr("refetch-trigger-event"),wb=Qr("redirect-menu-item-interaction"),Sb=e=>si(e.element,".tox-menu__searcher").bind((t=>e.getSystem().getByDom(t).toOptional())),Cb=Sb,kb=e=>({fetchPattern:ou.getValue(e),selectionStart:e.element.dom.selectionStart,selectionEnd:e.element.dom.selectionEnd}),Ob=e=>{const t=(e,t)=>(t.cut(),M.none()),o=(e,t)=>{const o={interactionEvent:t.event,eventType:t.event.raw.type};return Or(e,wb,o),M.some(!0)},n="searcher-events";return{dom:{tag:"div",classes:[Qf]},components:[yb.sketch({inputClasses:["tox-menu__searcher","tox-textfield"],inputAttributes:{...e.placeholder.map((t=>({placeholder:e.i18n(t)}))).getOr({}),type:"search","aria-autocomplete":"list"},inputBehaviours:gl([zp(n,[Fr(js(),(e=>{kr(e,xb)})),Fr(Ws(),((e,t)=>{"Escape"===t.event.raw.key&&t.stop()}))]),Tp.config({mode:"special",onLeft:t,onRight:t,onSpace:t,onEnter:o,onEscape:o,onUp:o,onDown:o})]),eventOrder:{keydown:[n,Tp.name()]}})]}},_b="tox-collection--results__js",Tb=e=>{var t;return e.dom?{...e,dom:{...e.dom,attributes:{...null!==(t=e.dom.attributes)&&void 0!==t?t:{},id:Qr("aria-item-search-result-id"),"aria-selected":"false"}}}:e},Eb=(e,t)=>o=>{const n=H(o,t);return P(n,(t=>({dom:e,components:t})))},Mb=(e,t)=>{const o=[];let n=[];return N(e,((e,s)=>{t(e,s)?(n.length>0&&o.push(n),n=[],(ve(e.dom,"innerHtml")||e.components&&e.components.length>0)&&n.push(e)):n.push(e)})),n.length>0&&o.push(n),P(o,(e=>({dom:{tag:"div",classes:["tox-collection__group"]},components:e})))},Bb=(e,t,o)=>bh.parts.items({preprocess:n=>{const s=P(n,o);return"auto"!==e&&e>1?Eb({tag:"div",classes:["tox-collection__group"]},e)(s):Mb(s,((e,o)=>"separator"===t[o].type))}}),Ab=(e,t,o=!0)=>({dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===e?["tox-collection--list"]:["tox-collection--grid"])},components:[Bb(e,t,x)]}),Db=e=>R(e,(e=>"icon"in e&&void 0!==e.icon)),Fb=e=>(console.error(Un(e)),console.log(e),M.none()),Ib=(e,t,o,n,s)=>{const r=(a=o,{dom:{tag:"div",classes:["tox-collection","tox-collection--horizontal"]},components:[bh.parts.items({preprocess:e=>Mb(e,((e,t)=>"separator"===a[t].type))})]});var a;return{value:e,dom:r.dom,components:r.components,items:o}},Vb=(e,t,o,n,s)=>{if("color"===s.menuType){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-swatches-menu"]},components:[{dom:{tag:"div",classes:["tox-swatches"]},components:[bh.parts.items({preprocess:"auto"!==e?Eb({tag:"div",classes:["tox-swatches__row"]},e):x})]}]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s.menuType&&"auto"===n){const t=Ab(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s.menuType||"searchable"===s.menuType){const t="searchable"!==s.menuType?Ab(n,o):"search-with-field"===s.searchMode.searchMode?((e,t,o)=>{const n=Qr("aria-controls-search-results");return{dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===e?["tox-collection--list"]:["tox-collection--grid"])},components:[Ob({i18n:Ah.translate,placeholder:o.placeholder}),{dom:{tag:"div",classes:[...1===e?["tox-collection--list"]:["tox-collection--grid"],_b],attributes:{id:n}},components:[Bb(e,t,Tb)]}]}})(n,o,s.searchMode):((e,t,o=!0)=>{const n=Qr("aria-controls-search-results");return{dom:{tag:"div",classes:["tox-menu","tox-collection",_b].concat(1===e?["tox-collection--list"]:["tox-collection--grid"]),attributes:{id:n}},components:[Bb(e,t,Tb)]}})(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("listpreview"===s.menuType&&"auto"!==n){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-collection","tox-collection--toolbar","tox-collection--toolbar-lg"]},components:[bh.parts.items({preprocess:Eb({tag:"div",classes:["tox-collection__group"]},e)})]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}return{value:e,dom:mb(t,n,s.menuType),components:gb,items:o}},Rb=Kn("type"),zb=Kn("name"),Hb=Kn("label"),Pb=Kn("text"),Nb=Kn("title"),Lb=Kn("icon"),Wb=Kn("value"),Ub=Qn("fetch"),jb=Qn("getSubmenuItems"),Gb=Qn("onAction"),$b=Qn("onItemAction"),qb=bs("onSetup",(()=>b)),Xb=as("name"),Jb=as("text"),Yb=as("icon"),Kb=as("tooltip"),Zb=as("label"),Qb=as("shortcut"),ev=ls("select"),tv=fs("active",!1),ov=fs("borderless",!1),nv=fs("enabled",!0),sv=fs("primary",!1),rv=e=>us("columns",e),av=us("meta",{}),iv=bs("onAction",b),lv=e=>ps("type",e),cv=e=>Gn("name","name",un((()=>Qr(`${e}-name`))),Dn),dv=kn([Rb,Jb]),uv=kn([lv("autocompleteitem"),tv,nv,av,Wb,Jb,Yb]),mv=[nv,Kb,Yb,Jb,qb],gv=kn([Rb,Gb].concat(mv)),pv=e=>Nn("toolbarbutton",gv,e),hv=[tv].concat(mv),fv=kn(hv.concat([Rb,Gb])),bv=e=>Nn("ToggleButton",fv,e),vv=[bs("predicate",_),hs("scope","node",["node","editor"]),hs("position","selection",["node","selection","line"])],yv=mv.concat([lv("contextformbutton"),sv,Gb,$n("original",x)]),xv=hv.concat([lv("contextformbutton"),sv,Gb,$n("original",x)]),wv=mv.concat([lv("contextformbutton")]),Sv=hv.concat([lv("contextformtogglebutton")]),Cv=jn("type",{contextformbutton:yv,contextformtogglebutton:xv}),kv=kn([lv("contextform"),bs("initValue",y("")),Zb,os("commands",Cv),ss("launch",jn("type",{contextformbutton:wv,contextformtogglebutton:Sv}))].concat(vv)),Ov=kn([lv("contexttoolbar"),Kn("items")].concat(vv)),_v=[Rb,Kn("src"),as("alt"),vs("classes",[],Dn)],Tv=kn(_v),Ev=[Rb,Pb,Xb,vs("classes",["tox-collection__item-label"],Dn)],Mv=kn(Ev),Bv=wn((()=>zn("type",{cardimage:Tv,cardtext:Mv,cardcontainer:Av}))),Av=kn([Rb,ps("direction","horizontal"),ps("align","left"),ps("valign","middle"),os("items",Bv)]),Dv=[nv,Jb,Qb,("menuitem",Gn("value","value",un((()=>Qr("menuitem-value"))),Mn())),av];const Fv=kn([Rb,Zb,os("items",Bv),qb,iv].concat(Dv)),Iv=kn([Rb,tv,Yb].concat(Dv)),Vv=[Rb,Kn("fancytype"),iv],Rv=[us("initData",{})].concat(Vv),zv=[ys("initData",{},[fs("allowCustomColors",!0),cs("colors",Mn())])].concat(Vv),Hv=jn("fancytype",{inserttable:Rv,colorswatch:zv}),Pv=kn([Rb,qb,iv,Yb].concat(Dv)),Nv=kn([Rb,jb,qb,Yb].concat(Dv)),Lv=kn([Rb,Yb,tv,qb,Gb].concat(Dv)),Wv=(e,t,o)=>{const n=Rc(e.element,"."+o);if(n.length>0){const e=$(n,(e=>{const o=e.dom.getBoundingClientRect().top,s=n[0].dom.getBoundingClientRect().top;return Math.abs(o-s)>t})).getOr(n.length);return M.some({numColumns:e,numRows:Math.ceil(n.length/e)})}return M.none()},Uv=e=>((e,t)=>gl([zp(e,t)]))(Qr("unnamed-events"),e),jv=Qr("tooltip.exclusive"),Gv=Qr("tooltip.show"),$v=Qr("tooltip.hide"),qv=(e,t,o)=>{e.getSystem().broadcastOn([jv],{})};var Xv=Object.freeze({__proto__:null,hideAllExclusive:qv,setComponents:(e,t,o,n)=>{o.getTooltip().each((e=>{e.getSystem().isConnected()&&Rp.set(e,n)}))}}),Jv=Object.freeze({__proto__:null,events:(e,t)=>{const o=o=>{t.getTooltip().each((n=>{wd(n),e.onHide(o,n),t.clearTooltip()})),t.clearTimer()};return Br(q([[Fr(Gv,(o=>{t.resetTimer((()=>{(o=>{if(!t.isShowing()){qv(o);const n=e.lazySink(o).getOrDie(),s=o.getSystem().build({dom:e.tooltipDom,components:e.tooltipComponents,events:Br("normal"===e.mode?[Fr(Ps(),(e=>{kr(o,Gv)})),Fr(zs(),(e=>{kr(o,$v)}))]:[]),behaviours:gl([Rp.config({})])});t.setTooltip(s),vd(n,s),e.onShow(o,s),ud.position(n,s,{anchor:e.anchor(o)})}})(o)}),e.delay)})),Fr($v,(n=>{t.resetTimer((()=>{o(n)}),e.delay)})),Fr(or(),((e,t)=>{const n=t;n.universal||V(n.channels,jv)&&o(e)})),Wr((e=>{o(e)}))],"normal"===e.mode?[Fr(Ns(),(e=>{kr(e,Gv)})),Fr(er(),(e=>{kr(e,$v)})),Fr(Ps(),(e=>{kr(e,Gv)})),Fr(zs(),(e=>{kr(e,$v)}))]:[Fr(Sr(),((e,t)=>{kr(e,Gv)})),Fr(Cr(),(e=>{kr(e,$v)}))]]))}}),Yv=[Xn("lazySink"),Xn("tooltipDom"),us("exclusive",!0),us("tooltipComponents",[]),us("delay",300),hs("mode","normal",["normal","follow-highlight"]),us("anchor",(e=>({type:"hotspot",hotspot:e,layouts:{onLtr:y([Qi,Zi,Xi,Yi,Ji,Ki]),onRtl:y([Qi,Zi,Xi,Yi,Ji,Ki])}}))),wi("onHide"),wi("onShow")];const Kv=hl({fields:Yv,name:"tooltipping",active:Jv,state:Object.freeze({__proto__:null,init:()=>{const e=Wl(),t=Wl(),o=()=>{e.on(clearTimeout)},n=y("not-implemented");return ba({getTooltip:t.get,isShowing:t.isSet,setTooltip:t.set,clearTooltip:t.clear,clearTimer:o,resetTimer:(t,n)=>{o(),e.set(setTimeout(t,n))},readState:n})}}),apis:Xv}),Zv="silver.readonly",Qv=kn([("readonly",Jn("readonly",Fn))]);const ey=(e,t)=>{const o=e.outerContainer.element;t&&(e.mothership.broadcastOn([zd()],{target:o}),e.uiMothership.broadcastOn([zd()],{target:o})),e.mothership.broadcastOn([Zv],{readonly:t}),e.uiMothership.broadcastOn([Zv],{readonly:t})},ty=(e,t)=>{e.on("init",(()=>{e.mode.isReadOnly()&&ey(t,!0)})),e.on("SwitchMode",(()=>ey(t,e.mode.isReadOnly()))),Kh(e)&&e.mode.set("readonly")},oy=()=>yl.config({channels:{[Zv]:{schema:Qv,onReceive:(e,t)=>{Cm.set(e,t.readonly)}}}}),ny=e=>Cm.config({disabled:e}),sy=e=>Cm.config({disabled:e,disableClass:"tox-tbtn--disabled"}),ry=e=>Cm.config({disabled:e,disableClass:"tox-tbtn--disabled",useNative:!1}),ay=(e,t)=>{const o=e.getApi(t);return e=>{e(o)}},iy=(e,t)=>Lr((o=>{ay(e,o)((o=>{const n=e.onSetup(o);p(n)&&t.set(n)}))})),ly=(e,t)=>Wr((o=>ay(e,o)(t.get()))),cy=(e,t)=>jr(((o,n)=>{ay(e,o)(e.onAction),e.triggersSubmenu||t!==Kf.CLOSE_ON_EXECUTE||(o.getSystem().isConnected()&&kr(o,ir()),n.stop())})),dy={[nr()]:["disabling","alloy.base.behaviour","toggling","item-events"]},uy=we,my=(e,t,o,n)=>{const s=xs(b);return{type:"item",dom:t.dom,components:uy(t.optComponents),data:e.data,eventOrder:dy,hasSubmenu:e.triggersSubmenu,itemBehaviours:gl([zp("item-events",[cy(e,o),iy(e,s),ly(e,s)]),(r=()=>!e.enabled||n.isDisabled(),Cm.config({disabled:r,disableClass:"tox-collection__item--state-disabled"})),oy(),Rp.config({})].concat(e.itemBehaviours))};var r},gy=e=>({value:e.value,meta:{text:e.text.getOr(""),...e.meta}}),py=e=>{const t=qh.os.isMacOS()||qh.os.isiOS(),o=t?{alt:"\u2325",ctrl:"\u2303",shift:"\u21e7",meta:"\u2318",access:"\u2303\u2325"}:{meta:"Ctrl",access:"Shift+Alt"},n=e.split("+"),s=P(n,(e=>{const t=e.toLowerCase().trim();return ve(o,t)?o[t]:e}));return t?s.join(""):s.join("+")},hy=(e,t,o=[ob])=>Nh(e,{tag:"div",classes:o},t),fy=e=>({dom:{tag:"div",classes:[nb]},components:[Ga(Ah.translate(e))]}),by=(e,t)=>({dom:{tag:"div",classes:t,innerHtml:e}}),vy=(e,t)=>({dom:{tag:"div",classes:[nb]},components:[{dom:{tag:e.tag,styles:e.styles},components:[Ga(Ah.translate(t))]}]}),yy=e=>({dom:{tag:"div",classes:["tox-collection__item-accessory"]},components:[Ga(py(e))]}),xy=e=>hy("checkmark",e,["tox-collection__item-checkmark"]),wy=e=>{const t=e.map((e=>({attributes:{title:Ah.translate(e)}}))).getOr({});return{tag:"div",classes:[Zf,Qf],...t}},Sy=(e,t,o,n=M.none())=>"color"===e.presets?((e,t,o)=>{const n=e.ariaLabel,s=e.value,r=e.iconContent.map((e=>((e,t,o)=>{const n=t();return Rh(e,n).or(o).getOrThunk(Ih(n))})(e,t.icons,o)));return{dom:(()=>{const e=r.getOr(""),o=n.map((e=>({title:t.translate(e)}))).getOr({}),a={tag:"div",attributes:o,classes:["tox-swatch"]};return"custom"===s?{...a,tag:"button",classes:[...a.classes,"tox-swatches__picker-btn"],innerHtml:e}:"remove"===s?{...a,classes:[...a.classes,"tox-swatch--remove"],innerHtml:e}:g(s)?{...a,attributes:{...a.attributes,"data-mce-color":s},styles:{"background-color":s}}:a})(),optComponents:[]}})(e,t,n):((e,t,o,n)=>{const s={tag:"div",classes:[ob]},r=o?e.iconContent.map((e=>Nh(e,s,t.icons,n))).orThunk((()=>M.some({dom:s}))):M.none(),a=e.checkMark,i=M.from(e.meta).fold((()=>fy),(e=>ve(e,"style")?S(vy,e.style):fy)),l=e.htmlContent.fold((()=>e.textContent.map(i)),(e=>M.some(by(e,[nb]))));return{dom:wy(e.ariaLabel),optComponents:[r,l,e.shortcutContent.map(yy),a,e.caret]}})(e,t,o,n),Cy=(e,t)=>be(e,"tooltipWorker").map((e=>[Kv.config({lazySink:t.getSink,tooltipDom:{tag:"div",classes:["tox-tooltip-worker-container"]},tooltipComponents:[],anchor:e=>({type:"submenu",item:e,overrides:{maxHeightFunction:Zl}}),mode:"follow-highlight",onShow:(t,o)=>{e((e=>{Kv.setComponents(t,[$a({element:Ie(e)})])}))}})])).getOr([]),ky=(e,t)=>{const o=(e=>Gh.DOM.encode(e))(Ah.translate(e));if(t.length>0){const e=new RegExp((e=>e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"))(t),"gi");return o.replace(e,(e=>`<span class="tox-autocompleter-highlight">${e}</span>`))}return o},Oy=(e,t)=>P(e,(e=>{switch(e.type){case"cardcontainer":return((e,t)=>{const o="vertical"===e.direction?"tox-collection__item-container--column":ib,n="left"===e.align?"tox-collection__item-container--align-left":"tox-collection__item-container--align-right";return{dom:{tag:"div",classes:[ab,o,n,(()=>{switch(e.valign){case"top":return"tox-collection__item-container--valign-top";case"middle":return"tox-collection__item-container--valign-middle";case"bottom":return"tox-collection__item-container--valign-bottom"}})()]},components:t}})(e,Oy(e.items,t));case"cardimage":return((e,t,o)=>({dom:{tag:"img",classes:t,attributes:{src:e,alt:o.getOr("")}}}))(e.src,e.classes,e.alt);case"cardtext":const o=e.name.exists((e=>V(t.cardText.highlightOn,e))),n=o?M.from(t.cardText.matchText).getOr(""):"";return by(ky(e.text,n),e.classes)}})),_y=zu(dh(),uh()),Ty=e=>({value:e}),Ey=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,My=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,By=e=>Ey.test(e)||My.test(e),Ay=e=>{return(t=e,((e,t)=>ke(e,t,0))(t,"#")?((e,t)=>e.substring(t))(t,"#".length):t).toUpperCase();var t},Dy=e=>{const t=e.toString(16);return(1===t.length?"0"+t:t).toUpperCase()},Fy=e=>{const t=Dy(e.red)+Dy(e.green)+Dy(e.blue);return Ty(t)},Iy=Math.min,Vy=Math.max,Ry=Math.round,zy=/^\s*rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)\s*$/i,Hy=/^\s*rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d?(?:\.\d+)?)\s*\)\s*$/i,Py=(e,t,o,n)=>({red:e,green:t,blue:o,alpha:n}),Ny=e=>{const t=parseInt(e,10);return t.toString()===e&&t>=0&&t<=255},Ly=e=>{let t,o,n;const s=(e.hue||0)%360;let r=e.saturation/100,a=e.value/100;if(r=Vy(0,Iy(r,1)),a=Vy(0,Iy(a,1)),0===r)return t=o=n=Ry(255*a),Py(t,o,n,1);const i=s/60,l=a*r,c=l*(1-Math.abs(i%2-1)),d=a-l;switch(Math.floor(i)){case 0:t=l,o=c,n=0;break;case 1:t=c,o=l,n=0;break;case 2:t=0,o=l,n=c;break;case 3:t=0,o=c,n=l;break;case 4:t=c,o=0,n=l;break;case 5:t=l,o=0,n=c;break;default:t=o=n=0}return t=Ry(255*(t+d)),o=Ry(255*(o+d)),n=Ry(255*(n+d)),Py(t,o,n,1)},Wy=e=>{const t=(e=>{const t=(e=>{const t=e.value.replace(Ey,((e,t,o,n)=>t+t+o+o+n+n));return{value:t}})(e),o=My.exec(t.value);return null===o?["FFFFFF","FF","FF","FF"]:o})(e),o=parseInt(t[1],16),n=parseInt(t[2],16),s=parseInt(t[3],16);return Py(o,n,s,1)},Uy=(e,t,o,n)=>{const s=parseInt(e,10),r=parseInt(t,10),a=parseInt(o,10),i=parseFloat(n);return Py(s,r,a,i)},jy=e=>{if("transparent"===e)return M.some(Py(0,0,0,0));const t=zy.exec(e);if(null!==t)return M.some(Uy(t[1],t[2],t[3],"1"));const o=Hy.exec(e);return null!==o?M.some(Uy(o[1],o[2],o[3],o[4])):M.none()},Gy=e=>`rgba(${e.red},${e.green},${e.blue},${e.alpha})`,$y=Py(255,0,0,1),qy=(e,t)=>{e.dispatch("ResizeContent",t)},Xy=(e,t)=>e.dispatch("ResolveName",{name:t.nodeName.toLowerCase(),target:t}),Jy=(e,t,o)=>({hue:e,saturation:t,value:o}),Yy=e=>{let t=0,o=0,n=0;const s=e.red/255,r=e.green/255,a=e.blue/255,i=Math.min(s,Math.min(r,a)),l=Math.max(s,Math.max(r,a));return i===l?(n=i,Jy(0,0,100*n)):(t=s===i?3:a===i?1:5,t=60*(t-(s===i?r-a:a===i?s-r:a-s)/(l-i)),o=(l-i)/l,n=l,Jy(Math.round(t),Math.round(100*o),Math.round(100*n)))},Ky=e=>Fy(Ly(e)),Zy=e=>{return(t=e,By(t)?M.some({value:Ay(t)}):M.none()).orThunk((()=>jy(e).map(Fy))).getOrThunk((()=>{const t=document.createElement("canvas");t.height=1,t.width=1;const o=t.getContext("2d");o.clearRect(0,0,t.width,t.height),o.fillStyle="#FFFFFF",o.fillStyle=e,o.fillRect(0,0,1,1);const n=o.getImageData(0,0,1,1).data,s=n[0],r=n[1],a=n[2],i=n[3];return Fy(Py(s,r,a,i))}));var t};var Qy=tinymce.util.Tools.resolve("tinymce.util.LocalStorage");const ex="tinymce-custom-colors",tx=((e=10)=>{const t=Qy.getItem(ex),o=r(t)?JSON.parse(t):[],n=e-(s=o).length<0?s.slice(0,e):s;var s;const a=e=>{n.splice(e,1)};return{add:t=>{I(n,t).each(a),n.unshift(t),n.length>e&&n.pop(),Qy.setItem(ex,JSON.stringify(n))},state:()=>n.slice(0)}})(10),ox=e=>{const t=[];for(let o=0;o<e.length;o+=2)t.push({text:e[o+1],value:"#"+Zy(e[o]).value,type:"choiceitem"});return t},nx=e=>t=>t.options.get(e),sx=nx("color_cols"),rx=nx("custom_colors"),ax=nx("color_map"),ix=e=>{tx.add(e)},lx="#000000",cx=e=>{const t="choiceitem",o={type:t,text:"Remove color",icon:"color-swatch-remove-color",value:"remove"};return e?[o,{type:t,text:"Custom color",icon:"color-picker",value:"custom"}]:[o]},dx=(e,t,o,n)=>{"custom"===o?fx(e)((o=>{o.each((o=>{ix(o),e.execCommand("mceApplyTextcolor",t,o),n(o)}))}),lx):"remove"===o?(n(""),e.execCommand("mceRemoveTextcolor",t)):(n(o),e.execCommand("mceApplyTextcolor",t,o))},ux=(e,t)=>e.concat(P(tx.state(),(e=>({type:"choiceitem",text:e,value:e}))).concat(cx(t))),mx=(e,t)=>o=>{o(ux(e,t))},gx=(e,t,o)=>{const n="forecolor"===t?"tox-icon-text-color__color":"tox-icon-highlight-bg-color__color";e.setIconFill(n,o)},px=(e,t,o,n,s)=>{e.ui.registry.addSplitButton(t,{tooltip:n,presets:"color",icon:"forecolor"===t?"text-color":"highlight-bg-color",select:t=>((e,t)=>{let o;return e.dom.getParents(e.selection.getStart(),(e=>{const n=g(e.style)?e.style["forecolor"===t?"color":"backgroundColor"]:null;n&&(o=o||n)})),M.from(o)})(e,o).bind((e=>jy(e).map((e=>{const o=Fy(e).value;return Oe(t.toLowerCase(),o)})))).getOr(!1),columns:sx(e),fetch:mx(ax(e),rx(e)),onAction:t=>{dx(e,o,s.get(),b)},onItemAction:(n,r)=>{dx(e,o,r,(o=>{s.set(o),((e,t)=>{e.dispatch("TextColorChange",t)})(e,{name:t,color:o})}))},onSetup:o=>{gx(o,t,s.get());const n=e=>{e.name===t&&gx(o,e.name,e.color)};return e.on("TextColorChange",n),()=>{e.off("TextColorChange",n)}}})},hx=(e,t,o,n)=>{e.ui.registry.addNestedMenuItem(t,{text:n,icon:"forecolor"===t?"text-color":"highlight-bg-color",getSubmenuItems:()=>[{type:"fancymenuitem",fancytype:"colorswatch",onAction:t=>{dx(e,o,t.value,b)}}]})},fx=e=>(t,o)=>{let n=!1;const s={colorpicker:o};e.windowManager.open({title:"Color Picker",size:"normal",body:{type:"panel",items:[{type:"colorpicker",name:"colorpicker",label:"Color"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:s,onAction:(e,t)=>{"hex-valid"===t.name&&(n=t.value)},onSubmit:o=>{const s=o.getData().colorpicker;n?(t(M.from(s)),o.close()):e.windowManager.alert(e.translate(["Invalid hex color code: {0}",s]))},onClose:b,onCancel:()=>{t(M.none())}})},bx=(e,t,o,n,s,r,a,i)=>{const l=Db(t),c=vx(t,o,n,"color"!==s?"normal":"color",r,a,i);return Vb(e,l,c,n,{menuType:s})},vx=(e,t,o,n,s,r,a)=>we(P(e,(i=>{return"choiceitem"===i.type?(l=i,Nn("choicemenuitem",Iv,l)).fold(Fb,(i=>M.some(((e,t,o,n,s,r,a,i=!0)=>{const l=Sy({presets:o,textContent:t?e.text:M.none(),htmlContent:M.none(),ariaLabel:e.text,iconContent:e.icon,shortcutContent:t?e.shortcut:M.none(),checkMark:t?M.some(xy(a.icons)):M.none(),caret:M.none(),value:e.value},a,i);return cn(my({data:gy(e),enabled:e.enabled,getApi:e=>({setActive:t=>{Yp.set(e,t)},isActive:()=>Yp.isOn(e),isEnabled:()=>!Cm.isDisabled(e),setEnabled:t=>Cm.set(e,!t)}),onAction:t=>n(e.value),onSetup:e=>(e.setActive(s),b),triggersSubmenu:!1,itemBehaviours:[]},l,r,a),{toggling:{toggleClass:tb,toggleOnExecute:!1,selected:e.active,exclusive:!0}})})(i,1===o,n,t,r(i.value),s,a,Db(e))))):M.none();var l}))),yx=(e,t)=>{const o=ub(t);return 1===e?{mode:"menu",moveOnTab:!0}:"auto"===e?{mode:"grid",selector:"."+o.item,initSize:{numColumns:1,numRows:1}}:{mode:"matrix",rowSelector:"."+("color"===t?"tox-swatches__row":"tox-collection__group")}},xx=Qr("cell-over"),wx=Qr("cell-execute"),Sx=(e,t,o)=>{const n=o=>Or(o,wx,{row:e,col:t}),s=(e,t)=>{t.stop(),n(e)};return Ja({dom:{tag:"div",attributes:{role:"button","aria-labelledby":o}},behaviours:gl([zp("insert-table-picker-cell",[Fr(Ps(),Wp.focus),Fr(nr(),n),Fr($s(),s),Fr(rr(),s)]),Yp.config({toggleClass:"tox-insert-table-picker__selected",toggleOnExecute:!1}),Wp.config({onFocus:o=>Or(o,xx,{row:e,col:t})})])})},Cx=e=>X(e,(e=>P(e,Ya))),kx=(e,t)=>Ga(`${t}x${e}`),Ox={inserttable:e=>{const t=Qr("size-label"),o=((e,t,o)=>{const n=[];for(let t=0;t<10;t++){const o=[];for(let n=0;n<10;n++)o.push(Sx(t,n,e));n.push(o)}return n})(t),n=kx(0,0),s=Bh({dom:{tag:"span",classes:["tox-insert-table-picker__label"],attributes:{id:t}},components:[n],behaviours:gl([Rp.config({})])});return{type:"widget",data:{value:Qr("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[_y.widget({dom:{tag:"div",classes:["tox-insert-table-picker"]},components:Cx(o).concat(s.asSpec()),behaviours:gl([zp("insert-table-picker",[Lr((e=>{Rp.set(s.get(e),[n])})),zr(xx,((e,t,n)=>{const{row:r,col:a}=n.event;((e,t,o,n,s)=>{for(let n=0;n<10;n++)for(let s=0;s<10;s++)Yp.set(e[n][s],n<=t&&s<=o)})(o,r,a),Rp.set(s.get(e),[kx(r+1,a+1)])})),zr(wx,((t,o,n)=>{const{row:s,col:r}=n.event;e.onAction({numRows:s+1,numColumns:r+1}),kr(t,ir())}))]),Tp.config({initSize:{numRows:10,numColumns:10},mode:"flatgrid",selector:'[role="button"]'})])})]}},colorswatch:(e,t)=>{const o=((e,t)=>{const o=e.initData.allowCustomColors&&t.colorinput.hasCustomColors();return e.initData.colors.fold((()=>ux(t.colorinput.getColors(),o)),(e=>e.concat(cx(o))))})(e,t),n=t.colorinput.getColorCols(),s="color",r={...bx(Qr("menu-value"),o,(t=>{e.onAction({value:t})}),n,s,Kf.CLOSE_ON_EXECUTE,_,t.shared.providers),markers:ub(s),movement:yx(n,s)};return{type:"widget",data:{value:Qr("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[_y.widget(bh.sketch(r))]}}},_x=e=>({type:"separator",dom:{tag:"div",classes:[Qf,"tox-collection__group-heading"]},components:e.text.map(Ga).toArray()});var Tx=Object.freeze({__proto__:null,getCoupled:(e,t,o,n)=>o.getOrCreate(e,t,n),getExistingCoupled:(e,t,o,n)=>o.getExisting(e,t,n)}),Ex=[Jn("others",Pn(Ko.value,Mn()))],Mx=Object.freeze({__proto__:null,init:()=>{const e={},t=(t,o)=>{if(0===ae(t.others).length)throw new Error("Cannot find any known coupled components");return be(e,o)},o=y({});return ba({readState:o,getExisting:(e,o,n)=>t(o,n).orThunk((()=>(be(o.others,n).getOrDie("No information found for coupled component: "+n),M.none()))),getOrCreate:(o,n,s)=>t(n,s).getOrThunk((()=>{const t=be(n.others,s).getOrDie("No information found for coupled component: "+s)(o),r=o.getSystem().build(t);return e[s]=r,r}))})}});const Bx=hl({fields:Ex,name:"coupling",apis:Tx,state:Mx}),Ax=e=>{let t=M.none(),o=[];const n=e=>{s()?r(e):o.push(e)},s=()=>t.isSome(),r=e=>{t.each((t=>{setTimeout((()=>{e(t)}),0)}))};return e((e=>{s()||(t=M.some(e),N(o,r),o=[])})),{get:n,map:e=>Ax((t=>{n((o=>{t(e(o))}))})),isReady:s}},Dx={nu:Ax,pure:e=>Ax((t=>{t(e)}))},Fx=e=>{setTimeout((()=>{throw e}),0)},Ix=e=>{const t=t=>{e().then(t,Fx)};return{map:t=>Ix((()=>e().then(t))),bind:t=>Ix((()=>e().then((e=>t(e).toPromise())))),anonBind:t=>Ix((()=>e().then((()=>t.toPromise())))),toLazy:()=>Dx.nu(t),toCached:()=>{let t=null;return Ix((()=>(null===t&&(t=e()),t)))},toPromise:e,get:t}},Vx=e=>Ix((()=>new Promise(e))),Rx=e=>Ix((()=>Promise.resolve(e))),zx=y("sink"),Hx=y(Du({name:zx(),overrides:y({dom:{tag:"div"},behaviours:gl([ud.config({useFixed:T})]),events:Br([Hr(Ws()),Hr(Vs()),Hr($s())])})})),Px=(e,t)=>{const o=e.getHotspot(t).getOr(t),n="hotspot",s=e.getAnchorOverrides();return e.layouts.fold((()=>({type:n,hotspot:o,overrides:s})),(e=>({type:n,hotspot:o,overrides:s,layouts:e})))},Nx=(e,t,o,n,s,r,a)=>{const i=((e,t,o,n,s,r,a)=>{const i=((e,t,o)=>(0,e.fetch)(o).map(t))(e,t,n),l=Ux(n,e);return i.map((e=>e.bind((e=>M.from(_h.sketch({...r.menu(),uid:aa(""),data:e,highlightOnOpen:a,onOpenMenu:(e,t)=>{const n=l().getOrDie();ud.position(n,t,{anchor:o}),Rd.decloak(s)},onOpenSubmenu:(e,t,o)=>{const n=l().getOrDie();ud.position(n,o,{anchor:{type:"submenu",item:t}}),Rd.decloak(s)},onRepositionMenu:(e,t,n)=>{const s=l().getOrDie();ud.position(s,t,{anchor:o}),N(n,(e=>{ud.position(s,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem}})}))},onEscape:()=>(Wp.focus(n),Rd.close(s),M.some(!0))}))))))})(e,t,Px(e,o),o,n,s,a);return i.map((e=>(e.fold((()=>{Rd.isOpen(n)&&Rd.close(n)}),(e=>{Rd.cloak(n),Rd.open(n,e),r(n)})),n)))},Lx=(e,t,o,n,s,r,a)=>(Rd.close(n),Rx(n)),Wx=(e,t,o,n,s,r)=>{const a=Bx.getCoupled(o,"sandbox");return(Rd.isOpen(a)?Lx:Nx)(e,t,o,a,n,s,r)},Ux=(e,t)=>e.getSystem().getByUid(t.uid+"-"+zx()).map((e=>()=>Ko.value(e))).getOrThunk((()=>t.lazySink.fold((()=>()=>Ko.error(new Error("No internal sink is specified, nor could an external sink be found"))),(t=>()=>t(e))))),jx=e=>{Rd.getState(e).each((e=>{_h.repositionMenus(e)}))},Gx=(e,t,o)=>{const n=ii(),s=Ux(t,e);return{dom:{tag:"div",classes:e.sandboxClasses,attributes:{id:n.id,role:"listbox"}},behaviours:iu(e.sandboxBehaviours,[ou.config({store:{mode:"memory",initialValue:t}}),Rd.config({onOpen:(s,r)=>{const a=Px(e,t);n.link(t.element),e.matchWidth&&((e,t,o)=>{const n=cm.getCurrent(t).getOr(t),s=$t(e.element);o?_t(n.element,"min-width",s+"px"):((e,t)=>{Gt.set(e,t)})(n.element,s)})(a.hotspot,r,e.useMinWidth),e.onOpen(a,s,r),void 0!==o&&void 0!==o.onOpen&&o.onOpen(s,r)},onClose:(e,s)=>{n.unlink(t.element),void 0!==o&&void 0!==o.onClose&&o.onClose(e,s)},isPartOf:(e,o,n)=>li(o,n)||li(t,n),getAttachPoint:()=>s().getOrDie()}),cm.config({find:e=>Rd.getState(e).bind((e=>cm.getCurrent(e)))}),yl.config({channels:{...Ld({isExtraPart:_}),...Ud({doReposition:jx})}})])}},$x=e=>{const t=Bx.getCoupled(e,"sandbox");jx(t)},qx=()=>[us("sandboxClasses",[]),au("sandboxBehaviours",[cm,yl,Rd,ou])],Xx=y([Xn("dom"),Xn("fetch"),wi("onOpen"),Si("onExecute"),us("getHotspot",M.some),us("getAnchorOverrides",y({})),dc(),nu("dropdownBehaviours",[Yp,Bx,Tp,Wp]),Xn("toggleClass"),us("eventOrder",{}),ns("lazySink"),us("matchWidth",!1),us("useMinWidth",!1),ns("role")].concat(qx())),Jx=y([Au({schema:[vi(),us("fakeFocus",!1)],name:"menu",defaults:e=>({onExecute:e.onExecute})}),Hx()]),Yx=rm({name:"Dropdown",configFields:Xx(),partFields:Jx(),factory:(e,t,o,n)=>{const s=e=>{Rd.getState(e).each((e=>{_h.highlightPrimary(e)}))},r=(t,o,s)=>Wx(e,x,t,n,o,s),a={expand:e=>{Yp.isOn(e)||r(e,b,kh.HighlightNone).get(b)},open:e=>{Yp.isOn(e)||r(e,b,kh.HighlightMenuAndItem).get(b)},refetch:t=>Bx.getExistingCoupled(t,"sandbox").fold((()=>r(t,b,kh.HighlightMenuAndItem).map(b)),(o=>Nx(e,x,t,o,n,b,kh.HighlightMenuAndItem).map(b))),isOpen:Yp.isOn,close:e=>{Yp.isOn(e)&&r(e,b,kh.HighlightMenuAndItem).get(b)},repositionMenus:e=>{Yp.isOn(e)&&$x(e)}},i=(e,t)=>(_r(e),M.some(!0));return{uid:e.uid,dom:e.dom,components:t,behaviours:ru(e.dropdownBehaviours,[Yp.config({toggleClass:e.toggleClass,aria:{mode:"expanded"}}),Bx.config({others:{sandbox:t=>Gx(e,t,{onOpen:()=>Yp.on(t),onClose:()=>Yp.off(t)})}}),Tp.config({mode:"special",onSpace:i,onEnter:i,onDown:(e,t)=>{if(Yx.isOpen(e)){const t=Bx.getCoupled(e,"sandbox");s(t)}else Yx.open(e);return M.some(!0)},onEscape:(e,t)=>Yx.isOpen(e)?(Yx.close(e),M.some(!0)):M.none()}),Wp.config({})]),events:Zp(M.some((e=>{r(e,s,kh.HighlightMenuAndItem).get(b)}))),eventOrder:{...e.eventOrder,[nr()]:["disabling","toggling","alloy.base.behaviour"]},apis:a,domModification:{attributes:{"aria-haspopup":"true",...e.role.fold((()=>({})),(e=>({role:e}))),..."button"===e.dom.tag?{type:("type",be(e.dom,"attributes").bind((e=>be(e,"type")))).getOr("button")}:{}}}}},apis:{open:(e,t)=>e.open(t),refetch:(e,t)=>e.refetch(t),expand:(e,t)=>e.expand(t),close:(e,t)=>e.close(t),isOpen:(e,t)=>e.isOpen(t),repositionMenus:(e,t)=>e.repositionMenus(t)}}),Kx=(e,t,o)=>{Cb(e).each((e=>{var n;((e,t)=>{wt(t.element,"id").each((t=>vt(e.element,"aria-activedescendant",t)))})(e,o),(Fa((n=t).element,_b)?M.some(n.element):si(n.element,"."+_b)).each((t=>{wt(t,"id").each((t=>vt(e.element,"aria-controls",t)))}))})),vt(o.element,"aria-selected","true")},Zx=(e,t,o)=>{vt(o.element,"aria-selected","false")},Qx=e=>Bx.getExistingCoupled(e,"sandbox").bind(Sb).map(kb).map((e=>e.fetchPattern)).getOr("");var ew;!function(e){e[e.ContentFocus=0]="ContentFocus",e[e.UiFocus=1]="UiFocus"}(ew||(ew={}));const tw=(e,t,o,n,s)=>{const r=o.shared.providers,a=e=>s?{...e,shortcut:M.none(),icon:e.text.isSome()?M.none():e.icon}:e;switch(e.type){case"menuitem":return(i=e,Nn("menuitem",Pv,i)).fold(Fb,(e=>M.some(((e,t,o,n=!0)=>{const s=Sy({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:M.none(),ariaLabel:e.text,caret:M.none(),checkMark:M.none(),shortcutContent:e.shortcut},o,n);return my({data:gy(e),getApi:e=>({isEnabled:()=>!Cm.isDisabled(e),setEnabled:t=>Cm.set(e,!t)}),enabled:e.enabled,onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},s,t,o)})(a(e),t,r,n))));case"nestedmenuitem":return(e=>Nn("nestedmenuitem",Nv,e))(e).fold(Fb,(e=>M.some(((e,t,o,n=!0,s=!1)=>{const r=s?(a=o.icons,hy("chevron-down",a,[sb])):(e=>hy("chevron-right",e,[sb]))(o.icons);var a;const i=Sy({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:M.none(),ariaLabel:e.text,caret:M.some(r),checkMark:M.none(),shortcutContent:e.shortcut},o,n);return my({data:gy(e),getApi:e=>({isEnabled:()=>!Cm.isDisabled(e),setEnabled:t=>Cm.set(e,!t)}),enabled:e.enabled,onAction:b,onSetup:e.onSetup,triggersSubmenu:!0,itemBehaviours:[]},i,t,o)})(a(e),t,r,n,s))));case"togglemenuitem":return(e=>Nn("togglemenuitem",Lv,e))(e).fold(Fb,(e=>M.some(((e,t,o,n=!0)=>{const s=Sy({iconContent:e.icon,textContent:e.text,htmlContent:M.none(),ariaLabel:e.text,checkMark:M.some(xy(o.icons)),caret:M.none(),shortcutContent:e.shortcut,presets:"normal",meta:e.meta},o,n);return cn(my({data:gy(e),enabled:e.enabled,getApi:e=>({setActive:t=>{Yp.set(e,t)},isActive:()=>Yp.isOn(e),isEnabled:()=>!Cm.isDisabled(e),setEnabled:t=>Cm.set(e,!t)}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},s,t,o),{toggling:{toggleClass:tb,toggleOnExecute:!1,selected:e.active}})})(a(e),t,r,n))));case"separator":return(e=>Nn("separatormenuitem",dv,e))(e).fold(Fb,(e=>M.some(_x(e))));case"fancymenuitem":return(e=>Nn("fancymenuitem",Hv,e))(e).fold(Fb,(e=>((e,t)=>be(Ox,e.fancytype).map((o=>o(e,t))))(e,o)));default:return console.error("Unknown item in general menu",e),M.none()}var i},ow=(e,t,o,n,s,r,a)=>{const i=1===n,l=!i||Db(e);return we(P(e,(e=>{switch(e.type){case"separator":return(n=e,Nn("Autocompleter.Separator",dv,n)).fold(Fb,(e=>M.some(_x(e))));case"cardmenuitem":return(e=>Nn("cardmenuitem",Fv,e))(e).fold(Fb,(e=>M.some(((e,t,o,n)=>{const s={dom:wy(e.label),optComponents:[M.some({dom:{tag:"div",classes:[ab,ib]},components:Oy(e.items,n)})]};return my({data:gy({text:M.none(),...e}),enabled:e.enabled,getApi:e=>({isEnabled:()=>!Cm.isDisabled(e),setEnabled:t=>{Cm.set(e,!t),N(Rc(e.element,"*"),(o=>{e.getSystem().getByDom(o).each((e=>{e.hasConfigured(Cm)&&Cm.set(e,!t)}))}))}}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:M.from(n.itemBehaviours).getOr([])},s,t,o.providers)})({...e,onAction:t=>{e.onAction(t),o(e.value,e.meta)}},s,r,{itemBehaviours:Cy(e.meta,r),cardText:{matchText:t,highlightOn:a}}))));default:return(e=>Nn("Autocompleter.Item",uv,e))(e).fold(Fb,(e=>M.some(((e,t,o,n,s,r,a,i=!0)=>{const l=Sy({presets:n,textContent:M.none(),htmlContent:o?e.text.map((e=>ky(e,t))):M.none(),ariaLabel:e.text,iconContent:e.icon,shortcutContent:M.none(),checkMark:M.none(),caret:M.none(),value:e.value},a.providers,i,e.icon);return my({data:gy(e),enabled:e.enabled,getApi:y({}),onAction:t=>s(e.value,e.meta),onSetup:y(b),triggersSubmenu:!1,itemBehaviours:Cy(e.meta,a)},l,r,a.providers)})(e,t,i,"normal",o,s,r,l))))}var n})))},nw=(e,t,o,n,s,r)=>{const a=Db(t),i=we(P(t,(e=>{const t=e=>tw(e,o,n,(e=>s?!ve(e,"text"):a)(e),s);return"nestedmenuitem"===e.type&&e.getSubmenuItems().length<=0?t({...e,enabled:!1}):t(e)}))),l=(e=>"no-search"===e.searchMode?{menuType:"normal"}:{menuType:"searchable",searchMode:e})(r);return(s?Ib:Vb)(e,a,i,1,l)},sw=e=>_h.singleData(e.value,e),rw=(e,t)=>{const o=xs(!1),n=xs(!1),s=Ja(Th.sketch({dom:{tag:"div",classes:["tox-autocompleter"]},components:[],fireDismissalEventInstead:{},inlineBehaviours:gl([zp("dismissAutocompleter",[Fr(fr(),(()=>c()))])]),lazySink:t.getSink})),r=()=>Th.isOpen(s),a=n.get,i=()=>{r()&&Th.hide(s)},l=()=>Th.getContent(s).bind((e=>te(e.components(),0))),c=()=>e.execCommand("mceAutocompleterClose"),d=n=>{const r=(n=>{const s=re(n,(e=>M.from(e.columns))).getOr(1);return X(n,(n=>{const r=n.items;return ow(r,n.matchText,((t,s)=>{const r=e.selection.getRng();((e,t)=>Jf(Ie(t.startContainer)).map((t=>{const o=e.createRng();return o.selectNode(t.dom),o})))(e.dom,r).each((r=>{const a={hide:()=>c(),reload:t=>{i(),e.execCommand("mceAutocompleterReload",!1,{fetchOptions:t})}};o.set(!0),n.onAction(a,r,t,s),o.set(!1)}))}),s,Kf.BUBBLE_TO_SANDBOX,t,n.highlightOn)}))})(n);r.length>0?((t,o)=>{var n;(n=Ie(e.getBody()),si(n,Xf)).each((n=>{const r=re(t,(e=>M.from(e.columns))).getOr(1);Th.showMenuAt(s,{anchor:{type:"node",root:Ie(e.getBody()),node:M.from(n)}},((e,t,o,n)=>{const s=yx(t,n),r=ub(n);return{data:sw({...e,movement:s,menuBehaviours:Uv("auto"!==t?[]:[Lr(((e,t)=>{Wv(e,4,r.item).each((({numColumns:t,numRows:o})=>{Tp.setGridSize(e,o,t)}))}))])}),menu:{markers:ub(n),fakeFocus:o===ew.ContentFocus}}})(Vb("autocompleter-value",!0,o,r,{menuType:"normal"}),r,ew.ContentFocus,"normal"))})),l().each(Fm.highlightFirst)})(n,r):i()};e.on("AutocompleterStart",(({lookupData:e})=>{n.set(!0),o.set(!1),d(e)})),e.on("AutocompleterUpdate",(({lookupData:e})=>d(e))),e.on("AutocompleterEnd",(()=>{i(),n.set(!1),o.set(!1)}));((e,t)=>{const o=(e,t)=>{Or(e,Ws(),{raw:t})},n=()=>e.getMenu().bind(Fm.getHighlighted);t.on("keydown",(t=>{const s=t.which;e.isActive()&&(e.isMenuOpen()?13===s?(n().each(_r),t.preventDefault()):40===s?(n().fold((()=>{e.getMenu().each(Fm.highlightFirst)}),(e=>{o(e,t)})),t.preventDefault(),t.stopImmediatePropagation()):37!==s&&38!==s&&39!==s||n().each((e=>{o(e,t),t.preventDefault(),t.stopImmediatePropagation()})):13!==s&&38!==s&&40!==s||e.cancelIfNecessary())})),t.on("NodeChange",(t=>{e.isActive()&&!e.isProcessingAction()&&Jf(Ie(t.element)).isNone()&&e.cancelIfNecessary()}))})({cancelIfNecessary:c,isMenuOpen:r,isActive:a,isProcessingAction:o.get,getMenu:l},e)},aw=(e,t,o)=>ri(e,t,o).isSome(),iw=(e,t)=>{let o=null;return{cancel:()=>{null!==o&&(clearTimeout(o),o=null)},schedule:(...n)=>{o=setTimeout((()=>{e.apply(null,n),o=null}),t)}}},lw=e=>{const t=e.raw;return void 0===t.touches||1!==t.touches.length?M.none():M.some(t.touches[0])},cw=(e,t)=>{const o={stopBackspace:!0,...t},n=(e=>{const t=Wl(),o=xs(!1),n=iw((t=>{e.triggerEvent(ar(),t),o.set(!0)}),400),s=Cs([{key:As(),value:e=>(lw(e).each((s=>{n.cancel();const r={x:s.clientX,y:s.clientY,target:e.target};n.schedule(e),o.set(!1),t.set(r)})),M.none())},{key:Ds(),value:e=>(n.cancel(),lw(e).each((e=>{t.on((o=>{((e,t)=>{const o=Math.abs(e.clientX-t.x),n=Math.abs(e.clientY-t.y);return o>5||n>5})(e,o)&&t.clear()}))})),M.none())},{key:Fs(),value:s=>(n.cancel(),t.get().filter((e=>Xe(e.target,s.target))).map((t=>o.get()?(s.prevent(),!1):e.triggerEvent(rr(),s))))}]);return{fireIfReady:(e,t)=>be(s,t).bind((t=>t(e)))}})(o),s=P(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","transitioncancel","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),(t=>jl(e,t,(e=>{n.fireIfReady(e,t).each((t=>{t&&e.kill()})),o.triggerEvent(t,e)&&e.kill()})))),r=Wl(),a=jl(e,"paste",(e=>{n.fireIfReady(e,"paste").each((t=>{t&&e.kill()})),o.triggerEvent("paste",e)&&e.kill(),r.set(setTimeout((()=>{o.triggerEvent(tr(),e)}),0))})),i=jl(e,"keydown",(e=>{o.triggerEvent("keydown",e)?e.kill():o.stopBackspace&&(e=>e.raw.which===Im[0]&&!V(["input","textarea"],Pe(e.target))&&!aw(e.target,'[contenteditable="true"]'))(e)&&e.prevent()})),l=jl(e,"focusin",(e=>{o.triggerEvent("focusin",e)&&e.kill()})),c=Wl(),d=jl(e,"focusout",(e=>{o.triggerEvent("focusout",e)&&e.kill(),c.set(setTimeout((()=>{o.triggerEvent(er(),e)}),0))}));return{unbind:()=>{N(s,(e=>{e.unbind()})),i.unbind(),l.unbind(),d.unbind(),a.unbind(),r.on(clearTimeout),c.on(clearTimeout)}}},dw=(e,t)=>{const o=be(e,"target").getOr(t);return xs(o)},uw=ws([{stopped:[]},{resume:["element"]},{complete:[]}]),mw=(e,t,o,n,s,r)=>{const a=e(t,n),i=((e,t)=>{const o=xs(!1),n=xs(!1);return{stop:()=>{o.set(!0)},cut:()=>{n.set(!0)},isStopped:o.get,isCut:n.get,event:e,setSource:t.set,getSource:t.get}})(o,s);return a.fold((()=>(r.logEventNoHandlers(t,n),uw.complete())),(e=>{const o=e.descHandler;return xa(o)(i),i.isStopped()?(r.logEventStopped(t,e.element,o.purpose),uw.stopped()):i.isCut()?(r.logEventCut(t,e.element,o.purpose),uw.complete()):et(e.element).fold((()=>(r.logNoParent(t,e.element,o.purpose),uw.complete())),(n=>(r.logEventResponse(t,e.element,o.purpose),uw.resume(n))))}))},gw=(e,t,o,n,s,r)=>mw(e,t,o,n,s,r).fold(T,(n=>gw(e,t,o,n,s,r)),_),pw=(e,t,o,n,s)=>{const r=dw(o,n);return gw(e,t,o,n,r,s)},hw=()=>{const e=(()=>{const e={};return{registerId:(t,o,n)=>{le(n,((n,s)=>{const r=void 0!==e[s]?e[s]:{};r[o]=((e,t)=>({cHandler:S.apply(void 0,[e.handler].concat(t)),purpose:e.purpose}))(n,t),e[s]=r}))},unregisterId:t=>{le(e,((e,o)=>{ve(e,t)&&delete e[t]}))},filterByType:t=>be(e,t).map((e=>pe(e,((e,t)=>((e,t)=>({id:e,descHandler:t}))(t,e))))).getOr([]),find:(t,o,n)=>be(e,o).bind((e=>_s(n,(t=>((e,t)=>ra(t).bind((t=>be(e,t))).map((e=>((e,t)=>({element:e,descHandler:t}))(t,e))))(e,t)),t)))}})(),t={},o=o=>{ra(o.element).each((o=>{delete t[o],e.unregisterId(o)}))};return{find:(t,o,n)=>e.find(t,o,n),filter:t=>e.filterByType(t),register:n=>{const s=(e=>{const t=e.element;return ra(t).getOrThunk((()=>((e,t)=>{const o=Qr(oa+"uid-");return sa(t,o),o})(0,e.element)))})(n);ye(t,s)&&((e,n)=>{const s=t[n];if(s!==e)throw new Error('The tagId "'+n+'" is already used by: '+Xr(s.element)+"\nCannot use it for: "+Xr(e.element)+"\nThe conflicting element is"+(pt(s.element)?" ":" not ")+"already in the DOM");o(e)})(n,s);const r=[n];e.registerId(r,s,n.events),t[s]=n},unregister:o,getById:e=>be(t,e)}},fw=sm({name:"Container",factory:e=>{const{attributes:t,...o}=e.dom;return{uid:e.uid,dom:{tag:"div",attributes:{role:"presentation",...t},...o},components:e.components,behaviours:su(e.containerBehaviours),events:e.events,domModification:e.domModification,eventOrder:e.eventOrder}},configFields:[us("components",[]),nu("containerBehaviours",[]),us("events",{}),us("domModification",{}),us("eventOrder",{})]}),bw=e=>{const t=t=>et(e.element).fold(T,(e=>Xe(t,e))),o=hw(),n=(e,n)=>o.find(t,e,n),s=cw(e.element,{triggerEvent:(e,t)=>mi(e,t.target,(o=>((e,t,o,n)=>pw(e,t,o,o.target,n))(n,e,t,o)))}),r={debugInfo:y("real"),triggerEvent:(e,t,o)=>{mi(e,t,(s=>pw(n,e,o,t,s)))},triggerFocus:(e,t)=>{ra(e).fold((()=>{wl(e)}),(o=>{mi(Qs(),e,(o=>(((e,t,o,n,s)=>{const r=dw(o,n);mw(e,t,o,n,r,s)})(n,Qs(),{originator:t,kill:b,prevent:b,target:e},e,o),!1)))}))},triggerEscape:(e,t)=>{r.triggerEvent("keydown",e.element,t.event)},getByUid:e=>p(e),getByDom:e=>h(e),build:Ja,buildOrPatch:Xa,addToGui:e=>{l(e)},removeFromGui:e=>{c(e)},addToWorld:e=>{a(e)},removeFromWorld:e=>{i(e)},broadcast:e=>{u(e)},broadcastOn:(e,t)=>{m(e,t)},broadcastEvent:(e,t)=>{g(e,t)},isConnected:T},a=e=>{e.connect(r),We(e.element)||(o.register(e),N(e.components(),a),r.triggerEvent(cr(),e.element,{target:e.element}))},i=e=>{We(e.element)||(N(e.components(),i),o.unregister(e)),e.disconnect()},l=t=>{vd(e,t)},c=e=>{wd(e)},d=e=>{const t=o.filter(or());N(t,(t=>{const o=t.descHandler;xa(o)(e)}))},u=e=>{d({universal:!0,data:e})},m=(e,t)=>{d({universal:!1,channels:e,data:t})},g=(e,t)=>((e,t,o)=>{const n=(e=>{const t=xs(!1);return{stop:()=>{t.set(!0)},cut:b,isStopped:t.get,isCut:_,event:e,setSource:k("Cannot set source of a broadcasted event"),getSource:k("Cannot get source of a broadcasted event")}})(t);return N(e,(e=>{const t=e.descHandler;xa(t)(n)})),n.isStopped()})(o.filter(e),t),p=e=>o.getById(e).fold((()=>Ko.error(new Error('Could not find component with uid: "'+e+'" in system.'))),Ko.value),h=e=>{const t=ra(e).getOr("not found");return p(t)};return a(e),{root:e,element:e.element,destroy:()=>{s.unbind(),Ro(e.element)},add:l,remove:c,getByUid:p,getByDom:h,addToWorld:a,removeFromWorld:i,broadcast:u,broadcastOn:m,broadcastEvent:g}},vw=y([us("prefix","form-field"),nu("fieldBehaviours",[cm,ou])]),yw=y([Du({schema:[Xn("dom")],name:"label"}),Du({factory:{sketch:e=>({uid:e.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:e.text}})},schema:[Xn("text")],name:"aria-descriptor"}),Bu({factory:{sketch:e=>{const t=((e,t)=>{const o={};return le(e,((e,n)=>{V(t,n)||(o[n]=e)})),o})(e,["factory"]);return e.factory.sketch(t)}},schema:[Xn("factory")],name:"field"})]),xw=rm({name:"FormField",configFields:vw(),partFields:yw(),factory:(e,t,o,n)=>{const s=ru(e.fieldBehaviours,[cm.config({find:t=>ju(t,e,"field")}),ou.config({store:{mode:"manual",getValue:e=>cm.getCurrent(e).bind(ou.getValue),setValue:(e,t)=>{cm.getCurrent(e).each((e=>{ou.setValue(e,t)}))}}})]),r=Br([Lr(((t,o)=>{const n=$u(t,e,["label","field","aria-descriptor"]);n.field().each((t=>{const o=Qr(e.prefix);n.label().each((e=>{vt(e.element,"for",o),vt(t.element,"id",o)})),n["aria-descriptor"]().each((o=>{const n=Qr(e.prefix);vt(o.element,"id",n),vt(t.element,"aria-describedby",n)}))}))}))]),a={getField:t=>ju(t,e,"field"),getLabel:t=>ju(t,e,"label")};return{uid:e.uid,dom:e.dom,components:t,behaviours:s,events:r,apis:a}},apis:{getField:(e,t)=>e.getField(t),getLabel:(e,t)=>e.getLabel(t)}});var ww=Object.freeze({__proto__:null,exhibit:(e,t)=>ya({attributes:Cs([{key:t.tabAttr,value:"true"}])})}),Sw=[us("tabAttr","data-alloy-tabstop")];const Cw=hl({fields:Sw,name:"tabstopping",active:ww});var kw=tinymce.util.Tools.resolve("tinymce.html.Entities");const Ow=(e,t,o,n)=>{const s=_w(e,t,o,n);return xw.sketch(s)},_w=(e,t,o,n)=>({dom:Tw(o),components:e.toArray().concat([t]),fieldBehaviours:gl(n)}),Tw=e=>({tag:"div",classes:["tox-form__group"].concat(e)}),Ew=(e,t)=>xw.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[Ga(t.translate(e))]}),Mw=Qr("form-component-change"),Bw=Qr("form-close"),Aw=Qr("form-cancel"),Dw=Qr("form-action"),Fw=Qr("form-submit"),Iw=Qr("form-block"),Vw=Qr("form-unblock"),Rw=Qr("form-tabchange"),zw=Qr("form-resize"),Hw=["input","textarea"],Pw=e=>{const t=Pe(e);return V(Hw,t)},Nw=(e,t)=>{const o=t.getRoot(e).getOr(e.element);Da(o,t.invalidClass),t.notify.each((t=>{Pw(e.element)&&vt(e.element,"aria-invalid",!1),t.getContainer(e).each((e=>{$r(e,t.validHtml)})),t.onValid(e)}))},Lw=(e,t,o,n)=>{const s=t.getRoot(e).getOr(e.element);Aa(s,t.invalidClass),t.notify.each((t=>{Pw(e.element)&&vt(e.element,"aria-invalid",!0),t.getContainer(e).each((e=>{$r(e,n)})),t.onInvalid(e,n)}))},Ww=(e,t,o)=>t.validator.fold((()=>Rx(Ko.value(!0))),(t=>t.validate(e))),Uw=(e,t,o)=>(t.notify.each((t=>{t.onValidate(e)})),Ww(e,t).map((o=>e.getSystem().isConnected()?o.fold((o=>(Lw(e,t,0,o),Ko.error(o))),(o=>(Nw(e,t),Ko.value(o)))):Ko.error("No longer in system"))));var jw=Object.freeze({__proto__:null,markValid:Nw,markInvalid:Lw,query:Ww,run:Uw,isInvalid:(e,t)=>{const o=t.getRoot(e).getOr(e.element);return Fa(o,t.invalidClass)}}),Gw=Object.freeze({__proto__:null,events:(e,t)=>e.validator.map((t=>Br([Fr(t.onEvent,(t=>{Uw(t,e).get(x)}))].concat(t.validateOnLoad?[Lr((t=>{Uw(t,e).get(b)}))]:[])))).getOr({})}),$w=[Xn("invalidClass"),us("getRoot",M.none),ds("notify",[us("aria","alert"),us("getContainer",M.none),us("validHtml",""),wi("onValid"),wi("onInvalid"),wi("onValidate")]),ds("validator",[Xn("validate"),us("onEvent","input"),us("validateOnLoad",!0)])];const qw=hl({fields:$w,name:"invalidating",active:Gw,apis:jw,extra:{validation:e=>t=>{const o=ou.getValue(t);return Rx(e(o))}}}),Xw=hl({fields:[],name:"unselecting",active:Object.freeze({__proto__:null,events:()=>Br([Ar(Ys(),T)]),exhibit:()=>ya({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})})}),Jw=Qr("color-input-change"),Yw=Qr("color-swatch-change"),Kw=Qr("color-picker-cancel"),Zw=Du({schema:[Xn("dom")],name:"label"}),Qw=e=>Du({name:e+"-edge",overrides:t=>t.model.manager.edgeActions[e].fold((()=>({})),(e=>({events:Br([Ir(As(),((t,o,n)=>e(t,n)),[t]),Ir(Vs(),((t,o,n)=>e(t,n)),[t]),Ir(Rs(),((t,o,n)=>{n.mouseIsDown.get()&&e(t,n)}),[t])])})))}),eS=Qw("top-left"),tS=Qw("top"),oS=Qw("top-right"),nS=Qw("right"),sS=Qw("bottom-right"),rS=Qw("bottom"),aS=Qw("bottom-left");var iS=[Zw,Qw("left"),nS,tS,rS,eS,oS,aS,sS,Bu({name:"thumb",defaults:y({dom:{styles:{position:"absolute"}}}),overrides:e=>({events:Br([Rr(As(),e,"spectrum"),Rr(Ds(),e,"spectrum"),Rr(Fs(),e,"spectrum"),Rr(Vs(),e,"spectrum"),Rr(Rs(),e,"spectrum"),Rr(Hs(),e,"spectrum")])})}),Bu({schema:[$n("mouseIsDown",(()=>xs(!1)))],name:"spectrum",overrides:e=>{const t=e.model.manager,o=(o,n)=>t.getValueFromEvent(n).map((n=>t.setValueFrom(o,e,n)));return{behaviours:gl([Tp.config({mode:"special",onLeft:o=>t.onLeft(o,e),onRight:o=>t.onRight(o,e),onUp:o=>t.onUp(o,e),onDown:o=>t.onDown(o,e)}),Wp.config({})]),events:Br([Fr(As(),o),Fr(Ds(),o),Fr(Vs(),o),Fr(Rs(),((t,n)=>{e.mouseIsDown.get()&&o(t,n)}))])}}})];const lS=y("slider.change.value"),cS=e=>{const t=e.event.raw;if((e=>-1!==e.type.indexOf("touch"))(t)){const e=t;return void 0!==e.touches&&1===e.touches.length?M.some(e.touches[0]).map((e=>Lt(e.clientX,e.clientY))):M.none()}{const e=t;return void 0!==e.clientX?M.some(e).map((e=>Lt(e.clientX,e.clientY))):M.none()}},dS=e=>e.model.minX,uS=e=>e.model.minY,mS=e=>e.model.minX-1,gS=e=>e.model.minY-1,pS=e=>e.model.maxX,hS=e=>e.model.maxY,fS=e=>e.model.maxX+1,bS=e=>e.model.maxY+1,vS=(e,t,o)=>t(e)-o(e),yS=e=>vS(e,pS,dS),xS=e=>vS(e,hS,uS),wS=e=>yS(e)/2,SS=e=>xS(e)/2,CS=e=>e.stepSize,kS=e=>e.snapToGrid,OS=e=>e.snapStart,_S=e=>e.rounded,TS=(e,t)=>void 0!==e[t+"-edge"],ES=e=>TS(e,"left"),MS=e=>TS(e,"right"),BS=e=>TS(e,"top"),AS=e=>TS(e,"bottom"),DS=e=>e.model.value.get(),FS=(e,t)=>({x:e,y:t}),IS=(e,t)=>{Or(e,lS(),{value:t})},VS=(e,t,o,n)=>e<t?e:e>o?o:e===t?t-1:Math.max(t,e-n),RS=(e,t,o,n)=>e>o?e:e<t?t:e===o?o+1:Math.min(o,e+n),zS=(e,t,o)=>Math.max(t,Math.min(o,e)),HS=e=>{const{min:t,max:o,range:n,value:s,step:r,snap:a,snapStart:i,rounded:l,hasMinEdge:c,hasMaxEdge:d,minBound:u,maxBound:m,screenRange:g}=e,p=c?t-1:t,h=d?o+1:o;if(s<u)return p;if(s>m)return h;{const e=((e,t,o)=>Math.min(o,Math.max(e,t))-t)(s,u,m),c=zS(e/g*n+t,p,h);return a&&c>=t&&c<=o?((e,t,o,n,s)=>s.fold((()=>{const s=e-t,r=Math.round(s/n)*n;return zS(t+r,t-1,o+1)}),(t=>{const s=(e-t)%n,r=Math.round(s/n),a=Math.floor((e-t)/n),i=Math.floor((o-t)/n),l=t+Math.min(i,a+r)*n;return Math.max(t,l)})))(c,t,o,r,i):l?Math.round(c):c}},PS=e=>{const{min:t,max:o,range:n,value:s,hasMinEdge:r,hasMaxEdge:a,maxBound:i,maxOffset:l,centerMinEdge:c,centerMaxEdge:d}=e;return s<t?r?0:c:s>o?a?i:d:(s-t)/n*l},NS="top",LS="right",WS="bottom",US="left",jS=e=>e.element.dom.getBoundingClientRect(),GS=(e,t)=>e[t],$S=e=>{const t=jS(e);return GS(t,US)},qS=e=>{const t=jS(e);return GS(t,LS)},XS=e=>{const t=jS(e);return GS(t,NS)},JS=e=>{const t=jS(e);return GS(t,WS)},YS=e=>{const t=jS(e);return GS(t,"width")},KS=e=>{const t=jS(e);return GS(t,"height")},ZS=(e,t,o)=>(e+t)/2-o,QS=(e,t)=>{const o=jS(e),n=jS(t),s=GS(o,US),r=GS(o,LS),a=GS(n,US);return ZS(s,r,a)},eC=(e,t)=>{const o=jS(e),n=jS(t),s=GS(o,NS),r=GS(o,WS),a=GS(n,NS);return ZS(s,r,a)},tC=(e,t)=>{Or(e,lS(),{value:t})},oC=(e,t,o)=>{const n={min:dS(t),max:pS(t),range:yS(t),value:o,step:CS(t),snap:kS(t),snapStart:OS(t),rounded:_S(t),hasMinEdge:ES(t),hasMaxEdge:MS(t),minBound:$S(e),maxBound:qS(e),screenRange:YS(e)};return HS(n)},nC=e=>(t,o)=>((e,t,o)=>{const n=(e>0?RS:VS)(DS(o),dS(o),pS(o),CS(o));return tC(t,n),M.some(n)})(e,t,o).map(T),sC=(e,t,o,n,s,r)=>{const a=((e,t,o,n,s)=>{const r=YS(e),a=n.bind((t=>M.some(QS(t,e)))).getOr(0),i=s.bind((t=>M.some(QS(t,e)))).getOr(r),l={min:dS(t),max:pS(t),range:yS(t),value:o,hasMinEdge:ES(t),hasMaxEdge:MS(t),minBound:$S(e),minOffset:0,maxBound:qS(e),maxOffset:r,centerMinEdge:a,centerMaxEdge:i};return PS(l)})(t,r,o,n,s);return $S(t)-$S(e)+a},rC=nC(-1),aC=nC(1),iC=M.none,lC=M.none,cC={"top-left":M.none(),top:M.none(),"top-right":M.none(),right:M.some(((e,t)=>{IS(e,fS(t))})),"bottom-right":M.none(),bottom:M.none(),"bottom-left":M.none(),left:M.some(((e,t)=>{IS(e,mS(t))}))};var dC=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=oC(e,t,o);return tC(e,n),n},setToMin:(e,t)=>{const o=dS(t);tC(e,o)},setToMax:(e,t)=>{const o=pS(t);tC(e,o)},findValueOfOffset:oC,getValueFromEvent:e=>cS(e).map((e=>e.left)),findPositionOfValue:sC,setPositionFromValue:(e,t,o,n)=>{const s=DS(o),r=sC(e,n.getSpectrum(e),s,n.getLeftEdge(e),n.getRightEdge(e),o),a=$t(t.element)/2;_t(t.element,"left",r-a+"px")},onLeft:rC,onRight:aC,onUp:iC,onDown:lC,edgeActions:cC});const uC=(e,t)=>{Or(e,lS(),{value:t})},mC=(e,t,o)=>{const n={min:uS(t),max:hS(t),range:xS(t),value:o,step:CS(t),snap:kS(t),snapStart:OS(t),rounded:_S(t),hasMinEdge:BS(t),hasMaxEdge:AS(t),minBound:XS(e),maxBound:JS(e),screenRange:KS(e)};return HS(n)},gC=e=>(t,o)=>((e,t,o)=>{const n=(e>0?RS:VS)(DS(o),uS(o),hS(o),CS(o));return uC(t,n),M.some(n)})(e,t,o).map(T),pC=(e,t,o,n,s,r)=>{const a=((e,t,o,n,s)=>{const r=KS(e),a=n.bind((t=>M.some(eC(t,e)))).getOr(0),i=s.bind((t=>M.some(eC(t,e)))).getOr(r),l={min:uS(t),max:hS(t),range:xS(t),value:o,hasMinEdge:BS(t),hasMaxEdge:AS(t),minBound:XS(e),minOffset:0,maxBound:JS(e),maxOffset:r,centerMinEdge:a,centerMaxEdge:i};return PS(l)})(t,r,o,n,s);return XS(t)-XS(e)+a},hC=M.none,fC=M.none,bC=gC(-1),vC=gC(1),yC={"top-left":M.none(),top:M.some(((e,t)=>{IS(e,gS(t))})),"top-right":M.none(),right:M.none(),"bottom-right":M.none(),bottom:M.some(((e,t)=>{IS(e,bS(t))})),"bottom-left":M.none(),left:M.none()};var xC=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=mC(e,t,o);return uC(e,n),n},setToMin:(e,t)=>{const o=uS(t);uC(e,o)},setToMax:(e,t)=>{const o=hS(t);uC(e,o)},findValueOfOffset:mC,getValueFromEvent:e=>cS(e).map((e=>e.top)),findPositionOfValue:pC,setPositionFromValue:(e,t,o,n)=>{const s=DS(o),r=pC(e,n.getSpectrum(e),s,n.getTopEdge(e),n.getBottomEdge(e),o),a=Ht(t.element)/2;_t(t.element,"top",r-a+"px")},onLeft:hC,onRight:fC,onUp:bC,onDown:vC,edgeActions:yC});const wC=(e,t)=>{Or(e,lS(),{value:t})},SC=(e,t)=>({x:e,y:t}),CC=(e,t)=>(o,n)=>((e,t,o,n)=>{const s=e>0?RS:VS,r=t?DS(n).x:s(DS(n).x,dS(n),pS(n),CS(n)),a=t?s(DS(n).y,uS(n),hS(n),CS(n)):DS(n).y;return wC(o,SC(r,a)),M.some(r)})(e,t,o,n).map(T),kC=CC(-1,!1),OC=CC(1,!1),_C=CC(-1,!0),TC=CC(1,!0),EC={"top-left":M.some(((e,t)=>{IS(e,FS(mS(t),gS(t)))})),top:M.some(((e,t)=>{IS(e,FS(wS(t),gS(t)))})),"top-right":M.some(((e,t)=>{IS(e,FS(fS(t),gS(t)))})),right:M.some(((e,t)=>{IS(e,FS(fS(t),SS(t)))})),"bottom-right":M.some(((e,t)=>{IS(e,FS(fS(t),bS(t)))})),bottom:M.some(((e,t)=>{IS(e,FS(wS(t),bS(t)))})),"bottom-left":M.some(((e,t)=>{IS(e,FS(mS(t),bS(t)))})),left:M.some(((e,t)=>{IS(e,FS(mS(t),SS(t)))}))};var MC=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=oC(e,t,o.left),s=mC(e,t,o.top),r=SC(n,s);return wC(e,r),r},setToMin:(e,t)=>{const o=dS(t),n=uS(t);wC(e,SC(o,n))},setToMax:(e,t)=>{const o=pS(t),n=hS(t);wC(e,SC(o,n))},getValueFromEvent:e=>cS(e),setPositionFromValue:(e,t,o,n)=>{const s=DS(o),r=sC(e,n.getSpectrum(e),s.x,n.getLeftEdge(e),n.getRightEdge(e),o),a=pC(e,n.getSpectrum(e),s.y,n.getTopEdge(e),n.getBottomEdge(e),o),i=$t(t.element)/2,l=Ht(t.element)/2;_t(t.element,"left",r-i+"px"),_t(t.element,"top",a-l+"px")},onLeft:kC,onRight:OC,onUp:_C,onDown:TC,edgeActions:EC});const BC=rm({name:"Slider",configFields:[us("stepSize",1),us("onChange",b),us("onChoose",b),us("onInit",b),us("onDragStart",b),us("onDragEnd",b),us("snapToGrid",!1),us("rounded",!0),ns("snapStart"),Jn("model",jn("mode",{x:[us("minX",0),us("maxX",100),$n("value",(e=>xs(e.mode.minX))),Xn("getInitialValue"),Oi("manager",dC)],y:[us("minY",0),us("maxY",100),$n("value",(e=>xs(e.mode.minY))),Xn("getInitialValue"),Oi("manager",xC)],xy:[us("minX",0),us("maxX",100),us("minY",0),us("maxY",100),$n("value",(e=>xs({x:e.mode.minX,y:e.mode.minY}))),Xn("getInitialValue"),Oi("manager",MC)]})),nu("sliderBehaviours",[Tp,ou]),$n("mouseIsDown",(()=>xs(!1)))],partFields:iS,factory:(e,t,o,n)=>{const s=t=>Gu(t,e,"thumb"),r=t=>Gu(t,e,"spectrum"),a=t=>ju(t,e,"left-edge"),i=t=>ju(t,e,"right-edge"),l=t=>ju(t,e,"top-edge"),c=t=>ju(t,e,"bottom-edge"),d=e.model,u=d.manager,m=(t,o)=>{u.setPositionFromValue(t,o,e,{getLeftEdge:a,getRightEdge:i,getTopEdge:l,getBottomEdge:c,getSpectrum:r})},g=(e,t)=>{d.value.set(t);const o=s(e);m(e,o)},p=t=>{const o=e.mouseIsDown.get();e.mouseIsDown.set(!1),o&&ju(t,e,"thumb").each((o=>{const n=d.value.get();e.onChoose(t,o,n)}))},h=(t,o)=>{o.stop(),e.mouseIsDown.set(!0),e.onDragStart(t,s(t))},f=(t,o)=>{o.stop(),e.onDragEnd(t,s(t)),p(t)};return{uid:e.uid,dom:e.dom,components:t,behaviours:ru(e.sliderBehaviours,[Tp.config({mode:"special",focusIn:t=>ju(t,e,"spectrum").map(Tp.focusIn).map(T)}),ou.config({store:{mode:"manual",getValue:e=>d.value.get(),setValue:g}}),yl.config({channels:{[Pd()]:{onReceive:p}}})]),events:Br([Fr(lS(),((t,o)=>{((t,o)=>{g(t,o);const n=s(t);e.onChange(t,n,o),M.some(!0)})(t,o.event.value)})),Lr(((t,o)=>{const n=d.getInitialValue();d.value.set(n);const a=s(t);m(t,a);const i=r(t);e.onInit(t,a,i,d.value.get())})),Fr(As(),h),Fr(Fs(),f),Fr(Vs(),h),Fr(Hs(),f)]),apis:{resetToMin:t=>{u.setToMin(t,e)},resetToMax:t=>{u.setToMax(t,e)},setValue:g,refresh:m},domModification:{styles:{position:"relative"}}}},apis:{setValue:(e,t,o)=>{e.setValue(t,o)},resetToMin:(e,t)=>{e.resetToMin(t)},resetToMax:(e,t)=>{e.resetToMax(t)},refresh:(e,t)=>{e.refresh(t)}}}),AC=Qr("rgb-hex-update"),DC=Qr("slider-update"),FC=Qr("palette-update"),IC="form",VC=[nu("formBehaviours",[ou])],RC=e=>"<alloy.field."+e+">",zC=(e,t)=>({uid:e.uid,dom:e.dom,components:t,behaviours:ru(e.formBehaviours,[ou.config({store:{mode:"manual",getValue:t=>{const o=qu(t,e);return ce(o,((e,t)=>e().bind((e=>{return o=cm.getCurrent(e),n=new Error(`Cannot find a current component to extract the value from for form part '${t}': `+Xr(e.element)),o.fold((()=>Ko.error(n)),Ko.value);var o,n})).map(ou.getValue)))},setValue:(t,o)=>{le(o,((o,n)=>{ju(t,e,n).each((e=>{cm.getCurrent(e).each((e=>{ou.setValue(e,o)}))}))}))}}})]),apis:{getField:(t,o)=>ju(t,e,o).bind(cm.getCurrent)}}),HC={getField:ha(((e,t,o)=>e.getField(t,o))),sketch:e=>{const t=(()=>{const e=[];return{field:(t,o)=>(e.push(t),Pu(IC,RC(t),o)),record:y(e)}})(),o=e(t),n=t.record(),s=P(n,(e=>Bu({name:e,pname:RC(e)})));return em(IC,VC,s,zC,o)}},PC=Qr("valid-input"),NC=Qr("invalid-input"),LC=Qr("validating-input"),WC="colorcustom.rgb.",UC=(e,t,o,n)=>{const s=(o,n)=>qw.config({invalidClass:t("invalid"),notify:{onValidate:e=>{Or(e,LC,{type:o})},onValid:e=>{Or(e,PC,{type:o,value:ou.getValue(e)})},onInvalid:e=>{Or(e,NC,{type:o,value:ou.getValue(e)})}},validator:{validate:t=>{const o=ou.getValue(t),s=n(o)?Ko.value(!0):Ko.error(e("aria.input.invalid"));return Rx(s)},validateOnLoad:!1}}),r=(o,n,r,a,i)=>{const l=e("colorcustom.rgb.range"),c=xw.parts.label({dom:{tag:"label",attributes:{"aria-label":a}},components:[Ga(r)]}),d=xw.parts.field({data:i,factory:yb,inputAttributes:{type:"text",..."hex"===n?{"aria-live":"polite"}:{}},inputClasses:[t("textfield")],inputBehaviours:gl([s(n,o),Cw.config({})]),onSetValue:e=>{qw.isInvalid(e)&&qw.run(e).get(b)}}),u=[c,d],m="hex"!==n?[xw.parts["aria-descriptor"]({text:l})]:[];return{dom:{tag:"div",attributes:{role:"presentation"}},components:u.concat(m)}},a=(e,t)=>{const o=t.red,n=t.green,s=t.blue;ou.setValue(e,{red:o,green:n,blue:s})},i=Bh({dom:{tag:"div",classes:[t("rgba-preview")],styles:{"background-color":"white"},attributes:{role:"presentation"}}}),l=(e,t)=>{i.getOpt(e).each((e=>{_t(e.element,"background-color","#"+t.value)}))},c=sm({factory:()=>{const s={red:xs(M.some(255)),green:xs(M.some(255)),blue:xs(M.some(255)),hex:xs(M.some("ffffff"))},c=e=>s[e].get(),d=(e,t)=>{s[e].set(t)},u=e=>{const t=e.red,o=e.green,n=e.blue;d("red",M.some(t)),d("green",M.some(o)),d("blue",M.some(n))},m=(e,t)=>{const o=t.event;"hex"!==o.type?d(o.type,M.none()):n(e)},g=(e,t)=>{const n=t.event;(e=>"hex"===e.type)(n)?((e,t)=>{o(e);const n=Ty(t);d("hex",M.some(t));const s=Wy(n);a(e,s),u(s),Or(e,AC,{hex:n}),l(e,n)})(e,n.value):((e,t,o)=>{const n=parseInt(o,10);d(t,M.some(n)),c("red").bind((e=>c("green").bind((t=>c("blue").map((o=>Py(e,t,o,1))))))).each((t=>{const o=((e,t)=>{const o=Fy(t);return HC.getField(e,"hex").each((t=>{Wp.isFocused(t)||ou.setValue(e,{hex:o.value})})),o})(e,t);Or(e,AC,{hex:o}),l(e,o)}))})(e,n.type,n.value)},p=t=>({label:e(WC+t+".label"),description:e(WC+t+".description")}),h=p("red"),f=p("green"),b=p("blue"),v=p("hex");return cn(HC.sketch((o=>({dom:{tag:"form",classes:[t("rgb-form")],attributes:{"aria-label":e("aria.color.picker")}},components:[o.field("red",xw.sketch(r(Ny,"red",h.label,h.description,255))),o.field("green",xw.sketch(r(Ny,"green",f.label,f.description,255))),o.field("blue",xw.sketch(r(Ny,"blue",b.label,b.description,255))),o.field("hex",xw.sketch(r(By,"hex",v.label,v.description,"ffffff"))),i.asSpec()],formBehaviours:gl([qw.config({invalidClass:t("form-invalid")}),zp("rgb-form-events",[Fr(PC,g),Fr(NC,m),Fr(LC,m)])])}))),{apis:{updateHex:(e,t)=>{ou.setValue(e,{hex:t.value}),((e,t)=>{const o=Wy(t);a(e,o),u(o)})(e,t),l(e,t)}}})},name:"RgbForm",configFields:[],apis:{updateHex:(e,t,o)=>{e.updateHex(t,o)}},extraApis:{}});return c},jC=(e,t)=>{const o=sm({name:"ColourPicker",configFields:[Xn("dom"),us("onValidHex",b),us("onInvalidHex",b)],factory:o=>{const n=UC(e,t,o.onValidHex,o.onInvalidHex),s=((e,t)=>{const o=BC.parts.spectrum({dom:{tag:"canvas",attributes:{role:"presentation"},classes:[t("sv-palette-spectrum")]}}),n=BC.parts.thumb({dom:{tag:"div",attributes:{role:"presentation"},classes:[t("sv-palette-thumb")],innerHtml:`<div class=${t("sv-palette-inner-thumb")} role="presentation"></div>`}}),s=(e,t)=>{const{width:o,height:n}=e,s=e.getContext("2d");if(null===s)return;s.fillStyle=t,s.fillRect(0,0,o,n);const r=s.createLinearGradient(0,0,o,0);r.addColorStop(0,"rgba(255,255,255,1)"),r.addColorStop(1,"rgba(255,255,255,0)"),s.fillStyle=r,s.fillRect(0,0,o,n);const a=s.createLinearGradient(0,0,0,n);a.addColorStop(0,"rgba(0,0,0,0)"),a.addColorStop(1,"rgba(0,0,0,1)"),s.fillStyle=a,s.fillRect(0,0,o,n)};return sm({factory:e=>{const r=y({x:0,y:0}),a=gl([cm.config({find:M.some}),Wp.config({})]);return BC.sketch({dom:{tag:"div",attributes:{role:"presentation"},classes:[t("sv-palette")]},model:{mode:"xy",getInitialValue:r},rounded:!1,components:[o,n],onChange:(e,t,o)=>{Or(e,FC,{value:o})},onInit:(e,t,o,n)=>{s(o.element.dom,Gy($y))},sliderBehaviours:a})},name:"SaturationBrightnessPalette",configFields:[],apis:{setHue:(e,t,o)=>{((e,t)=>{const o=e.components()[0].element.dom,n=Jy(t,100,100),r=Ly(n);s(o,Gy(r))})(t,o)},setThumb:(e,t,o)=>{((e,t)=>{const o=Yy(Wy(t));BC.setValue(e,{x:o.saturation,y:100-o.value})})(t,o)}},extraApis:{}})})(0,t),r={paletteRgba:xs($y),paletteHue:xs(0)},a=Bh(((e,t)=>{const o=BC.parts.spectrum({dom:{tag:"div",classes:[t("hue-slider-spectrum")],attributes:{role:"presentation"}}}),n=BC.parts.thumb({dom:{tag:"div",classes:[t("hue-slider-thumb")],attributes:{role:"presentation"}}});return BC.sketch({dom:{tag:"div",classes:[t("hue-slider")],attributes:{role:"presentation"}},rounded:!1,model:{mode:"y",getInitialValue:y(0)},components:[o,n],sliderBehaviours:gl([Wp.config({})]),onChange:(e,t,o)=>{Or(e,DC,{value:o})}})})(0,t)),i=Bh(s.sketch({})),l=Bh(n.sketch({})),c=(e,t,o)=>{i.getOpt(e).each((e=>{s.setHue(e,o)}))},d=(e,t)=>{l.getOpt(e).each((e=>{n.updateHex(e,t)}))},u=(e,t,o)=>{a.getOpt(e).each((e=>{BC.setValue(e,(e=>100-e/360*100)(o))}))},m=(e,t)=>{i.getOpt(e).each((e=>{s.setThumb(e,t)}))},g=(e,t,o,n)=>{((e,t)=>{const o=Wy(e);r.paletteRgba.set(o),r.paletteHue.set(t)})(t,o),N(n,(n=>{n(e,t,o)}))};return{uid:o.uid,dom:o.dom,components:[i.asSpec(),a.asSpec(),l.asSpec()],behaviours:gl([zp("colour-picker-events",[Fr(AC,(()=>{const e=[c,u,m];return(t,o)=>{const n=o.event.hex,s=(e=>Yy(Wy(e)))(n);g(t,n,s.hue,e)}})()),Fr(FC,(()=>{const e=[d];return(t,o)=>{const n=o.event.value,s=r.paletteHue.get(),a=Jy(s,n.x,100-n.y),i=Ky(a);g(t,i,s,e)}})()),Fr(DC,(()=>{const e=[c,d];return(t,o)=>{const n=(e=>(100-e)/100*360)(o.event.value),s=r.paletteRgba.get(),a=Yy(s),i=Jy(n,a.saturation,a.value),l=Ky(i);g(t,l,n,e)}})())]),cm.config({find:e=>l.getOpt(e)}),Tp.config({mode:"acyclic"})])}}});return o},GC=()=>cm.config({find:M.some}),$C=e=>cm.config({find:t=>st(t.element,e).bind((e=>t.getSystem().getByDom(e).toOptional()))}),qC=kn([us("preprocess",x),us("postprocess",x)]),XC=(e,t,o)=>ou.config({store:{mode:"manual",...e.map((e=>({initialValue:e}))).getOr({}),getValue:t,setValue:o}}),JC=(e,t,o)=>XC(e,(e=>t(e.element)),((e,t)=>o(e.element,t))),YC=(e,t)=>{const o=Wn("RepresentingConfigs.memento processors",qC,t);return ou.config({store:{mode:"manual",getValue:t=>{const n=e.get(t),s=ou.getValue(n);return o.postprocess(s)},setValue:(t,n)=>{const s=o.preprocess(n),r=e.get(t);ou.setValue(r,s)}}})},KC=JC,ZC=XC,QC=e=>ou.config({store:{mode:"memory",initialValue:e}}),ek={"colorcustom.rgb.red.label":"R","colorcustom.rgb.red.description":"Red component","colorcustom.rgb.green.label":"G","colorcustom.rgb.green.description":"Green component","colorcustom.rgb.blue.label":"B","colorcustom.rgb.blue.description":"Blue component","colorcustom.rgb.hex.label":"#","colorcustom.rgb.hex.description":"Hex color code","colorcustom.rgb.range":"Range 0 to 255","aria.color.picker":"Color Picker","aria.input.invalid":"Invalid input"};var tk=tinymce.util.Tools.resolve("tinymce.Resource"),ok=tinymce.util.Tools.resolve("tinymce.util.Tools");const nk=Qr("alloy-fake-before-tabstop"),sk=Qr("alloy-fake-after-tabstop"),rk=e=>({dom:{tag:"div",styles:{width:"1px",height:"1px",outline:"none"},attributes:{tabindex:"0"},classes:e},behaviours:gl([Wp.config({ignore:!0}),Cw.config({})])}),ak=e=>({dom:{tag:"div",classes:["tox-navobj"]},components:[rk([nk]),e,rk([sk])],behaviours:gl([$C(1)])}),ik=(e,t)=>{Or(e,Ws(),{raw:{which:9,shiftKey:t}})},lk=(e,t)=>{const o=t.element;Fa(o,nk)?ik(e,!0):Fa(o,sk)&&ik(e,!1)},ck=e=>aw(e,["."+nk,"."+sk].join(","),_),dk=Qr("toolbar.button.execute"),uk={[nr()]:["disabling","alloy.base.behaviour","toggling","toolbar-button-events"]},mk=(e,t,o)=>Nh(e,{tag:"span",classes:["tox-icon","tox-tbtn__icon-wrap"],behaviours:o},t),gk=(e,t)=>mk(e,t,[]),pk=(e,t)=>mk(e,t,[Rp.config({})]),hk=(e,t,o)=>({dom:{tag:"span",classes:[`${t}__select-label`]},components:[Ga(o.translate(e))],behaviours:gl([Rp.config({})])}),fk=Qr("update-menu-text"),bk=Qr("update-menu-icon"),vk=(e,t,o)=>{const n=xs(b),s=e.text.map((e=>Bh(hk(e,t,o.providers)))),r=e.icon.map((e=>Bh(pk(e,o.providers.icons)))),a=(e,t)=>{const o=ou.getValue(e);return Wp.focus(o),Or(o,"keydown",{raw:t.event.raw}),Yx.close(o),M.some(!0)},i=e.role.fold((()=>({})),(e=>({role:e}))),l=e.tooltip.fold((()=>({})),(e=>{const t=o.providers.translate(e);return{title:t,"aria-label":t}})),c=Nh("chevron-down",{tag:"div",classes:[`${t}__select-chevron`]},o.providers.icons),d=Bh(Yx.sketch({...e.uid?{uid:e.uid}:{},...i,dom:{tag:"button",classes:[t,`${t}--select`].concat(P(e.classes,(e=>`${t}--${e}`))),attributes:{...l}},components:uy([r.map((e=>e.asSpec())),s.map((e=>e.asSpec())),M.some(c)]),matchWidth:!0,useMinWidth:!0,onOpen:(t,o,n)=>{e.searchable&&(e=>{Cb(e).each((e=>Wp.focus(e)))})(n)},dropdownBehaviours:gl([...e.dropdownBehaviours,ny((()=>e.disabled||o.providers.isDisabled())),oy(),Xw.config({}),Rp.config({}),zp("dropdown-events",[iy(e,n),ly(e,n)]),zp("menubutton-update-display-text",[Fr(fk,((e,t)=>{s.bind((t=>t.getOpt(e))).each((e=>{Rp.set(e,[Ga(o.providers.translate(t.event.text))])}))})),Fr(bk,((e,t)=>{r.bind((t=>t.getOpt(e))).each((e=>{Rp.set(e,[pk(t.event.icon,o.providers.icons)])}))}))])]),eventOrder:cn(uk,{mousedown:["focusing","alloy.base.behaviour","item-type-events","normal-dropdown-events"]}),sandboxBehaviours:gl([Tp.config({mode:"special",onLeft:a,onRight:a}),zp("dropdown-sandbox-events",[Fr(xb,((e,t)=>{(e=>{const t=ou.getValue(e),o=Sb(e).map(kb);Yx.refetch(t).get((()=>{const e=Bx.getCoupled(t,"sandbox");o.each((t=>Sb(e).each((e=>((e,t)=>{ou.setValue(e,t.fetchPattern),e.element.dom.selectionStart=t.selectionStart,e.element.dom.selectionEnd=t.selectionEnd})(e,t)))))}))})(e),t.stop()})),Fr(wb,((e,t)=>{((e,t)=>{(e=>Rd.getState(e).bind(Fm.getHighlighted).bind(Fm.getHighlighted))(e).each((o=>{((e,t,o,n)=>{const s={...n,target:t};e.getSystem().triggerEvent(o,t,s)})(e,o.element,t.event.eventType,t.event.interactionEvent)}))})(e,t),t.stop()}))])]),lazySink:o.getSink,toggleClass:`${t}--active`,parts:{menu:{...pb(0,e.columns,e.presets),fakeFocus:e.searchable,onHighlightItem:Kx,onCollapseMenu:(e,t,o)=>{Fm.getHighlighted(o).each((t=>{Kx(e,o,t)}))},onDehighlightItem:Zx}},fetch:t=>Vx(S(e.fetch,t))}));return d.asSpec()},yk=e=>"separator"===e.type,xk={type:"separator"},wk=(e,t)=>{const o=((e,t)=>{const o=j(e,((e,o)=>(e=>r(e))(o)?""===o?e:"|"===o?e.length>0&&!yk(e[e.length-1])?e.concat([xk]):e:ve(t,o.toLowerCase())?e.concat([t[o.toLowerCase()]]):e:e.concat([o])),[]);return o.length>0&&yk(o[o.length-1])&&o.pop(),o})(r(e)?e.split(" "):e,t);return U(o,((e,o)=>{if((e=>ve(e,"getSubmenuItems"))(o)){const n=(e=>{const t=be(e,"value").getOrThunk((()=>Qr("generated-menu-item")));return cn({value:t},e)})(o),s=((e,t)=>{const o=e.getSubmenuItems(),n=wk(o,t);return{item:e,menus:cn(n.menus,{[e.value]:n.items}),expansions:cn(n.expansions,{[e.value]:e.value})}})(n,t);return{menus:cn(e.menus,s.menus),items:[s.item,...e.items],expansions:cn(e.expansions,s.expansions)}}return{...e,items:[o,...e.items]}}),{menus:{},expansions:{},items:[]})},Sk=(e,t,o,n)=>{const s=Qr("primary-menu"),r=wk(e,o.shared.providers.menuItems());if(0===r.items.length)return M.none();const a=(e=>e.search.fold((()=>({searchMode:"no-search"})),(e=>({searchMode:"search-with-field",placeholder:e.placeholder}))))(n),i=nw(s,r.items,t,o,n.isHorizontalMenu,a),l=(e=>e.search.fold((()=>({searchMode:"no-search"})),(e=>({searchMode:"search-with-results"}))))(n),c=ce(r.menus,((e,n)=>nw(n,e,t,o,!1,l))),d=cn(c,Ss(s,i));return M.from(_h.tieredData(s,d,r.expansions))},Ck=e=>!ve(e,"items"),kk="data-value",Ok=(e,t,o,n)=>P(o,(o=>Ck(o)?{type:"togglemenuitem",text:o.text,value:o.value,active:o.value===n,onAction:()=>{ou.setValue(e,o.value),Or(e,Mw,{name:t}),Wp.focus(e)}}:{type:"nestedmenuitem",text:o.text,getSubmenuItems:()=>Ok(e,t,o.items,n)})),_k=(e,t)=>re(e,(e=>Ck(e)?Ce(e.value===t,e):_k(e.items,t))),Tk=sm({name:"HtmlSelect",configFields:[Xn("options"),nu("selectBehaviours",[Wp,ou]),us("selectClasses",[]),us("selectAttributes",{}),ns("data")],factory:(e,t)=>{const o=P(e.options,(e=>({dom:{tag:"option",value:e.value,innerHtml:e.text}}))),n=e.data.map((e=>Ss("initialValue",e))).getOr({});return{uid:e.uid,dom:{tag:"select",classes:e.selectClasses,attributes:e.selectAttributes},components:o,behaviours:ru(e.selectBehaviours,[Wp.config({}),ou.config({store:{mode:"manual",getValue:e=>Ra(e.element),setValue:(t,o)=>{G(e.options,(e=>e.value===o)).isSome()&&za(t.element,o)},...n}})])}}}),Ek=y([us("field1Name","field1"),us("field2Name","field2"),Ci("onLockedChange"),yi(["lockClass"]),us("locked",!1),au("coupledFieldBehaviours",[cm,ou])]),Mk=(e,t)=>Bu({factory:xw,name:e,overrides:e=>({fieldBehaviours:gl([zp("coupled-input-behaviour",[Fr(js(),(o=>{((e,t,o)=>ju(e,t,o).bind(cm.getCurrent))(o,e,t).each((t=>{ju(o,e,"lock").each((n=>{Yp.isOn(n)&&e.onLockedChange(o,t,n)}))}))}))])])})}),Bk=y([Mk("field1","field2"),Mk("field2","field1"),Bu({factory:Mh,schema:[Xn("dom")],name:"lock",overrides:e=>({buttonBehaviours:gl([Yp.config({selected:e.locked,toggleClass:e.markers.lockClass,aria:{mode:"pressed"}})])})})]),Ak=rm({name:"FormCoupledInputs",configFields:Ek(),partFields:Bk(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:iu(e.coupledFieldBehaviours,[cm.config({find:M.some}),ou.config({store:{mode:"manual",getValue:t=>{const o=Ju(t,e,["field1","field2"]);return{[e.field1Name]:ou.getValue(o.field1()),[e.field2Name]:ou.getValue(o.field2())}},setValue:(t,o)=>{const n=Ju(t,e,["field1","field2"]);ye(o,e.field1Name)&&ou.setValue(n.field1(),o[e.field1Name]),ye(o,e.field2Name)&&ou.setValue(n.field2(),o[e.field2Name])}}})]),apis:{getField1:t=>ju(t,e,"field1"),getField2:t=>ju(t,e,"field2"),getLock:t=>ju(t,e,"lock")}}),apis:{getField1:(e,t)=>e.getField1(t),getField2:(e,t)=>e.getField2(t),getLock:(e,t)=>e.getLock(t)}}),Dk=e=>{const t=/^\s*(\d+(?:\.\d+)?)\s*(|cm|mm|in|px|pt|pc|em|ex|ch|rem|vw|vh|vmin|vmax|%)\s*$/.exec(e);if(null!==t){const e=parseFloat(t[1]),o=t[2];return Ko.value({value:e,unit:o})}return Ko.error(e)},Fk=(e,t)=>{const o={"":96,px:96,pt:72,cm:2.54,pc:12,mm:25.4,in:1},n=e=>ve(o,e);return e.unit===t?M.some(e.value):n(e.unit)&&n(t)?o[e.unit]===o[t]?M.some(e.value):M.some(e.value/o[e.unit]*o[t]):M.none()},Ik=e=>M.none(),Vk=(e,t)=>{const o=e.label.map((e=>Ew(e,t))),n=[Cm.config({disabled:()=>e.disabled||t.isDisabled()}),oy(),Tp.config({mode:"execution",useEnter:!0!==e.multiline,useControlEnter:!0===e.multiline,execute:e=>(kr(e,Fw),M.some(!0))}),zp("textfield-change",[Fr(js(),((t,o)=>{Or(t,Mw,{name:e.name})})),Fr(tr(),((t,o)=>{Or(t,Mw,{name:e.name})}))]),Cw.config({})],s=e.validation.map((e=>qw.config({getRoot:e=>tt(e.element),invalidClass:"tox-invalid",validator:{validate:t=>{const o=ou.getValue(t),n=e.validator(o);return Rx(!0===n?Ko.value(o):Ko.error(n))},validateOnLoad:e.validateOnLoad}}))).toArray(),r={...e.placeholder.fold(y({}),(e=>({placeholder:t.translate(e)}))),...e.inputMode.fold(y({}),(e=>({inputmode:e})))},a=xw.parts.field({tag:!0===e.multiline?"textarea":"input",...e.data.map((e=>({data:e}))).getOr({}),inputAttributes:r,inputClasses:[e.classname],inputBehaviours:gl(q([n,s])),selectOnFocus:!1,factory:yb}),i=(e.flex?["tox-form__group--stretched"]:[]).concat(e.maximized?["tox-form-group--maximize"]:[]),l=[Cm.config({disabled:()=>e.disabled||t.isDisabled(),onDisabled:e=>{xw.getField(e).each(Cm.disable)},onEnabled:e=>{xw.getField(e).each(Cm.enable)}}),oy()];return Ow(o,a,i,l)};var Rk=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.stream.streams.setup(e,t);return Br([Fr(e.event,o),Wr((()=>t.cancel()))].concat(e.cancelEvent.map((e=>[Fr(e,(()=>t.cancel()))])).getOr([])))}});const zk=(e,t)=>{let o=null;const n=()=>{c(o)||(clearTimeout(o),o=null)};return{cancel:n,throttle:(...s)=>{n(),o=setTimeout((()=>{o=null,e.apply(null,s)}),t)}}},Hk=e=>{const t=xs(null);return ba({readState:()=>({timer:null!==t.get()?"set":"unset"}),setTimer:e=>{t.set(e)},cancel:()=>{const e=t.get();null!==e&&e.cancel()}})};var Pk=Object.freeze({__proto__:null,throttle:Hk,init:e=>e.stream.streams.state(e)}),Nk=[Jn("stream",jn("mode",{throttle:[Xn("delay"),us("stopEvent",!0),Oi("streams",{setup:(e,t)=>{const o=e.stream,n=zk(e.onStream,o.delay);return t.setTimer(n),(e,t)=>{n.throttle(e,t),o.stopEvent&&t.stop()}},state:Hk})]})),us("event","input"),ns("cancelEvent"),Ci("onStream")];const Lk=hl({fields:Nk,name:"streaming",active:Rk,state:Pk}),Wk=(e,t,o)=>{const n=ou.getValue(o);ou.setValue(t,n),jk(t)},Uk=(e,t)=>{const o=e.element,n=Ra(o),s=o.dom;"number"!==xt(o,"type")&&t(s,n)},jk=e=>{Uk(e,((e,t)=>e.setSelectionRange(t.length,t.length)))},Gk=y("alloy.typeahead.itemexecute"),$k=y([ns("lazySink"),Xn("fetch"),us("minChars",5),us("responseTime",1e3),wi("onOpen"),us("getHotspot",M.some),us("getAnchorOverrides",y({})),us("layouts",M.none()),us("eventOrder",{}),ys("model",{},[us("getDisplayText",(e=>void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.value)),us("selectsOver",!0),us("populateFromBrowse",!0)]),wi("onSetValue"),Si("onExecute"),wi("onItemExecute"),us("inputClasses",[]),us("inputAttributes",{}),us("inputStyles",{}),us("matchWidth",!0),us("useMinWidth",!1),us("dismissOnBlur",!0),yi(["openClass"]),ns("initialData"),nu("typeaheadBehaviours",[Wp,ou,Lk,Tp,Yp,Bx]),$n("lazyTypeaheadComp",(()=>xs(M.none))),$n("previewing",(()=>xs(!0)))].concat(hb()).concat(qx())),qk=y([Au({schema:[vi()],name:"menu",overrides:e=>({fakeFocus:!0,onHighlightItem:(t,o,n)=>{e.previewing.get()?e.lazyTypeaheadComp.get().each((t=>{((e,t,o)=>{if(e.selectsOver){const n=ou.getValue(t),s=e.getDisplayText(n),r=ou.getValue(o);return 0===e.getDisplayText(r).indexOf(s)?M.some((()=>{Wk(0,t,o),((e,t)=>{Uk(e,((e,o)=>e.setSelectionRange(t,o.length)))})(t,s.length)})):M.none()}return M.none()})(e.model,t,n).fold((()=>{e.model.selectsOver?(Fm.dehighlight(o,n),e.previewing.set(!0)):e.previewing.set(!1)}),(t=>{t(),e.previewing.set(!1)}))})):e.lazyTypeaheadComp.get().each((t=>{e.model.populateFromBrowse&&Wk(e.model,t,n)}))},onExecute:(t,o)=>e.lazyTypeaheadComp.get().map((e=>(Or(e,Gk(),{item:o}),!0))),onHover:(t,o)=>{e.previewing.set(!1),e.lazyTypeaheadComp.get().each((t=>{e.model.populateFromBrowse&&Wk(e.model,t,o)}))}})})]),Xk=rm({name:"Typeahead",configFields:$k(),partFields:qk(),factory:(e,t,o,n)=>{const s=(t,o,s)=>{e.previewing.set(!1);const r=Bx.getCoupled(t,"sandbox");if(Rd.isOpen(r))cm.getCurrent(r).each((e=>{Fm.getHighlighted(e).fold((()=>{s(e)}),(()=>{Mr(r,e.element,"keydown",o)}))}));else{const o=e=>{cm.getCurrent(e).each(s)};Nx(e,a(t),t,r,n,o,kh.HighlightMenuAndItem).get(b)}},r=fb(e),a=e=>t=>t.map((t=>{const o=fe(t.menus),n=X(o,(e=>W(e.items,(e=>"item"===e.type))));return ou.getState(e).update(P(n,(e=>e.data))),t})),i=e=>cm.getCurrent(e),l="typeaheadevents",c=[Wp.config({}),ou.config({onSetValue:e.onSetValue,store:{mode:"dataset",getDataKey:e=>Ra(e.element),getFallbackEntry:e=>({value:e,meta:{}}),setValue:(t,o)=>{za(t.element,e.model.getDisplayText(o))},...e.initialData.map((e=>Ss("initialValue",e))).getOr({})}}),Lk.config({stream:{mode:"throttle",delay:e.responseTime,stopEvent:!1},onStream:(t,o)=>{const s=Bx.getCoupled(t,"sandbox");if(Wp.isFocused(t)&&Ra(t.element).length>=e.minChars){const o=i(s).bind((e=>Fm.getHighlighted(e).map(ou.getValue)));e.previewing.set(!0);const r=t=>{i(s).each((t=>{o.fold((()=>{e.model.selectsOver&&Fm.highlightFirst(t)}),(e=>{Fm.highlightBy(t,(t=>ou.getValue(t).value===e.value)),Fm.getHighlighted(t).orThunk((()=>(Fm.highlightFirst(t),M.none())))}))}))};Nx(e,a(t),t,s,n,r,kh.HighlightJustMenu).get(b)}},cancelEvent:lr()}),Tp.config({mode:"special",onDown:(e,t)=>(s(e,t,Fm.highlightFirst),M.some(!0)),onEscape:e=>{const t=Bx.getCoupled(e,"sandbox");return Rd.isOpen(t)?(Rd.close(t),M.some(!0)):M.none()},onUp:(e,t)=>(s(e,t,Fm.highlightLast),M.some(!0)),onEnter:t=>{const o=Bx.getCoupled(t,"sandbox"),n=Rd.isOpen(o);if(n&&!e.previewing.get())return i(o).bind((e=>Fm.getHighlighted(e))).map((e=>(Or(t,Gk(),{item:e}),!0)));{const s=ou.getValue(t);return kr(t,lr()),e.onExecute(o,t,s),n&&Rd.close(o),M.some(!0)}}}),Yp.config({toggleClass:e.markers.openClass,aria:{mode:"expanded"}}),Bx.config({others:{sandbox:t=>Gx(e,t,{onOpen:()=>Yp.on(t),onClose:()=>Yp.off(t)})}}),zp(l,[Lr((t=>{e.lazyTypeaheadComp.set(M.some(t))})),Wr((t=>{e.lazyTypeaheadComp.set(M.none())})),jr((t=>{const o=b;Wx(e,a(t),t,n,o,kh.HighlightMenuAndItem).get(b)})),Fr(Gk(),((t,o)=>{const n=Bx.getCoupled(t,"sandbox");Wk(e.model,t,o.event.item),kr(t,lr()),e.onItemExecute(t,n,o.event.item,ou.getValue(t)),Rd.close(n),jk(t)}))].concat(e.dismissOnBlur?[Fr(er(),(e=>{const t=Bx.getCoupled(e,"sandbox");kl(t.element).isNone()&&Rd.close(t)}))]:[]))],d={[hr()]:[ou.name(),Lk.name(),l],...e.eventOrder};return{uid:e.uid,dom:vb(cn(e,{inputAttributes:{role:"combobox","aria-autocomplete":"list","aria-haspopup":"true"}})),behaviours:{...r,...ru(e.typeaheadBehaviours,c)},eventOrder:d}}}),Jk=e=>({...e,toCached:()=>Jk(e.toCached()),bindFuture:t=>Jk(e.bind((e=>e.fold((e=>Rx(Ko.error(e))),(e=>t(e)))))),bindResult:t=>Jk(e.map((e=>e.bind(t)))),mapResult:t=>Jk(e.map((e=>e.map(t)))),mapError:t=>Jk(e.map((e=>e.mapError(t)))),foldResult:(t,o)=>e.map((e=>e.fold(t,o))),withTimeout:(t,o)=>Jk(Vx((n=>{let s=!1;const r=setTimeout((()=>{s=!0,n(Ko.error(o()))}),t);e.get((e=>{s||(clearTimeout(r),n(e))}))})))}),Yk=e=>Jk(Vx(e)),Kk=e=>({isEnabled:()=>!Cm.isDisabled(e),setEnabled:t=>Cm.set(e,!t),setActive:t=>{const o=e.element;t?(Aa(o,"tox-tbtn--enabled"),vt(o,"aria-pressed",!0)):(Da(o,"tox-tbtn--enabled"),Ct(o,"aria-pressed"))},isActive:()=>Fa(e.element,"tox-tbtn--enabled")}),Zk=(e,t,o,n)=>vk({text:e.text,icon:e.icon,tooltip:e.tooltip,searchable:e.search.isSome(),role:n,fetch:(t,n)=>{const s={pattern:e.search.isSome()?Qx(t):""};e.fetch((t=>{n(Sk(t,Kf.CLOSE_ON_EXECUTE,o,{isHorizontalMenu:!1,search:e.search}))}),s)},onSetup:e.onSetup,getApi:Kk,columns:1,presets:"normal",classes:[],dropdownBehaviours:[Cw.config({})]},t,o.shared),Qk=(e,t,o)=>{const n=e=>n=>{const s=!n.isActive();n.setActive(s),e.storage.set(s),o.shared.getSink().each((o=>{t().getOpt(o).each((t=>{wl(t.element),Or(t,Dw,{name:e.name,value:e.storage.get()})}))}))},s=e=>t=>{t.setActive(e.storage.get())};return t=>{t(P(e,(e=>{const t=e.text.fold((()=>({})),(e=>({text:e})));return{type:e.type,active:!1,...t,onAction:n(e),onSetup:s(e)}})))}},eO=(e,t,o=[],n,s,r)=>{const a=t.fold((()=>({})),(e=>({action:e}))),i={buttonBehaviours:gl([ny((()=>!e.enabled||r.isDisabled())),oy(),Cw.config({}),zp("button press",[Dr("click"),Dr("mousedown")])].concat(o)),eventOrder:{click:["button press","alloy.base.behaviour"],mousedown:["button press","alloy.base.behaviour"]},...a},l=cn(i,{dom:n});return cn(l,{components:s})},tO=(e,t,o,n=[])=>{const s={tag:"button",classes:["tox-tbtn"],attributes:e.tooltip.map((e=>({"aria-label":o.translate(e),title:o.translate(e)}))).getOr({})},r=e.icon.map((e=>gk(e,o.icons))),a=uy([r]);return eO(e,t,n,s,a,o)},oO=(e,t,o,n=[],s=[])=>{const r=o.translate(e.text),a=e.icon.map((e=>gk(e,o.icons))),i=[a.getOrThunk((()=>Ga(r)))],l=[...(e=>{switch(e){case"primary":return["tox-button"];case"toolbar":return["tox-tbtn"];default:return["tox-button","tox-button--secondary"]}})(e.buttonType.getOr(e.primary||e.borderless?"primary":"secondary")),...a.isSome()?["tox-button--icon"]:[],...e.borderless?["tox-button--naked"]:[],...s];return eO(e,t,n,{tag:"button",classes:l,attributes:{title:r}},i,o)},nO=(e,t,o,n=[],s=[])=>{const r=oO(e,M.some(t),o,n,s);return Mh.sketch(r)},sO=(e,t)=>o=>{"custom"===t?Or(o,Dw,{name:e,value:{}}):"submit"===t?kr(o,Fw):"cancel"===t?kr(o,Aw):console.error("Unknown button type: ",t)},rO=(e,t,o)=>{if(((e,t)=>"menu"===t)(0,t)){const t=()=>r,n=e,s={...e,type:"menubutton",search:M.none(),onSetup:t=>(t.setEnabled(e.enabled),b),fetch:Qk(n.items,t,o)},r=Bh(Zk(s,"tox-tbtn",o,M.none()));return r.asSpec()}if(((e,t)=>"custom"===t||"cancel"===t||"submit"===t)(0,t)){const n=sO(e.name,t),s={...e,borderless:!1};return nO(s,n,o.shared.providers,[])}throw console.error("Unknown footer button type: ",t),new Error("Unknown footer button type")},aO={type:"separator"},iO=e=>({type:"menuitem",value:e.url,text:e.title,meta:{attach:e.attach},onAction:b}),lO=(e,t)=>({type:"menuitem",value:t,text:e,meta:{attach:void 0},onAction:b}),cO=(e,t)=>(e=>P(e,iO))(((e,t)=>W(t,(t=>t.type===e)))(e,t)),dO=e=>cO("header",e.targets),uO=e=>cO("anchor",e.targets),mO=e=>M.from(e.anchorTop).map((e=>lO("<top>",e))).toArray(),gO=e=>M.from(e.anchorBottom).map((e=>lO("<bottom>",e))).toArray(),pO=(e,t)=>{const o=e.toLowerCase();return W(t,(e=>{var t;const n=void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.text,s=null!==(t=e.value)&&void 0!==t?t:"";return Oe(n.toLowerCase(),o)||Oe(s.toLowerCase(),o)}))},hO=Qr("aria-invalid"),fO=(e,t)=>{e.dom.checked=t},bO=e=>e.dom.checked,vO=e=>(t,o,n,s)=>be(o,"name").fold((()=>e(o,s,M.none())),(r=>t.field(r,e(o,s,be(n,r))))),yO={bar:vO(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-bar","tox-form__controls-h-stack"]},components:P(e.items,t.interpreter)}))(e,t.shared))),collection:vO(((e,t,o)=>((e,t,o)=>{const n=e.label.map((e=>Ew(e,t))),s=e=>(t,o)=>{ri(o.event.target,"[data-collection-item-value]").each((n=>{e(t,o,n,xt(n,"data-collection-item-value"))}))},r=s(((o,n,s,r)=>{n.stop(),t.isDisabled()||Or(o,Dw,{name:e.name,value:r})})),a=[Fr(Ps(),s(((e,t,o)=>{wl(o)}))),Fr($s(),r),Fr(rr(),r),Fr(Ns(),s(((e,t,o)=>{si(e.element,"."+rb).each((e=>{Da(e,rb)})),Aa(o,rb)}))),Fr(Ls(),s((e=>{si(e.element,"."+rb).each((e=>{Da(e,rb)}))}))),jr(s(((t,o,n,s)=>{Or(t,Dw,{name:e.name,value:s})})))],i=(e,t)=>P(Rc(e.element,".tox-collection__item"),t),l=xw.parts.field({dom:{tag:"div",classes:["tox-collection"].concat(1!==e.columns?["tox-collection--grid"]:["tox-collection--list"])},components:[],factory:{sketch:x},behaviours:gl([Cm.config({disabled:t.isDisabled,onDisabled:e=>{i(e,(e=>{Aa(e,"tox-collection__item--state-disabled"),vt(e,"aria-disabled",!0)}))},onEnabled:e=>{i(e,(e=>{Da(e,"tox-collection__item--state-disabled"),Ct(e,"aria-disabled")}))}}),oy(),Rp.config({}),ou.config({store:{mode:"memory",initialValue:o.getOr([])},onSetValue:(o,n)=>{((o,n)=>{const s=P(n,(o=>{const n=Ah.translate(o.text),s=1===e.columns?`<div class="tox-collection__item-label">${n}</div>`:"",r=`<div class="tox-collection__item-icon">${o.icon}</div>`,a={_:" "," - ":" ","-":" "},i=n.replace(/\_| \- |\-/g,(e=>a[e]));return`<div class="tox-collection__item${t.isDisabled()?" tox-collection__item--state-disabled":""}" tabindex="-1" data-collection-item-value="${kw.encodeAllRaw(o.value)}" title="${i}" aria-label="${i}">${r}${s}</div>`})),r="auto"!==e.columns&&e.columns>1?H(s,e.columns):[s],a=P(r,(e=>`<div class="tox-collection__group">${e.join("")}</div>`));$r(o.element,a.join(""))})(o,n),"auto"===e.columns&&Wv(o,5,"tox-collection__item").each((({numRows:e,numColumns:t})=>{Tp.setGridSize(o,e,t)})),kr(o,zw)}}),Cw.config({}),Tp.config((c=e.columns,1===c?{mode:"menu",moveOnTab:!1,selector:".tox-collection__item"}:"auto"===c?{mode:"flatgrid",selector:".tox-collection__item",initSize:{numColumns:1,numRows:1}}:{mode:"matrix",selectors:{row:".tox-collection__group",cell:`.${Qf}`}})),zp("collection-events",a)]),eventOrder:{[nr()]:["disabling","alloy.base.behaviour","collection-events"]}});var c;return Ow(n,l,["tox-form__group--collection"],[])})(e,t.shared.providers,o))),alertbanner:vO(((e,t)=>((e,t)=>fw.sketch({dom:{tag:"div",attributes:{role:"alert"},classes:["tox-notification","tox-notification--in",`tox-notification--${e.level}`]},components:[{dom:{tag:"div",classes:["tox-notification__icon"]},components:[Mh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--naked","tox-button--icon"],innerHtml:zh(e.icon,t.icons),attributes:{title:t.translate(e.iconTooltip)}},action:t=>{Or(t,Dw,{name:"alert-banner",value:e.url})},buttonBehaviours:gl([Hh()])})]},{dom:{tag:"div",classes:["tox-notification__body"],innerHtml:t.translate(e.text)}}]}))(e,t.shared.providers))),input:vO(((e,t,o)=>((e,t,o)=>Vk({name:e.name,multiline:!1,label:e.label,inputMode:e.inputMode,placeholder:e.placeholder,flex:!1,disabled:!e.enabled,classname:"tox-textfield",validation:M.none(),maximized:e.maximized,data:o},t))(e,t.shared.providers,o))),textarea:vO(((e,t,o)=>((e,t,o)=>Vk({name:e.name,multiline:!0,label:e.label,inputMode:M.none(),placeholder:e.placeholder,flex:!0,disabled:!e.enabled,classname:"tox-textarea",validation:M.none(),maximized:e.maximized,data:o},t))(e,t.shared.providers,o))),label:vO(((e,t)=>((e,t)=>{return{dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"label",classes:["tox-label"]},components:[Ga(t.providers.translate(e.label))]},...P(e.items,t.interpreter)],behaviours:gl([GC(),Rp.config({}),(o=M.none(),JC(o,Gr,$r)),Tp.config({mode:"acyclic"})])};var o})(e,t.shared))),iframe:($_=(e,t,o)=>((e,t,o)=>{const n=e.sandboxed,s=e.transparent,r="tox-dialog__iframe",a={...e.label.map((e=>({title:e}))).getOr({}),...o.map((e=>({srcdoc:e}))).getOr({}),...n?{sandbox:"allow-scripts allow-same-origin"}:{}},i=(e=>{const t=xs(e.getOr(""));return{getValue:e=>t.get(),setValue:(e,o)=>{t.get()!==o&&vt(e.element,"srcdoc",o),t.set(o)}}})(o),l=e.label.map((e=>Ew(e,t))),c=xw.parts.field({factory:{sketch:e=>ak({uid:e.uid,dom:{tag:"iframe",attributes:a,classes:s?[r]:[r,`${r}--opaque`]},behaviours:gl([Cw.config({}),Wp.config({}),ZC(o,i.getValue,i.setValue)])})}});return Ow(l,c,["tox-form__group--stretched"],[])})(e,t.shared.providers,o),(e,t,o,n)=>{const s=cn(t,{source:"dynamic"});return vO($_)(e,s,o,n)}),button:vO(((e,t)=>((e,t)=>{const o=sO(e.name,"custom");return n=M.none(),s=xw.parts.field({factory:Mh,...oO(e,M.some(o),t,[QC(""),GC()])}),Ow(n,s,[],[]);var n,s})(e,t.shared.providers))),checkbox:vO(((e,t,o)=>((e,t,o)=>{const n=e=>(e.element.dom.click(),M.some(!0)),s=xw.parts.field({factory:{sketch:x},dom:{tag:"input",classes:["tox-checkbox__input"],attributes:{type:"checkbox"}},behaviours:gl([GC(),Cm.config({disabled:()=>!e.enabled||t.isDisabled()}),Cw.config({}),Wp.config({}),KC(o,bO,fO),Tp.config({mode:"special",onEnter:n,onSpace:n,stopSpaceKeyup:!0}),zp("checkbox-events",[Fr(Gs(),((t,o)=>{Or(t,Mw,{name:e.name})}))])])}),r=xw.parts.label({dom:{tag:"span",classes:["tox-checkbox__label"]},components:[Ga(t.translate(e.label))],behaviours:gl([Xw.config({})])}),a=e=>Nh("checked"===e?"selected":"unselected",{tag:"span",classes:["tox-icon","tox-checkbox-icon__"+e]},t.icons),i=Bh({dom:{tag:"div",classes:["tox-checkbox__icons"]},components:[a("checked"),a("unchecked")]});return xw.sketch({dom:{tag:"label",classes:["tox-checkbox"]},components:[s,i.asSpec(),r],fieldBehaviours:gl([Cm.config({disabled:()=>!e.enabled||t.isDisabled(),disableClass:"tox-checkbox--disabled",onDisabled:e=>{xw.getField(e).each(Cm.disable)},onEnabled:e=>{xw.getField(e).each(Cm.enable)}}),oy()])})})(e,t.shared.providers,o))),colorinput:vO(((e,t,o)=>((e,t,o,n)=>{const s=xw.parts.field({factory:yb,inputClasses:["tox-textfield"],data:n,onSetValue:e=>qw.run(e).get(b),inputBehaviours:gl([Cm.config({disabled:t.providers.isDisabled}),oy(),Cw.config({}),qw.config({invalidClass:"tox-textbox-field-invalid",getRoot:e=>tt(e.element),notify:{onValid:e=>{const t=ou.getValue(e);Or(e,Jw,{color:t})}},validator:{validateOnLoad:!1,validate:e=>{const t=ou.getValue(e);if(0===t.length)return Rx(Ko.value(!0));{const e=De("span");_t(e,"background-color",t);const o=At(e,"background-color").fold((()=>Ko.error("blah")),(e=>Ko.value(t)));return Rx(o)}}}})]),selectOnFocus:!1}),r=e.label.map((e=>Ew(e,t.providers))),a=(e,t)=>{Or(e,Yw,{value:t})},i=Bh(((e,t)=>Yx.sketch({dom:e.dom,components:e.components,toggleClass:"mce-active",dropdownBehaviours:gl([ny(t.providers.isDisabled),oy(),Xw.config({}),Cw.config({})]),layouts:e.layouts,sandboxClasses:["tox-dialog__popups"],lazySink:t.getSink,fetch:o=>Vx((t=>e.fetch(t))).map((n=>M.from(sw(cn(bx(Qr("menu-value"),n,(t=>{e.onItemAction(o,t)}),e.columns,e.presets,Kf.CLOSE_ON_EXECUTE,_,t.providers),{movement:yx(e.columns,e.presets)}))))),parts:{menu:pb(0,0,e.presets)}}))({dom:{tag:"span",attributes:{"aria-label":t.providers.translate("Color swatch")}},layouts:{onRtl:()=>[Ji,Xi,Qi],onLtr:()=>[Xi,Ji,Qi]},components:[],fetch:mx(o.getColors(),o.hasCustomColors()),columns:o.getColorCols(),presets:"color",onItemAction:(e,t)=>{i.getOpt(e).each((e=>{"custom"===t?o.colorPicker((t=>{t.fold((()=>kr(e,Kw)),(t=>{a(e,t),ix(t)}))}),"#ffffff"):a(e,"remove"===t?"":t)}))}},t));return xw.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:r.toArray().concat([{dom:{tag:"div",classes:["tox-color-input"]},components:[s,i.asSpec()]}]),fieldBehaviours:gl([zp("form-field-events",[Fr(Jw,((t,o)=>{i.getOpt(t).each((e=>{_t(e.element,"background-color",o.event.color)})),Or(t,Mw,{name:e.name})})),Fr(Yw,((e,t)=>{xw.getField(e).each((o=>{ou.setValue(o,t.event.value),cm.getCurrent(e).each(Wp.focus)}))})),Fr(Kw,((e,t)=>{xw.getField(e).each((t=>{cm.getCurrent(e).each(Wp.focus)}))}))])])})})(e,t.shared,t.colorinput,o))),colorpicker:vO(((e,t,o)=>((e,t,o)=>{const n=e=>"tox-"+e,s=jC((e=>t=>e.translate(ek[t]))(t),n),r=Bh(s.sketch({dom:{tag:"div",classes:[n("color-picker-container")],attributes:{role:"presentation"}},onValidHex:e=>{Or(e,Dw,{name:"hex-valid",value:!0})},onInvalidHex:e=>{Or(e,Dw,{name:"hex-valid",value:!1})}}));return{dom:{tag:"div"},components:[r.asSpec()],behaviours:gl([ZC(o,(e=>{const t=r.get(e);return cm.getCurrent(t).bind((e=>ou.getValue(e).hex)).map((e=>"#"+e)).getOr("")}),((e,t)=>{const o=M.from(/^#([a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)/.exec(t)).bind((e=>te(e,1))),n=r.get(e);cm.getCurrent(n).fold((()=>{console.log("Can not find form")}),(e=>{ou.setValue(e,{hex:o.getOr("")}),HC.getField(e,"hex").each((e=>{kr(e,js())}))}))})),GC()])}})(0,t.shared.providers,o))),dropzone:vO(((e,t,o)=>((e,t,o)=>{const n=(e,t)=>{t.stop()},s=e=>(t,o)=>{N(e,(e=>{e(t,o)}))},r=(e,t)=>{var o;if(!Cm.isDisabled(e)){const n=t.event.raw;i(e,null===(o=n.dataTransfer)||void 0===o?void 0:o.files)}},a=(e,t)=>{const o=t.event.raw.target;i(e,o.files)},i=(o,n)=>{n&&(ou.setValue(o,((e,t)=>{const o=ok.explode(t.getOption("images_file_types"));return W(se(e),(e=>R(o,(t=>_e(e.name.toLowerCase(),`.${t.toLowerCase()}`)))))})(n,t)),Or(o,Mw,{name:e.name}))},l=Bh({dom:{tag:"input",attributes:{type:"file",accept:"image/*"},styles:{display:"none"}},behaviours:gl([zp("input-file-events",[Hr($s()),Hr(rr())])])}),c=e.label.map((e=>Ew(e,t))),d=xw.parts.field({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-dropzone-container"]},behaviours:gl([QC(o.getOr([])),GC(),Cm.config({}),Yp.config({toggleClass:"dragenter",toggleOnExecute:!1}),zp("dropzone-events",[Fr("dragenter",s([n,Yp.toggle])),Fr("dragleave",s([n,Yp.toggle])),Fr("dragover",n),Fr("drop",s([n,r])),Fr(Gs(),a)])]),components:[{dom:{tag:"div",classes:["tox-dropzone"],styles:{}},components:[{dom:{tag:"p"},components:[Ga(t.translate("Drop an image here"))]},Mh.sketch({dom:{tag:"button",styles:{position:"relative"},classes:["tox-button","tox-button--secondary"]},components:[Ga(t.translate("Browse for an image")),l.asSpec()],action:e=>{l.get(e).element.dom.click()},buttonBehaviours:gl([Cw.config({}),ny(t.isDisabled),oy()])})]}]})}});return Ow(c,d,["tox-form__group--stretched"],[])})(e,t.shared.providers,o))),grid:vO(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-form__grid",`tox-form__grid--${e.columns}col`]},components:P(e.items,t.interpreter)}))(e,t.shared))),listbox:vO(((e,t,o)=>((e,t,o)=>{const n=t.shared.providers,s=o.bind((t=>_k(e.items,t))).orThunk((()=>oe(e.items).filter(Ck))),r=e.label.map((e=>Ew(e,n))),a=xw.parts.field({dom:{},factory:{sketch:o=>vk({uid:o.uid,text:s.map((e=>e.text)),icon:M.none(),tooltip:e.label,role:M.none(),fetch:(o,n)=>{const s=Ok(o,e.name,e.items,ou.getValue(o));n(Sk(s,Kf.CLOSE_ON_EXECUTE,t,{isHorizontalMenu:!1,search:M.none()}))},onSetup:y(b),getApi:y({}),columns:1,presets:"normal",classes:[],dropdownBehaviours:[Cw.config({}),ZC(s.map((e=>e.value)),(e=>xt(e.element,kk)),((t,o)=>{_k(e.items,o).each((e=>{vt(t.element,kk,e.value),Or(t,fk,{text:e.text})}))}))]},"tox-listbox",t.shared)}}),i={dom:{tag:"div",classes:["tox-listboxfield"]},components:[a]};return xw.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:q([r.toArray(),[i]]),fieldBehaviours:gl([Cm.config({disabled:y(!e.enabled),onDisabled:e=>{xw.getField(e).each(Cm.disable)},onEnabled:e=>{xw.getField(e).each(Cm.enable)}})])})})(e,t,o))),selectbox:vO(((e,t,o)=>((e,t,o)=>{const n=P(e.items,(e=>({text:t.translate(e.text),value:e.value}))),s=e.label.map((e=>Ew(e,t))),r=xw.parts.field({dom:{},...o.map((e=>({data:e}))).getOr({}),selectAttributes:{size:e.size},options:n,factory:Tk,selectBehaviours:gl([Cm.config({disabled:()=>!e.enabled||t.isDisabled()}),Cw.config({}),zp("selectbox-change",[Fr(Gs(),((t,o)=>{Or(t,Mw,{name:e.name})}))])])}),a=e.size>1?M.none():M.some(Nh("chevron-down",{tag:"div",classes:["tox-selectfield__icon-js"]},t.icons)),i={dom:{tag:"div",classes:["tox-selectfield"]},components:q([[r],a.toArray()])};return xw.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:q([s.toArray(),[i]]),fieldBehaviours:gl([Cm.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{xw.getField(e).each(Cm.disable)},onEnabled:e=>{xw.getField(e).each(Cm.enable)}}),oy()])})})(e,t.shared.providers,o))),sizeinput:vO(((e,t)=>((e,t)=>{let o=Ik;const n=Qr("ratio-event"),s=e=>Nh(e,{tag:"span",classes:["tox-icon","tox-lock-icon__"+e]},t.icons),r=Ak.parts.lock({dom:{tag:"button",classes:["tox-lock","tox-button","tox-button--naked","tox-button--icon"],attributes:{title:t.translate(e.label.getOr("Constrain proportions"))}},components:[s("lock"),s("unlock")],buttonBehaviours:gl([Cm.config({disabled:()=>!e.enabled||t.isDisabled()}),oy(),Cw.config({})])}),a=e=>({dom:{tag:"div",classes:["tox-form__group"]},components:e}),i=o=>xw.parts.field({factory:yb,inputClasses:["tox-textfield"],inputBehaviours:gl([Cm.config({disabled:()=>!e.enabled||t.isDisabled()}),oy(),Cw.config({}),zp("size-input-events",[Fr(Ns(),((e,t)=>{Or(e,n,{isField1:o})})),Fr(Gs(),((t,o)=>{Or(t,Mw,{name:e.name})}))])]),selectOnFocus:!1}),l=e=>({dom:{tag:"label",classes:["tox-label"]},components:[Ga(t.translate(e))]}),c=Ak.parts.field1(a([xw.parts.label(l("Width")),i(!0)])),d=Ak.parts.field2(a([xw.parts.label(l("Height")),i(!1)]));return Ak.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:[c,d,a([l("\xa0"),r])]}],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:(e,t,n)=>{Dk(ou.getValue(e)).each((e=>{o(e).each((e=>{ou.setValue(t,(e=>{const t={"":0,px:0,pt:1,mm:1,pc:2,ex:2,em:2,ch:2,rem:2,cm:3,in:4,"%":4};let o=e.value.toFixed((n=e.unit)in t?t[n]:1);var n;return-1!==o.indexOf(".")&&(o=o.replace(/\.?0*$/,"")),o+e.unit})(e))}))}))},coupledFieldBehaviours:gl([Cm.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{Ak.getField1(e).bind(xw.getField).each(Cm.disable),Ak.getField2(e).bind(xw.getField).each(Cm.disable),Ak.getLock(e).each(Cm.disable)},onEnabled:e=>{Ak.getField1(e).bind(xw.getField).each(Cm.enable),Ak.getField2(e).bind(xw.getField).each(Cm.enable),Ak.getLock(e).each(Cm.enable)}}),oy(),zp("size-input-events2",[Fr(n,((e,t)=>{const n=t.event.isField1,s=n?Ak.getField1(e):Ak.getField2(e),r=n?Ak.getField2(e):Ak.getField1(e),a=s.map(ou.getValue).getOr(""),i=r.map(ou.getValue).getOr("");o=((e,t)=>{const o=Dk(e).toOptional(),n=Dk(t).toOptional();return Se(o,n,((e,t)=>Fk(e,t.unit).map((e=>t.value/e)).map((e=>{return o=e,n=t.unit,e=>Fk(e,n).map((e=>({value:e*o,unit:n})));var o,n})).getOr(Ik))).getOr(Ik)})(a,i)}))])])})})(e,t.shared.providers))),slider:vO(((e,t,o)=>((e,t,o)=>{const n=BC.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[Ga(t.translate(e.label))]}),s=BC.parts.spectrum({dom:{tag:"div",classes:["tox-slider__rail"],attributes:{role:"presentation"}}}),r=BC.parts.thumb({dom:{tag:"div",classes:["tox-slider__handle"],attributes:{role:"presentation"}}});return BC.sketch({dom:{tag:"div",classes:["tox-slider"],attributes:{role:"presentation"}},model:{mode:"x",minX:e.min,maxX:e.max,getInitialValue:y(o.getOrThunk((()=>(Math.abs(e.max)-Math.abs(e.min))/2)))},components:[n,s,r],sliderBehaviours:gl([GC(),Wp.config({})]),onChoose:(t,o,n)=>{Or(t,Mw,{name:e.name,value:n})}})})(e,t.shared.providers,o))),urlinput:vO(((e,t,o)=>((e,t,o,n)=>{const s=t.shared.providers,r=t=>{const n=ou.getValue(t);o.addToHistory(n.value,e.filetype)},a={...n.map((e=>({initialData:e}))).getOr({}),dismissOnBlur:!0,inputClasses:["tox-textfield"],sandboxClasses:["tox-dialog__popups"],inputAttributes:{"aria-errormessage":hO,type:"url"},minChars:0,responseTime:0,fetch:n=>{const s=((e,t,o)=>{const n=ou.getValue(t),s=void 0!==n.meta.text?n.meta.text:n.value;return o.getLinkInformation().fold((()=>[]),(t=>{const n=pO(s,(e=>P(e,(e=>lO(e,e))))(o.getHistory(e)));return"file"===e?(r=[n,pO(s,dO(t)),pO(s,q([mO(t),uO(t),gO(t)]))],j(r,((e,t)=>0===e.length||0===t.length?e.concat(t):e.concat(aO,t)),[])):n;var r}))})(e.filetype,n,o),r=Sk(s,Kf.BUBBLE_TO_SANDBOX,t,{isHorizontalMenu:!1,search:M.none()});return Rx(r)},getHotspot:e=>g.getOpt(e),onSetValue:(e,t)=>{e.hasConfigured(qw)&&qw.run(e).get(b)},typeaheadBehaviours:gl([...o.getValidationHandler().map((t=>qw.config({getRoot:e=>tt(e.element),invalidClass:"tox-control-wrap--status-invalid",notify:{onInvalid:(e,t)=>{c.getOpt(e).each((e=>{vt(e.element,"title",s.translate(t))}))}},validator:{validate:o=>{const n=ou.getValue(o);return Yk((o=>{t({type:e.filetype,url:n.value},(e=>{if("invalid"===e.status){const t=Ko.error(e.message);o(t)}else{const t=Ko.value(e.message);o(t)}}))}))},validateOnLoad:!1}}))).toArray(),Cm.config({disabled:()=>!e.enabled||s.isDisabled()}),Cw.config({}),zp("urlinput-events",[Fr(js(),(t=>{const o=Ra(t.element),n=o.trim();n!==o&&za(t.element,n),"file"===e.filetype&&Or(t,Mw,{name:e.name})})),Fr(Gs(),(t=>{Or(t,Mw,{name:e.name}),r(t)})),Fr(tr(),(t=>{Or(t,Mw,{name:e.name}),r(t)}))])]),eventOrder:{[js()]:["streaming","urlinput-events","invalidating"]},model:{getDisplayText:e=>e.value,selectsOver:!1,populateFromBrowse:!1},markers:{openClass:"tox-textfield--popup-open"},lazySink:t.shared.getSink,parts:{menu:pb(0,0,"normal")},onExecute:(e,t,o)=>{Or(t,Fw,{})},onItemExecute:(t,o,n,s)=>{r(t),Or(t,Mw,{name:e.name})}},i=xw.parts.field({...a,factory:Xk}),l=e.label.map((e=>Ew(e,s))),c=Bh(((e,t,o=e,n=e)=>Nh(o,{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+e],attributes:{title:s.translate(n),"aria-live":"polite",...t.fold((()=>({})),(e=>({id:e})))}},s.icons))("invalid",M.some(hO),"warning")),d=Bh({dom:{tag:"div",classes:["tox-control-wrap__status-icon-wrap"]},components:[c.asSpec()]}),u=o.getUrlPicker(e.filetype),m=Qr("browser.url.event"),g=Bh({dom:{tag:"div",classes:["tox-control-wrap"]},components:[i,d.asSpec()],behaviours:gl([Cm.config({disabled:()=>!e.enabled||s.isDisabled()})])}),p=Bh(nO({name:e.name,icon:M.some("browse"),text:e.label.getOr(""),enabled:e.enabled,primary:!1,buttonType:M.none(),borderless:!0},(e=>kr(e,m)),s,[],["tox-browse-url"]));return xw.sketch({dom:Tw([]),components:l.toArray().concat([{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:q([[g.asSpec()],u.map((()=>p.asSpec())).toArray()])}]),fieldBehaviours:gl([Cm.config({disabled:()=>!e.enabled||s.isDisabled(),onDisabled:e=>{xw.getField(e).each(Cm.disable),p.getOpt(e).each(Cm.disable)},onEnabled:e=>{xw.getField(e).each(Cm.enable),p.getOpt(e).each(Cm.enable)}}),oy(),zp("url-input-events",[Fr(m,(t=>{cm.getCurrent(t).each((o=>{const n=ou.getValue(o),s={fieldname:e.name,...n};u.each((n=>{n(s).get((n=>{ou.setValue(o,n),Or(t,Mw,{name:e.name})}))}))}))}))])])})})(e,t,t.urlinput,o))),customeditor:vO((e=>{const t=Wl(),o=Bh({dom:{tag:e.tag}}),n=Wl();return{dom:{tag:"div",classes:["tox-custom-editor"]},behaviours:gl([zp("custom-editor-events",[Lr((s=>{o.getOpt(s).each((o=>{((e=>ve(e,"init"))(e)?e.init(o.element.dom):tk.load(e.scriptId,e.scriptUrl).then((t=>t(o.element.dom,e.settings)))).then((e=>{n.on((t=>{e.setValue(t)})),n.clear(),t.set(e)}))}))}))]),ZC(M.none(),(()=>t.get().fold((()=>n.get().getOr("")),(e=>e.getValue()))),((e,o)=>{t.get().fold((()=>n.set(o)),(e=>e.setValue(o)))})),GC()]),components:[o.asSpec()]}})),htmlpanel:vO((e=>"presentation"===e.presets?fw.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:e.html}}):fw.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:e.html,attributes:{role:"document"}},containerBehaviours:gl([Cw.config({}),Wp.config({})])}))),imagepreview:vO(((e,t,o)=>((e,t)=>{const o=xs(t.getOr({url:""})),n=Bh({dom:{tag:"img",classes:["tox-imagepreview__image"],attributes:t.map((e=>({src:e.url}))).getOr({})}}),s=Bh({dom:{tag:"div",classes:["tox-imagepreview__container"],attributes:{role:"presentation"}},components:[n.asSpec()]}),r={};e.height.each((e=>r.height=e));const a=t.map((e=>({url:e.url,zoom:M.from(e.zoom),cachedWidth:M.from(e.cachedWidth),cachedHeight:M.from(e.cachedHeight)})));return{dom:{tag:"div",classes:["tox-imagepreview"],styles:r,attributes:{role:"presentation"}},components:[s.asSpec()],behaviours:gl([GC(),ZC(a,(()=>o.get()),((e,t)=>{const r={url:t.url};t.zoom.each((e=>r.zoom=e)),t.cachedWidth.each((e=>r.cachedWidth=e)),t.cachedHeight.each((e=>r.cachedHeight=e)),o.set(r);const a=()=>{const{cachedWidth:t,cachedHeight:o,zoom:n}=r;if(!u(t)&&!u(o)){if(u(n)){const n=((e,t,o)=>{const n=$t(e),s=Ht(e);return Math.min(n/t,s/o,1)})(e.element,t,o);r.zoom=n}const a=((e,t,o,n,s)=>{const r=o*s,a=n*s,i=Math.max(0,e/2-r/2),l=Math.max(0,t/2-a/2);return{left:i.toString()+"px",top:l.toString()+"px",width:r.toString()+"px",height:a.toString()+"px"}})($t(e.element),Ht(e.element),t,o,r.zoom);s.getOpt(e).each((e=>{Tt(e.element,a)}))}};n.getOpt(e).each((o=>{const n=o.element;var s;t.url!==xt(n,"src")&&(vt(n,"src",t.url),Da(e.element,"tox-imagepreview__loaded")),a(),(s=n,new Promise(((e,t)=>{const o=()=>{r(),e(s)},n=[jl(s,"load",o),jl(s,"error",(()=>{r(),t("Unable to load data from image: "+s.dom.src)}))],r=()=>N(n,(e=>e.unbind()));s.dom.complete&&o()}))).then((t=>{e.getSystem().isConnected()&&(Aa(e.element,"tox-imagepreview__loaded"),r.cachedWidth=t.dom.naturalWidth,r.cachedHeight=t.dom.naturalHeight,a())}))}))}))])}})(e,o))),table:vO(((e,t)=>((e,t)=>{const o=e=>({dom:{tag:"td",innerHtml:t.translate(e)}});return{dom:{tag:"table",classes:["tox-dialog__table"]},components:[(s=e.header,{dom:{tag:"thead"},components:[{dom:{tag:"tr"},components:P(s,(e=>({dom:{tag:"th",innerHtml:t.translate(e)}})))}]}),(n=e.cells,{dom:{tag:"tbody"},components:P(n,(e=>({dom:{tag:"tr"},components:P(e,o)})))})],behaviours:gl([Cw.config({}),Wp.config({})])};var n,s})(e,t.shared.providers))),panel:vO(((e,t)=>((e,t)=>({dom:{tag:"div",classes:e.classes},components:P(e.items,t.shared.interpreter)}))(e,t)))},xO={field:(e,t)=>t,record:y([])},wO=(e,t,o,n)=>{const s=cn(n,{shared:{interpreter:t=>SO(e,t,o,s)}});return SO(e,t,o,s)},SO=(e,t,o,n)=>be(yO,t.type).fold((()=>(console.error(`Unknown factory type "${t.type}", defaulting to container: `,t),t)),(s=>s(e,t,o,n))),CO="layout-inset",kO=e=>e.x,OO=(e,t)=>e.x+e.width/2-t.width/2,_O=(e,t)=>e.x+e.width-t.width,TO=e=>e.y,EO=(e,t)=>e.y+e.height-t.height,MO=(e,t)=>e.y+e.height/2-t.height/2,BO=(e,t,o)=>Ei(_O(e,t),EO(e,t),o.insetSouthwest(),Fi(),"southwest",Ni(e,{right:0,bottom:3}),CO),AO=(e,t,o)=>Ei(kO(e),EO(e,t),o.insetSoutheast(),Di(),"southeast",Ni(e,{left:1,bottom:3}),CO),DO=(e,t,o)=>Ei(_O(e,t),TO(e),o.insetNorthwest(),Ai(),"northwest",Ni(e,{right:0,top:2}),CO),FO=(e,t,o)=>Ei(kO(e),TO(e),o.insetNortheast(),Bi(),"northeast",Ni(e,{left:1,top:2}),CO),IO=(e,t,o)=>Ei(OO(e,t),TO(e),o.insetNorth(),Ii(),"north",Ni(e,{top:2}),CO),VO=(e,t,o)=>Ei(OO(e,t),EO(e,t),o.insetSouth(),Vi(),"south",Ni(e,{bottom:3}),CO),RO=(e,t,o)=>Ei(_O(e,t),MO(e,t),o.insetEast(),zi(),"east",Ni(e,{right:0}),CO),zO=(e,t,o)=>Ei(kO(e),MO(e,t),o.insetWest(),Ri(),"west",Ni(e,{left:1}),CO),HO=e=>{switch(e){case"north":return IO;case"northeast":return FO;case"northwest":return DO;case"south":return VO;case"southeast":return AO;case"southwest":return BO;case"east":return RO;case"west":return zO}},PO=(e,t,o,n,s)=>zl(n).map(HO).getOr(IO)(e,t,o,n,s),NO=e=>{switch(e){case"north":return VO;case"northeast":return AO;case"northwest":return BO;case"south":return IO;case"southeast":return FO;case"southwest":return DO;case"east":return zO;case"west":return RO}},LO=(e,t,o,n,s)=>zl(n).map(NO).getOr(IO)(e,t,o,n,s),WO={valignCentre:[],alignCentre:[],alignLeft:[],alignRight:[],right:[],left:[],bottom:[],top:[]},UO=(e,t,o)=>{const n={maxHeightFunction:Zl()};return()=>o()?{type:"node",root:ut(dt(e())),node:M.from(e()),bubble:oc(12,12,WO),layouts:{onRtl:()=>[FO],onLtr:()=>[DO]},overrides:n}:{type:"hotspot",hotspot:t(),bubble:oc(-12,12,WO),layouts:{onRtl:()=>[Xi],onLtr:()=>[Ji]},overrides:n}},jO=(e,t,o)=>()=>o()?{type:"node",root:ut(dt(e())),node:M.from(e()),layouts:{onRtl:()=>[IO],onLtr:()=>[IO]}}:{type:"hotspot",hotspot:t(),layouts:{onRtl:()=>[Qi],onLtr:()=>[Qi]}},GO=(e,t)=>()=>({type:"selection",root:t(),getSelection:()=>{const t=e.selection.getRng();return M.some(Mc.range(Ie(t.startContainer),t.startOffset,Ie(t.endContainer),t.endOffset))}}),$O=e=>t=>({type:"node",root:e(),node:t}),qO=(e,t,o)=>{const n=Wf(e),s=()=>Ie(e.getBody()),r=()=>Ie(e.getContentAreaContainer()),a=()=>n||!o();return{inlineDialog:UO(r,t,a),banner:jO(r,t,a),cursor:GO(e,s),node:$O(s)}},XO=e=>(t,o)=>{fx(e)(t,o)},JO=e=>()=>rx(e),YO=e=>()=>ax(e),KO=e=>()=>sx(e),ZO=e=>({colorPicker:XO(e),hasCustomColors:JO(e),getColors:YO(e),getColorCols:KO(e)}),QO=e=>()=>Of(e),e_=e=>({isDraggableModal:QO(e)}),t_=e=>ye(e,"items"),o_=e=>ye(e,"format"),n_=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",format:"bold"},{title:"Italic",format:"italic"},{title:"Underline",format:"underline"},{title:"Strikethrough",format:"strikethrough"},{title:"Superscript",format:"superscript"},{title:"Subscript",format:"subscript"},{title:"Code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Align",items:[{title:"Left",format:"alignleft"},{title:"Center",format:"aligncenter"},{title:"Right",format:"alignright"},{title:"Justify",format:"alignjustify"}]}],s_=e=>j(e,((e,t)=>{if(ve(t,"items")){const o=s_(t.items);return{customFormats:e.customFormats.concat(o.customFormats),formats:e.formats.concat([{title:t.title,items:o.formats}])}}if(ve(t,"inline")||(e=>ve(e,"block"))(t)||(e=>ve(e,"selector"))(t)){const o=`custom-${r(t.name)?t.name:t.title.toLowerCase()}`;return{customFormats:e.customFormats.concat([{name:o,format:t}]),formats:e.formats.concat([{title:t.title,format:o,icon:t.icon}])}}return{...e,formats:e.formats.concat(t)}}),{customFormats:[],formats:[]}),r_=e=>sf(e).map((t=>{const o=((e,t)=>{const o=s_(t),n=t=>{N(t,(t=>{e.formatter.has(t.name)||e.formatter.register(t.name,t.format)}))};return e.formatter?n(o.customFormats):e.on("init",(()=>{n(o.customFormats)})),o.formats})(e,t);return rf(e)?n_.concat(o):o})).getOr(n_),a_=(e,t,o)=>({...e,type:"formatter",isSelected:t(e.format),getStylePreview:o(e.format)}),i_=(e,t,o,n)=>{const s=t=>P(t,(t=>t_(t)?(e=>{const t=s(e.items);return{...e,type:"submenu",getStyleItems:y(t)}})(t):o_(t)?(e=>a_(e,o,n))(t):(e=>{const t=ae(e);return 1===t.length&&V(t,"title")})(t)?{...t,type:"separator"}:(t=>{const s=r(t.name)?t.name:Qr(t.title),a=`custom-${s}`,i={...t,type:"formatter",format:a,isSelected:o(a),getStylePreview:n(a)};return e.formatter.register(s,i),i})(t)));return s(t)},l_=e=>{const t=t=>()=>e.formatter.match(t),o=t=>()=>{const o=e.formatter.get(t);return void 0!==o?M.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):M.none()},n=xs([]),s=xs([]),r=xs(!1);return e.on("PreInit",(s=>{const r=r_(e),a=i_(e,r,t,o);n.set(a)})),e.on("addStyleModifications",(n=>{const a=i_(e,n.items,t,o);s.set(a),r.set(n.replace)})),{getData:()=>{const e=r.get()?[]:n.get(),t=s.get();return e.concat(t)}}},c_=ok.trim,d_=e=>t=>{if((e=>g(e)&&1===e.nodeType)(t)){if(t.contentEditable===e)return!0;if(t.getAttribute("data-mce-contenteditable")===e)return!0}return!1},u_=d_("true"),m_=d_("false"),g_=(e,t,o,n,s)=>({type:e,title:t,url:o,level:n,attach:s}),p_=e=>e.innerText||e.textContent,h_=e=>(e=>e&&"A"===e.nodeName&&void 0!==(e.id||e.name))(e)&&b_(e),f_=e=>e&&/^(H[1-6])$/.test(e.nodeName),b_=e=>(e=>{let t=e;for(;t=t.parentNode;){const e=t.contentEditable;if(e&&"inherit"!==e)return u_(t)}return!1})(e)&&!m_(e),v_=e=>f_(e)&&b_(e),y_=e=>{var t;const o=(e=>e.id?e.id:Qr("h"))(e);return g_("header",null!==(t=p_(e))&&void 0!==t?t:"","#"+o,(e=>f_(e)?parseInt(e.nodeName.substr(1),10):0)(e),(()=>{e.id=o}))},x_=e=>{const t=e.id||e.name,o=p_(e);return g_("anchor",o||"#"+t,"#"+t,0,b)},w_=e=>c_(e.title).length>0,S_=e=>{const t=(e=>{const t=P(Rc(Ie(e),"h1,h2,h3,h4,h5,h6,a:not([href])"),(e=>e.dom));return t})(e);return W((e=>P(W(e,v_),y_))(t).concat((e=>P(W(e,h_),x_))(t)),w_)},C_="tinymce-url-history",k_=e=>r(e)&&/^https?/.test(e),O_=e=>a(e)&&he(e,(e=>{return!(l(t=e)&&t.length<=5&&J(t,k_));var t})).isNone(),__=()=>{const e=Qy.getItem(C_);if(null===e)return{};let t;try{t=JSON.parse(e)}catch(e){if(e instanceof SyntaxError)return console.log("Local storage "+C_+" was not valid JSON",e),{};throw e}return O_(t)?t:(console.log("Local storage "+C_+" was not valid format",t),{})},T_=e=>{const t=__();return be(t,e).getOr([])},E_=(e,t)=>{if(!k_(e))return;const o=__(),n=be(o,t).getOr([]),s=W(n,(t=>t!==e));o[t]=[e].concat(s).slice(0,5),(e=>{if(!O_(e))throw new Error("Bad format for history:\n"+JSON.stringify(e));Qy.setItem(C_,JSON.stringify(e))})(o)},M_=e=>!!e,B_=e=>ce(ok.makeMap(e,/[, ]/),M_),A_=e=>M.from(yf(e)),D_=e=>M.from(e).filter(r).getOrUndefined(),F_=e=>({getHistory:T_,addToHistory:E_,getLinkInformation:()=>(e=>Sf(e)?M.some({targets:S_(e.getBody()),anchorTop:D_(Cf(e)),anchorBottom:D_(kf(e))}):M.none())(e),getValidationHandler:()=>(e=>M.from(xf(e)))(e),getUrlPicker:t=>((e,t)=>((e,t)=>{const o=(e=>{const t=M.from(wf(e)).filter(M_).map(B_);return A_(e).fold(_,(e=>t.fold(T,(e=>ae(e).length>0&&e))))})(e);return d(o)?o?A_(e):M.none():o[t]?A_(e):M.none()})(e,t).map((o=>n=>Vx((s=>{const i={filetype:t,fieldname:n.fieldname,...M.from(n.meta).getOr({})};o.call(e,((e,t)=>{if(!r(e))throw new Error("Expected value to be string");if(void 0!==t&&!a(t))throw new Error("Expected meta to be a object");s({value:e,meta:t})}),n.value,i)})))))(e,t)}),I_=Zu,V_=Vu,R_=y([us("shell",!1),Xn("makeItem"),us("setupItem",b),au("listBehaviours",[Rp])]),z_=Du({name:"items",overrides:()=>({behaviours:gl([Rp.config({})])})}),H_=y([z_]),P_=rm({name:y("CustomList")(),configFields:R_(),partFields:H_(),factory:(e,t,o,n)=>{const s=e.shell?{behaviours:[Rp.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:s.components,behaviours:ru(e.listBehaviours,s.behaviours),apis:{setItems:(t,o)=>{var n;(n=t,e.shell?M.some(n):ju(n,e,"items")).fold((()=>{throw console.error("Custom List was defined to not be a shell, but no item container was specified in components"),new Error("Custom List was defined to not be a shell, but no item container was specified in components")}),(n=>{const s=Rp.contents(n),r=o.length,a=r-s.length,i=a>0?z(a,(()=>e.makeItem())):[],l=s.slice(r);N(l,(e=>Rp.remove(n,e))),N(i,(e=>Rp.append(n,e)));const c=Rp.contents(n);N(c,((n,s)=>{e.setupItem(t,n,o[s],s)}))}))}}}},apis:{setItems:(e,t,o)=>{e.setItems(t,o)}}}),N_=y([Xn("dom"),us("shell",!0),nu("toolbarBehaviours",[Rp])]),L_=y([Du({name:"groups",overrides:()=>({behaviours:gl([Rp.config({})])})})]),W_=rm({name:"Toolbar",configFields:N_(),partFields:L_(),factory:(e,t,o,n)=>{const s=e.shell?{behaviours:[Rp.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:s.components,behaviours:ru(e.toolbarBehaviours,s.behaviours),apis:{setGroups:(t,o)=>{var n;(n=t,e.shell?M.some(n):ju(n,e,"groups")).fold((()=>{throw console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")}),(e=>{Rp.set(e,o)}))}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)}}}),U_=b,j_=_,G_=y([]);var $_,q_=Object.freeze({__proto__:null,setup:U_,isDocked:j_,getBehaviours:G_});const X_=e=>(xe(At(e,"position"),"fixed")?M.none():ot(e)).orThunk((()=>{const t=De("span");return et(e).bind((e=>{Fo(e,t);const o=ot(t);return Ro(t),o}))})),J_=e=>X_(e).map(Ut).getOrThunk((()=>Lt(0,0))),Y_=ws([{static:[]},{absolute:["positionCss"]},{fixed:["positionCss"]}]),K_=(e,t)=>{const o=e.element;Aa(o,t.transitionClass),Da(o,t.fadeOutClass),Aa(o,t.fadeInClass),t.onShow(e)},Z_=(e,t)=>{const o=e.element;Aa(o,t.transitionClass),Da(o,t.fadeInClass),Aa(o,t.fadeOutClass),t.onHide(e)},Q_=(e,t,o)=>J(e,(e=>{switch(e){case"bottom":return((e,t)=>e.bottom<=t.bottom)(t,o);case"top":return((e,t)=>e.y>=t.y)(t,o)}})),eT=(e,t)=>t.getInitialPos().map((t=>Go(t.bounds.x,t.bounds.y,$t(e),Ht(e)))),tT=(e,t,o)=>o.getInitialPos().bind((n=>{switch(o.clearInitialPos(),n.position){case"static":return M.some(Y_.static());case"absolute":const o=X_(e).map($o).getOrThunk((()=>$o(ht())));return M.some(Y_.absolute(_l("absolute",be(n.style,"left").map((e=>t.x-o.x)),be(n.style,"top").map((e=>t.y-o.y)),be(n.style,"right").map((e=>o.right-t.right)),be(n.style,"bottom").map((e=>o.bottom-t.bottom)))));default:return M.none()}})),oT=(e,t,o)=>{const n=e.element;return xe(At(n,"position"),"fixed")?((e,t,o)=>eT(e,o).filter((e=>Q_(o.getModes(),e,t))).bind((t=>tT(e,t,o))))(n,t,o):((e,t,o)=>{const n=$o(e);if(Q_(o.getModes(),n,t))return M.none();{((e,t,o)=>{o.setInitialPos({style:Dt(e),position:Mt(e,"position")||"static",bounds:t})})(e,n,o);const s=Xo(),r=n.x-s.x,a=t.y-s.y,i=s.bottom-t.bottom,l=n.y<=t.y;return M.some(Y_.fixed(_l("fixed",M.some(r),l?M.some(a):M.none(),M.none(),l?M.none():M.some(i))))}})(n,t,o)},nT=(e,t,o)=>{o.setDocked(!1),N(["left","right","top","bottom","position"],(t=>It(e.element,t))),t.onUndocked(e)},sT=(e,t,o,n)=>{const s="fixed"===n.position;o.setDocked(s),Tl(e.element,n),(s?t.onDocked:t.onUndocked)(e)},rT=(e,t,o,n,s=!1)=>{t.contextual.each((t=>{t.lazyContext(e).each((r=>{const a=((e,t)=>e.y<t.bottom&&e.bottom>t.y)(r,n);a!==o.isVisible()&&(o.setVisible(a),s&&!a?(Ia(e.element,[t.fadeOutClass]),t.onHide(e)):(a?K_:Z_)(e,t))}))}))},aT=(e,t,o)=>{e.getSystem().isConnected()&&((e,t,o)=>{const n=t.lazyViewport(e);o.isDocked()&&rT(e,t,o,n),oT(e,n,o).each((s=>{s.fold((()=>nT(e,t,o)),(n=>sT(e,t,o,n)),(s=>{rT(e,t,o,n,!0),sT(e,t,o,s)}))}))})(e,t,o)},iT=(e,t,o)=>{o.isDocked()&&((e,t,o)=>{const n=e.element;o.setDocked(!1),((e,t)=>{const o=e.element;return eT(o,t).bind((e=>tT(o,e,t)))})(e,o).each((n=>{n.fold((()=>nT(e,t,o)),(n=>sT(e,t,o,n)),b)})),o.setVisible(!0),t.contextual.each((t=>{Va(n,[t.fadeInClass,t.fadeOutClass,t.transitionClass]),t.onShow(e)})),aT(e,t,o)})(e,t,o)};var lT=Object.freeze({__proto__:null,refresh:aT,reset:iT,isDocked:(e,t,o)=>o.isDocked(),getModes:(e,t,o)=>o.getModes(),setModes:(e,t,o,n)=>o.setModes(n)}),cT=Object.freeze({__proto__:null,events:(e,t)=>Br([Nr(Xs(),((o,n)=>{e.contextual.each((e=>{Fa(o.element,e.transitionClass)&&(Va(o.element,[e.transitionClass,e.fadeInClass]),(t.isVisible()?e.onShown:e.onHidden)(o)),n.stop()}))})),Fr(mr(),((o,n)=>{aT(o,e,t)})),Fr(gr(),((o,n)=>{iT(o,e,t)}))])}),dT=[ds("contextual",[Kn("fadeInClass"),Kn("fadeOutClass"),Kn("transitionClass"),Qn("lazyContext"),wi("onShow"),wi("onShown"),wi("onHide"),wi("onHidden")]),bs("lazyViewport",Xo),vs("modes",["top","bottom"],Dn),wi("onDocked"),wi("onUndocked")];const uT=hl({fields:dT,name:"docking",active:cT,apis:lT,state:Object.freeze({__proto__:null,init:e=>{const t=xs(!1),o=xs(!0),n=Wl(),s=xs(e.modes);return ba({isDocked:t.get,setDocked:t.set,getInitialPos:n.get,setInitialPos:n.set,clearInitialPos:n.clear,isVisible:o.get,setVisible:o.set,getModes:s.get,setModes:s.set,readState:()=>`docked:  ${t.get()}, visible: ${o.get()}, modes: ${s.get().join(",")}`})}})}),mT=y(Qr("toolbar-height-change")),gT={fadeInClass:"tox-editor-dock-fadein",fadeOutClass:"tox-editor-dock-fadeout",transitionClass:"tox-editor-dock-transition"},pT="tox-tinymce--toolbar-sticky-on",hT="tox-tinymce--toolbar-sticky-off",fT=(e,t)=>V(uT.getModes(e),t),bT=e=>{const t=e.element;tt(t).each((o=>{const n="padding-"+uT.getModes(e)[0];if(uT.isDocked(e)){const e=$t(o);_t(t,"width",e+"px"),_t(o,n,(e=>Pt(e)+(parseInt(Mt(e,"margin-top"),10)||0)+(parseInt(Mt(e,"margin-bottom"),10)||0))(t)+"px")}else It(t,"width"),It(o,n)}))},vT=(e,t)=>{t?(Da(e,gT.fadeOutClass),Ia(e,[gT.transitionClass,gT.fadeInClass])):(Da(e,gT.fadeInClass),Ia(e,[gT.fadeOutClass,gT.transitionClass]))},yT=(e,t)=>{const o=Ie(e.getContainer());t?(Aa(o,pT),Da(o,hT)):(Aa(o,hT),Da(o,pT))},xT=(e,t)=>{const o=Wl(),n=t.getSink,s=e=>{n().each((t=>e(t.element)))},r=t=>{e.inline||bT(t),yT(e,uT.isDocked(t)),t.getSystem().broadcastOn([Hd()],{}),n().each((e=>e.getSystem().broadcastOn([Hd()],{})))},a=e.inline?[]:[yl.config({channels:{[mT()]:{onReceive:bT}}})];return[Wp.config({}),uT.config({contextual:{lazyContext:t=>{const o=Pt(t.element),n=e.inline?e.getContentAreaContainer():e.getContainer(),s=$o(Ie(n)),r=s.height-o,a=s.y+(fT(t,"top")?0:o);return M.some(Go(s.x,a,s.width,r))},onShow:()=>{s((e=>vT(e,!0)))},onShown:e=>{s((e=>Va(e,[gT.transitionClass,gT.fadeInClass]))),o.get().each((t=>{((e,t)=>{const o=Ye(t);Cl(o).filter((e=>!Xe(t,e))).filter((t=>Xe(t,Ie(o.dom.body))||Je(e,t))).each((()=>wl(t)))})(e.element,t),o.clear()}))},onHide:e=>{((e,t)=>kl(e).orThunk((()=>t().toOptional().bind((e=>kl(e.element))))))(e.element,n).fold(o.clear,o.set),s((e=>vT(e,!1)))},onHidden:()=>{s((e=>Va(e,[gT.transitionClass])))},...gT},lazyViewport:t=>{const o=Xo(),n=ff(e),s=o.y+(fT(t,"top")?n:0),r=o.height-(fT(t,"bottom")?n:0);return Go(o.x,s,o.width,r)},modes:[t.header.getDockingMode()],onDocked:r,onUndocked:r}),...a]};var wT=Object.freeze({__proto__:null,setup:(e,t,o)=>{e.inline||(t.header.isPositionedAtTop()||e.on("ResizeEditor",(()=>{o().each(uT.reset)})),e.on("ResizeWindow ResizeEditor",(()=>{o().each(bT)})),e.on("SkinLoaded",(()=>{o().each((e=>{uT.isDocked(e)?uT.reset(e):uT.refresh(e)}))})),e.on("FullscreenStateChanged",(()=>{o().each(uT.reset)}))),e.on("AfterScrollIntoView",(e=>{o().each((t=>{uT.refresh(t);const o=t.element;Sg(o)&&((e,t)=>{const o=Ye(t),n=Qe(t).dom.innerHeight,s=zo(o),r=Ie(e.elm),a=qo(r),i=Ht(r),l=a.y,c=l+i,d=Ut(t),u=Ht(t),m=d.top,g=m+u,p=Math.abs(m-s.top)<2,h=Math.abs(g-(s.top+n))<2;if(p&&l<g)Ho(s.left,l-u,o);else if(h&&c>m){const e=l-n+i+u;Ho(s.left,e,o)}})(e,o)}))})),e.on("PostRender",(()=>{yT(e,!1)}))},isDocked:e=>e().map(uT.isDocked).getOr(!1),getBehaviours:xT});const ST=kn([Rb,Jn("items",_n([En([zb,os("items",Dn)]),Dn]))].concat(mv)),CT=[as("text"),as("tooltip"),as("icon"),ms("search",!1,_n([Fn,kn([as("placeholder")])],(e=>d(e)?e?M.some({placeholder:M.none()}):M.none():M.some(e)))),Qn("fetch"),bs("onSetup",(()=>b))],kT=kn([Rb,...CT]),OT=e=>Nn("menubutton",kT,e),_T=kn([Rb,Kb,Yb,Jb,ev,Ub,qb,hs("presets","normal",["normal","color","listpreview"]),rv(1),Gb,$b]);var TT=sm({factory:(e,t)=>{const o={focus:Tp.focusIn,setMenus:(e,o)=>{const n=P(o,(e=>{const o={type:"menubutton",text:e.text,fetch:t=>{t(e.getItems())}},n=OT(o).mapError((e=>Un(e))).getOrDie();return Zk(n,"tox-mbtn",t.backstage,M.some("menuitem"))}));Rp.set(e,n)}};return{uid:e.uid,dom:e.dom,components:[],behaviours:gl([Rp.config({}),zp("menubar-events",[Lr((t=>{e.onSetup(t)})),Fr(Ps(),((e,t)=>{si(e.element,".tox-mbtn--active").each((o=>{ri(t.event.target,".tox-mbtn").each((t=>{Xe(o,t)||e.getSystem().getByDom(o).each((o=>{e.getSystem().getByDom(t).each((e=>{Yx.expand(e),Yx.close(o),Wp.focus(e)}))}))}))}))})),Fr(vr(),((e,t)=>{t.event.prevFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((o=>{t.event.newFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((e=>{Yx.isOpen(o)&&(Yx.expand(e),Yx.close(o))}))}))}))]),Tp.config({mode:"flow",selector:".tox-mbtn",onEscape:t=>(e.onEscape(t),M.some(!0))}),Cw.config({})]),apis:o,domModification:{attributes:{role:"menubar"}}}},name:"silver.Menubar",configFields:[Xn("dom"),Xn("uid"),Xn("onEscape"),Xn("backstage"),us("onSetup",b)],apis:{focus:(e,t)=>{e.focus(t)},setMenus:(e,t,o)=>{e.setMenus(t,o)}}});const ET=(e,t)=>t.getAnimationRoot.fold((()=>e.element),(t=>t(e))),MT=e=>e.dimension.property,BT=(e,t)=>e.dimension.getDimension(t),AT=(e,t)=>{const o=ET(e,t);Va(o,[t.shrinkingClass,t.growingClass])},DT=(e,t)=>{Da(e.element,t.openClass),Aa(e.element,t.closedClass),_t(e.element,MT(t),"0px"),Vt(e.element)},FT=(e,t)=>{Da(e.element,t.closedClass),Aa(e.element,t.openClass),It(e.element,MT(t))},IT=(e,t,o,n)=>{o.setCollapsed(),_t(e.element,MT(t),BT(t,e.element)),AT(e,t),DT(e,t),t.onStartShrink(e),t.onShrunk(e)},VT=(e,t,o,n)=>{const s=n.getOrThunk((()=>BT(t,e.element)));o.setCollapsed(),_t(e.element,MT(t),s),Vt(e.element);const r=ET(e,t);Da(r,t.growingClass),Aa(r,t.shrinkingClass),DT(e,t),t.onStartShrink(e)},RT=(e,t,o)=>{const n=BT(t,e.element);("0px"===n?IT:VT)(e,t,o,M.some(n))},zT=(e,t,o)=>{const n=ET(e,t),s=Fa(n,t.shrinkingClass),r=BT(t,e.element);FT(e,t);const a=BT(t,e.element);(s?()=>{_t(e.element,MT(t),r),Vt(e.element)}:()=>{DT(e,t)})(),Da(n,t.shrinkingClass),Aa(n,t.growingClass),FT(e,t),_t(e.element,MT(t),a),o.setExpanded(),t.onStartGrow(e)},HT=(e,t,o)=>{const n=ET(e,t);return!0===Fa(n,t.growingClass)},PT=(e,t,o)=>{const n=ET(e,t);return!0===Fa(n,t.shrinkingClass)};var NT=Object.freeze({__proto__:null,refresh:(e,t,o)=>{if(o.isExpanded()){It(e.element,MT(t));const o=BT(t,e.element);_t(e.element,MT(t),o)}},grow:(e,t,o)=>{o.isExpanded()||zT(e,t,o)},shrink:(e,t,o)=>{o.isExpanded()&&RT(e,t,o)},immediateShrink:(e,t,o)=>{o.isExpanded()&&IT(e,t,o)},hasGrown:(e,t,o)=>o.isExpanded(),hasShrunk:(e,t,o)=>o.isCollapsed(),isGrowing:HT,isShrinking:PT,isTransitioning:(e,t,o)=>HT(e,t)||PT(e,t),toggleGrow:(e,t,o)=>{(o.isExpanded()?RT:zT)(e,t,o)},disableTransitions:AT,immediateGrow:(e,t,o)=>{o.isExpanded()||(FT(e,t),_t(e.element,MT(t),BT(t,e.element)),AT(e,t),o.setExpanded(),t.onStartGrow(e),t.onGrown(e))}}),LT=Object.freeze({__proto__:null,exhibit:(e,t,o)=>{const n=t.expanded;return ya(n?{classes:[t.openClass],styles:{}}:{classes:[t.closedClass],styles:Ss(t.dimension.property,"0px")})},events:(e,t)=>Br([Nr(Xs(),((o,n)=>{n.event.raw.propertyName===e.dimension.property&&(AT(o,e),t.isExpanded()&&It(o.element,e.dimension.property),(t.isExpanded()?e.onGrown:e.onShrunk)(o))}))])}),WT=[Xn("closedClass"),Xn("openClass"),Xn("shrinkingClass"),Xn("growingClass"),ns("getAnimationRoot"),wi("onShrunk"),wi("onStartShrink"),wi("onGrown"),wi("onStartGrow"),us("expanded",!1),Jn("dimension",jn("property",{width:[Oi("property","width"),Oi("getDimension",(e=>$t(e)+"px"))],height:[Oi("property","height"),Oi("getDimension",(e=>Ht(e)+"px"))]}))];const UT=hl({fields:WT,name:"sliding",active:LT,apis:NT,state:Object.freeze({__proto__:null,init:e=>{const t=xs(e.expanded);return ba({isExpanded:()=>!0===t.get(),isCollapsed:()=>!1===t.get(),setCollapsed:S(t.set,!1),setExpanded:S(t.set,!0),readState:()=>"expanded: "+t.get()})}})}),jT="container",GT=[nu("slotBehaviours",[])],$T=e=>"<alloy.field."+e+">",qT=(e,t)=>{const o=t=>Xu(e),n=(t,o)=>(n,s)=>ju(n,e,s).map((e=>t(e,s))).getOr(o),s=(e,t)=>"true"!==xt(e.element,"aria-hidden"),r=n(s,!1),a=n(((e,t)=>{if(s(e)){const o=e.element;_t(o,"display","none"),vt(o,"aria-hidden","true"),Or(e,yr(),{name:t,visible:!1})}})),i=(l=a,(e,t)=>{N(t,(t=>l(e,t)))});var l;const c=n(((e,t)=>{if(!s(e)){const o=e.element;It(o,"display"),Ct(o,"aria-hidden"),Or(e,yr(),{name:t,visible:!0})}})),d={getSlotNames:o,getSlot:(t,o)=>ju(t,e,o),isShowing:r,hideSlot:a,hideAllSlots:e=>i(e,o()),showSlot:c};return{uid:e.uid,dom:e.dom,components:t,behaviours:su(e.slotBehaviours),apis:d}},XT=ce({getSlotNames:(e,t)=>e.getSlotNames(t),getSlot:(e,t,o)=>e.getSlot(t,o),isShowing:(e,t,o)=>e.isShowing(t,o),hideSlot:(e,t,o)=>e.hideSlot(t,o),hideAllSlots:(e,t)=>e.hideAllSlots(t),showSlot:(e,t,o)=>e.showSlot(t,o)},(e=>ha(e))),JT={...XT,sketch:e=>{const t=(()=>{const e=[];return{slot:(t,o)=>(e.push(t),Pu(jT,$T(t),o)),record:y(e)}})(),o=e(t),n=t.record(),s=P(n,(e=>Bu({name:e,pname:$T(e)})));return em(jT,GT,s,qT,o)}},YT=kn([Yb,Kb,bs("onShow",b),bs("onHide",b),qb]),KT=e=>({element:()=>e.element.dom}),ZT=(e,t)=>{const o=P(ae(t),(e=>{const o=t[e],n=Ln((e=>Nn("sidebar",YT,e))(o));return{name:e,getApi:KT,onSetup:n.onSetup,onShow:n.onShow,onHide:n.onHide}}));return P(o,(t=>{const n=xs(b);return e.slot(t.name,{dom:{tag:"div",classes:["tox-sidebar__pane"]},behaviours:Uv([iy(t,n),ly(t,n),Fr(yr(),((e,t)=>{const n=t.event,s=G(o,(e=>e.name===n.name));s.each((t=>{(n.visible?t.onShow:t.onHide)(t.getApi(e))}))}))])})}))},QT=e=>JT.sketch((t=>({dom:{tag:"div",classes:["tox-sidebar__pane-container"]},components:ZT(t,e),slotBehaviours:Uv([Lr((e=>JT.hideAllSlots(e)))])}))),eE=e=>cm.getCurrent(e).bind((e=>UT.isGrowing(e)||UT.hasGrown(e)?cm.getCurrent(e).bind((e=>G(JT.getSlotNames(e),(t=>JT.isShowing(e,t))))):M.none())),tE=Qr("FixSizeEvent"),oE=Qr("AutoSizeEvent");var nE=Object.freeze({__proto__:null,block:(e,t,o,n)=>{vt(e.element,"aria-busy",!0);const s=t.getRoot(e).getOr(e),r=gl([Tp.config({mode:"special",onTab:()=>M.some(!0),onShiftTab:()=>M.some(!0)}),Wp.config({})]),a=n(s,r),i=s.getSystem().build(a);Rp.append(s,Ya(i)),i.hasConfigured(Tp)&&t.focus&&Tp.focusIn(i),o.isBlocked()||t.onBlock(e),o.blockWith((()=>Rp.remove(s,i)))},unblock:(e,t,o)=>{Ct(e.element,"aria-busy"),o.isBlocked()&&t.onUnblock(e),o.clear()}}),sE=[bs("getRoot",M.none),fs("focus",!0),wi("onBlock"),wi("onUnblock")];const rE=hl({fields:sE,name:"blocking",apis:nE,state:Object.freeze({__proto__:null,init:()=>{const e=Nl((e=>e.destroy()));return ba({readState:e.isSet,blockWith:t=>{e.set({destroy:t})},clear:e.clear,isBlocked:e.isSet})}})}),aE=e=>{const t=Ae(e),o=nt(t),n=(e=>{const t=void 0!==e.dom.attributes?e.dom.attributes:[];return j(t,((e,t)=>"class"===t.name?e:{...e,[t.name]:t.value}),{})})(t),s=(e=>Array.prototype.slice.call(e.dom.classList,0))(t),r=0===o.length?{}:{innerHtml:Gr(t)};return{tag:Pe(t),classes:s,attributes:n,...r}},iE=e=>cm.getCurrent(e).each((e=>wl(e.element))),lE=(e,t,o)=>{const n=xs(!1),s=Wl(),r=o=>{var s;n.get()&&(!(e=>"focusin"===e.type)(s=o)||!(s.composed?oe(s.composedPath()):M.from(s.target)).map(Ie).filter(Le).exists((e=>Fa(e,"mce-pastebin"))))&&(o.preventDefault(),iE(t()),e.editorManager.setActive(e))};e.inline||e.on("PreInit",(()=>{e.dom.bind(e.getWin(),"focusin",r),e.on("BeforeExecCommand",(e=>{"mcefocus"===e.command.toLowerCase()&&!0!==e.value&&r(e)}))}));const a=s=>{s!==n.get()&&(n.set(s),((e,t,o,n)=>{const s=t.element;if(((e,t)=>{const o="tabindex",n="data-mce-tabindex";M.from(e.iframeElement).map(Ie).each((e=>{t?(wt(e,o).each((t=>vt(e,n,t))),vt(e,o,-1)):(Ct(e,o),wt(e,n).each((t=>{vt(e,o,t),Ct(e,n)})))}))})(e,o),o)rE.block(t,(e=>(t,o)=>({dom:{tag:"div",attributes:{"aria-label":e.translate("Loading..."),tabindex:"0"},classes:["tox-throbber__busy-spinner"]},components:[{dom:aE('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}))(n)),It(s,"display"),Ct(s,"aria-hidden"),e.hasFocus()&&iE(t);else{const o=cm.getCurrent(t).exists((e=>Sl(e.element)));rE.unblock(t),_t(s,"display","none"),vt(s,"aria-hidden","true"),o&&e.focus()}})(e,t(),s,o.providers),((e,t)=>{e.dispatch("AfterProgressState",{state:t})})(e,s))};e.on("ProgressState",(t=>{if(s.on(clearTimeout),h(t.time)){const o=Eh.setEditorTimeout(e,(()=>a(t.state)),t.time);s.set(o)}else a(t.state),s.clear()}))},cE=(e,t,o)=>({within:e,extra:t,withinWidth:o}),dE=(e,t,o)=>{const n=j(e,((e,t)=>((e,t)=>{const n=o(e);return M.some({element:e,start:t,finish:t+n,width:n})})(t,e.len).fold(y(e),(t=>({len:t.finish,list:e.list.concat([t])})))),{len:0,list:[]}).list,s=W(n,(e=>e.finish<=t)),r=U(s,((e,t)=>e+t.width),0);return{within:s,extra:n.slice(s.length),withinWidth:r}},uE=e=>P(e,(e=>e.element)),mE=(e,t)=>{const o=P(t,(e=>Ya(e)));W_.setGroups(e,o)},gE=(e,t,o)=>{const n=t.builtGroups.get();if(0===n.length)return;const s=Gu(e,t,"primary"),r=Bx.getCoupled(e,"overflowGroup");_t(s.element,"visibility","hidden");const a=n.concat([r]),i=re(a,(e=>kl(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()))));o([]),mE(s,a);const l=((e,t,o,n)=>{const s=((e,t,o)=>{const n=dE(t,e,o);return 0===n.extra.length?M.some(n):M.none()})(e,t,o).getOrThunk((()=>dE(t,e-o(n),o))),r=s.within,a=s.extra,i=s.withinWidth;return 1===a.length&&a[0].width<=o(n)?((e,t,o)=>{const n=uE(e.concat(t));return cE(n,[],o)})(r,a,i):a.length>=1?((e,t,o,n)=>{const s=uE(e).concat([o]);return cE(s,uE(t),n)})(r,a,n,i):((e,t,o)=>cE(uE(e),[],o))(r,0,i)})($t(s.element),t.builtGroups.get(),(e=>$t(e.element)),r);0===l.extra.length?(Rp.remove(s,r),o([])):(mE(s,l.within),o(l.extra)),It(s.element,"visibility"),Vt(s.element),i.each(Wp.focus)},pE=y([nu("splitToolbarBehaviours",[Bx]),$n("builtGroups",(()=>xs([])))]),hE=y([yi(["overflowToggledClass"]),ls("getOverflowBounds"),Xn("lazySink"),$n("overflowGroups",(()=>xs([])))].concat(pE())),fE=y([Bu({factory:W_,schema:N_(),name:"primary"}),Au({schema:N_(),name:"overflow"}),Au({name:"overflow-button"}),Au({name:"overflow-group"})]),bE=y(((e,t)=>{((e,t)=>{const o=Gt.max(e,t,["margin-left","border-left-width","padding-left","padding-right","border-right-width","margin-right"]);_t(e,"max-width",o+"px")})(e,Math.floor(t))})),vE=y([yi(["toggledClass"]),Xn("lazySink"),Qn("fetch"),ls("getBounds"),ds("fireDismissalEventInstead",[us("event",fr())]),dc()]),yE=y([Au({name:"button",overrides:e=>({dom:{attributes:{"aria-haspopup":"true"}},buttonBehaviours:gl([Yp.config({toggleClass:e.markers.toggledClass,aria:{mode:"expanded"},toggleOnExecute:!1})])})}),Au({factory:W_,schema:N_(),name:"toolbar",overrides:e=>({toolbarBehaviours:gl([Tp.config({mode:"cyclic",onEscape:t=>(ju(t,e,"button").each(Wp.focus),M.none())})])})})]),xE=(e,t)=>{const o=Bx.getCoupled(e,"toolbarSandbox");Rd.isOpen(o)?Rd.close(o):Rd.open(o,t.toolbar())},wE=(e,t,o,n)=>{const s=o.getBounds.map((e=>e())),r=o.lazySink(e).getOrDie();ud.positionWithinBounds(r,t,{anchor:{type:"hotspot",hotspot:e,layouts:n,overrides:{maxWidthFunction:bE()}}},s)},SE=(e,t,o,n,s)=>{W_.setGroups(t,s),wE(e,t,o,n),Yp.on(e)},CE=rm({name:"FloatingToolbarButton",factory:(e,t,o,n)=>({...Mh.sketch({...n.button(),action:e=>{xE(e,n)},buttonBehaviours:iu({dump:n.button().buttonBehaviours},[Bx.config({others:{toolbarSandbox:t=>((e,t,o)=>{const n=ii();return{dom:{tag:"div",attributes:{id:n.id}},behaviours:gl([Tp.config({mode:"special",onEscape:e=>(Rd.close(e),M.some(!0))}),Rd.config({onOpen:(s,r)=>{o.fetch().get((s=>{SE(e,r,o,t.layouts,s),n.link(e.element),Tp.focusIn(r)}))},onClose:()=>{Yp.off(e),Wp.focus(e),n.unlink(e.element)},isPartOf:(t,o,n)=>li(o,n)||li(e,n),getAttachPoint:()=>o.lazySink(e).getOrDie()}),yl.config({channels:{...Ld({isExtraPart:_,...o.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...Ud({doReposition:()=>{Rd.getState(Bx.getCoupled(e,"toolbarSandbox")).each((n=>{wE(e,n,o,t.layouts)}))}})}})])}})(t,o,e)}})])}),apis:{setGroups:(t,n)=>{Rd.getState(Bx.getCoupled(t,"toolbarSandbox")).each((s=>{SE(t,s,e,o.layouts,n)}))},reposition:t=>{Rd.getState(Bx.getCoupled(t,"toolbarSandbox")).each((n=>{wE(t,n,e,o.layouts)}))},toggle:e=>{xE(e,n)},getToolbar:e=>Rd.getState(Bx.getCoupled(e,"toolbarSandbox")),isOpen:e=>Rd.isOpen(Bx.getCoupled(e,"toolbarSandbox"))}}),configFields:vE(),partFields:yE(),apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},getToolbar:(e,t)=>e.getToolbar(t),isOpen:(e,t)=>e.isOpen(t)}}),kE=y([Xn("items"),yi(["itemSelector"]),nu("tgroupBehaviours",[Tp])]),OE=y([Fu({name:"items",unit:"item"})]),_E=rm({name:"ToolbarGroup",configFields:kE(),partFields:OE(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:ru(e.tgroupBehaviours,[Tp.config({mode:"flow",selector:e.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}})}),TE=e=>P(e,(e=>Ya(e))),EE=(e,t,o)=>{gE(e,o,(n=>{o.overflowGroups.set(n),t.getOpt(e).each((e=>{CE.setGroups(e,TE(n))}))}))},ME=rm({name:"SplitFloatingToolbar",configFields:hE(),partFields:fE(),factory:(e,t,o,n)=>{const s=Bh(CE.sketch({fetch:()=>Vx((t=>{t(TE(e.overflowGroups.get()))})),layouts:{onLtr:()=>[Ji,Xi],onRtl:()=>[Xi,Ji],onBottomLtr:()=>[Ki,Yi],onBottomRtl:()=>[Yi,Ki]},getBounds:o.getOverflowBounds,lazySink:e.lazySink,fireDismissalEventInstead:{},markers:{toggledClass:e.markers.overflowToggledClass},parts:{button:n["overflow-button"](),toolbar:n.overflow()}}));return{uid:e.uid,dom:e.dom,components:t,behaviours:ru(e.splitToolbarBehaviours,[Bx.config({others:{overflowGroup:()=>_E.sketch({...n["overflow-group"](),items:[s.asSpec()]})}})]),apis:{setGroups:(t,o)=>{e.builtGroups.set(P(o,t.getSystem().build)),EE(t,s,e)},refresh:t=>EE(t,s,e),toggle:e=>{s.getOpt(e).each((e=>{CE.toggle(e)}))},isOpen:e=>s.getOpt(e).map(CE.isOpen).getOr(!1),reposition:e=>{s.getOpt(e).each((e=>{CE.reposition(e)}))},getOverflow:e=>s.getOpt(e).bind(CE.getToolbar)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t),getOverflow:(e,t)=>e.getOverflow(t)}}),BE=y([yi(["closedClass","openClass","shrinkingClass","growingClass","overflowToggledClass"]),wi("onOpened"),wi("onClosed")].concat(pE())),AE=y([Bu({factory:W_,schema:N_(),name:"primary"}),Bu({factory:W_,schema:N_(),name:"overflow",overrides:e=>({toolbarBehaviours:gl([UT.config({dimension:{property:"height"},closedClass:e.markers.closedClass,openClass:e.markers.openClass,shrinkingClass:e.markers.shrinkingClass,growingClass:e.markers.growingClass,onShrunk:t=>{ju(t,e,"overflow-button").each((e=>{Yp.off(e),Wp.focus(e)})),e.onClosed(t)},onGrown:t=>{Tp.focusIn(t),e.onOpened(t)},onStartGrow:t=>{ju(t,e,"overflow-button").each(Yp.on)}}),Tp.config({mode:"acyclic",onEscape:t=>(ju(t,e,"overflow-button").each(Wp.focus),M.some(!0))})])})}),Au({name:"overflow-button",overrides:e=>({buttonBehaviours:gl([Yp.config({toggleClass:e.markers.overflowToggledClass,aria:{mode:"pressed"},toggleOnExecute:!1})])})}),Au({name:"overflow-group"})]),DE=(e,t)=>{ju(e,t,"overflow-button").bind((()=>ju(e,t,"overflow"))).each((o=>{FE(e,t),UT.toggleGrow(o)}))},FE=(e,t)=>{ju(e,t,"overflow").each((o=>{gE(e,t,(e=>{const t=P(e,(e=>Ya(e)));W_.setGroups(o,t)})),ju(e,t,"overflow-button").each((e=>{UT.hasGrown(o)&&Yp.on(e)})),UT.refresh(o)}))},IE=rm({name:"SplitSlidingToolbar",configFields:BE(),partFields:AE(),factory:(e,t,o,n)=>{const s="alloy.toolbar.toggle";return{uid:e.uid,dom:e.dom,components:t,behaviours:ru(e.splitToolbarBehaviours,[Bx.config({others:{overflowGroup:e=>_E.sketch({...n["overflow-group"](),items:[Mh.sketch({...n["overflow-button"](),action:t=>{kr(e,s)}})]})}}),zp("toolbar-toggle-events",[Fr(s,(t=>{DE(t,e)}))])]),apis:{setGroups:(t,o)=>{((t,o)=>{const n=P(o,t.getSystem().build);e.builtGroups.set(n)})(t,o),FE(t,e)},refresh:t=>FE(t,e),toggle:t=>DE(t,e),isOpen:t=>((e,t)=>ju(e,t,"overflow").map(UT.hasGrown).getOr(!1))(t,e)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},toggle:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t)}}),VE=e=>{const t=e.title.fold((()=>({})),(e=>({attributes:{title:e}})));return{dom:{tag:"div",classes:["tox-toolbar__group"],...t},components:[_E.parts.items({})],items:e.items,markers:{itemSelector:"*:not(.tox-split-button) > .tox-tbtn:not([disabled]), .tox-split-button:not([disabled]), .tox-toolbar-nav-js:not([disabled])"},tgroupBehaviours:gl([Cw.config({}),Wp.config({})])}},RE=e=>_E.sketch(VE(e)),zE=(e,t)=>{const o=Lr((t=>{const o=P(e.initGroups,RE);W_.setGroups(t,o)}));return gl([ry(e.providers.isDisabled),oy(),Tp.config({mode:t,onEscape:e.onEscape,selector:".tox-toolbar__group"}),zp("toolbar-events",[o])])},HE=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return{uid:e.uid,dom:{tag:"div",classes:["tox-toolbar-overlord"]},parts:{"overflow-group":VE({title:M.none(),items:[]}),"overflow-button":tO({name:"more",icon:M.some("more-drawer"),enabled:!0,tooltip:M.some("More..."),primary:!1,buttonType:M.none(),borderless:!1},M.none(),e.providers)},splitToolbarBehaviours:zE(e,t)}},PE=e=>{const t=HE(e),o=ME.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}});return ME.sketch({...t,lazySink:e.getSink,getOverflowBounds:()=>{const t=e.moreDrawerData.lazyHeader().element,o=qo(t),n=Ze(t),s=qo(n),r=Math.max(n.dom.scrollHeight,s.height);return Go(o.x+4,s.y,o.width-8,r)},parts:{...t.parts,overflow:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:e.attributes}}},components:[o],markers:{overflowToggledClass:"tox-tbtn--enabled"}})},NE=e=>{const t=IE.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}}),o=IE.parts.overflow({dom:{tag:"div",classes:["tox-toolbar__overflow"]}}),n=HE(e);return IE.sketch({...n,components:[t,o],markers:{openClass:"tox-toolbar__overflow--open",closedClass:"tox-toolbar__overflow--closed",growingClass:"tox-toolbar__overflow--growing",shrinkingClass:"tox-toolbar__overflow--shrinking",overflowToggledClass:"tox-tbtn--enabled"},onOpened:e=>{e.getSystem().broadcastOn([mT()],{type:"opened"})},onClosed:e=>{e.getSystem().broadcastOn([mT()],{type:"closed"})}})},LE=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return W_.sketch({uid:e.uid,dom:{tag:"div",classes:["tox-toolbar"].concat(e.type===Uh.scrolling?["tox-toolbar--scrolling"]:[])},components:[W_.parts.groups({})],toolbarBehaviours:zE(e,t)})},WE=V_.optional({factory:TT,name:"menubar",schema:[Xn("backstage")]}),UE=V_.optional({factory:{sketch:e=>P_.sketch({uid:e.uid,dom:e.dom,listBehaviours:gl([Tp.config({mode:"acyclic",selector:".tox-toolbar"})]),makeItem:()=>LE({type:e.type,uid:Qr("multiple-toolbar-item"),cyclicKeying:!1,initGroups:[],providers:e.providers,onEscape:()=>(e.onEscape(),M.some(!0))}),setupItem:(e,t,o,n)=>{W_.setGroups(t,o)},shell:!0})},name:"multiple-toolbar",schema:[Xn("dom"),Xn("onEscape")]}),jE=V_.optional({factory:{sketch:e=>{const t=(e=>e.type===Uh.sliding?NE:e.type===Uh.floating?PE:LE)(e);return t({type:e.type,uid:e.uid,onEscape:()=>(e.onEscape(),M.some(!0)),cyclicKeying:!1,initGroups:[],getSink:e.getSink,providers:e.providers,moreDrawerData:{lazyToolbar:e.lazyToolbar,lazyMoreButton:e.lazyMoreButton,lazyHeader:e.lazyHeader},attributes:e.attributes})}},name:"toolbar",schema:[Xn("dom"),Xn("onEscape"),Xn("getSink")]}),GE=V_.optional({factory:{sketch:e=>{const t=e.editor,o=e.sticky?xT:G_;return{uid:e.uid,dom:e.dom,components:e.components,behaviours:gl(o(t,e.sharedBackstage))}}},name:"header",schema:[Xn("dom")]}),$E=V_.optional({factory:{sketch:e=>({uid:e.uid,dom:e.dom,components:[{dom:{tag:"a",attributes:{href:"https://www.tiny.cloud/tinymce-self-hosted-premium-features/?utm_source=TinyMCE&utm_medium=SPAP&utm_campaign=SPAP&utm_id=editorreferral",rel:"noopener",target:"_blank","aria-hidden":"true"},classes:["tox-promotion-link"],innerHtml:"\u26a1\ufe0fUpgrade"}}]})},name:"promotion",schema:[Xn("dom")]}),qE=V_.optional({name:"socket",schema:[Xn("dom")]}),XE=V_.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-sidebar"],attributes:{role:"complementary"}},components:[{dom:{tag:"div",classes:["tox-sidebar__slider"]},components:[],behaviours:gl([Cw.config({}),Wp.config({}),UT.config({dimension:{property:"width"},closedClass:"tox-sidebar--sliding-closed",openClass:"tox-sidebar--sliding-open",shrinkingClass:"tox-sidebar--sliding-shrinking",growingClass:"tox-sidebar--sliding-growing",onShrunk:e=>{cm.getCurrent(e).each(JT.hideAllSlots),kr(e,oE)},onGrown:e=>{kr(e,oE)},onStartGrow:e=>{Or(e,tE,{width:At(e.element,"width").getOr("")})},onStartShrink:e=>{Or(e,tE,{width:$t(e.element)+"px"})}}),Rp.config({}),cm.config({find:e=>{const t=Rp.contents(e);return oe(t)}})])}],behaviours:gl([$C(0),zp("sidebar-sliding-events",[Fr(tE,((e,t)=>{_t(e.element,"width",t.event.width)})),Fr(oE,((e,t)=>{It(e.element,"width")}))])])})},name:"sidebar",schema:[Xn("dom")]}),JE=V_.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",attributes:{"aria-hidden":"true"},classes:["tox-throbber"],styles:{display:"none"}},behaviours:gl([Rp.config({}),rE.config({focus:!1}),cm.config({find:e=>oe(e.components())})]),components:[]})},name:"throbber",schema:[Xn("dom")]});var YE=rm({name:"OuterContainer",factory:(e,t,o)=>{const n={getSocket:t=>I_.getPart(t,e,"socket"),setSidebar:(t,o,n)=>{I_.getPart(t,e,"sidebar").each((e=>((e,t,o)=>{cm.getCurrent(e).each((e=>{Rp.set(e,[QT(t)]);const n=null==o?void 0:o.toLowerCase();r(n)&&ve(t,n)&&cm.getCurrent(e).each((t=>{JT.showSlot(t,n),UT.immediateGrow(e),It(e.element,"width")}))}))})(e,o,n)))},toggleSidebar:(t,o)=>{I_.getPart(t,e,"sidebar").each((e=>((e,t)=>{cm.getCurrent(e).each((e=>{cm.getCurrent(e).each((o=>{UT.hasGrown(e)?JT.isShowing(o,t)?UT.shrink(e):(JT.hideAllSlots(o),JT.showSlot(o,t)):(JT.hideAllSlots(o),JT.showSlot(o,t),UT.grow(e))}))}))})(e,o)))},whichSidebar:t=>I_.getPart(t,e,"sidebar").bind(eE).getOrNull(),getHeader:t=>I_.getPart(t,e,"header"),getToolbar:t=>I_.getPart(t,e,"toolbar"),setToolbar:(t,o)=>{I_.getPart(t,e,"toolbar").each((e=>{const t=P(o,RE);e.getApis().setGroups(e,t)}))},setToolbars:(t,o)=>{I_.getPart(t,e,"multiple-toolbar").each((e=>{const t=P(o,(e=>P(e,RE)));P_.setItems(e,t)}))},refreshToolbar:t=>{I_.getPart(t,e,"toolbar").each((e=>e.getApis().refresh(e)))},toggleToolbarDrawer:t=>{I_.getPart(t,e,"toolbar").each((e=>{var t,o;o=t=>t(e),null!=(t=e.getApis().toggle)?M.some(o(t)):M.none()}))},isToolbarDrawerToggled:t=>I_.getPart(t,e,"toolbar").bind((e=>M.from(e.getApis().isOpen).map((t=>t(e))))).getOr(!1),getThrobber:t=>I_.getPart(t,e,"throbber"),focusToolbar:t=>{I_.getPart(t,e,"toolbar").orThunk((()=>I_.getPart(t,e,"multiple-toolbar"))).each((e=>{Tp.focusIn(e)}))},setMenubar:(t,o)=>{I_.getPart(t,e,"menubar").each((e=>{TT.setMenus(e,o)}))},focusMenubar:t=>{I_.getPart(t,e,"menubar").each((e=>{TT.focus(e)}))}};return{uid:e.uid,dom:e.dom,components:t,apis:n,behaviours:e.behaviours}},configFields:[Xn("dom"),Xn("behaviours")],partFields:[GE,WE,jE,UE,qE,XE,$E,JE],apis:{getSocket:(e,t)=>e.getSocket(t),setSidebar:(e,t,o,n)=>{e.setSidebar(t,o,n)},toggleSidebar:(e,t,o)=>{e.toggleSidebar(t,o)},whichSidebar:(e,t)=>e.whichSidebar(t),getHeader:(e,t)=>e.getHeader(t),getToolbar:(e,t)=>e.getToolbar(t),setToolbar:(e,t,o)=>{e.setToolbar(t,o)},setToolbars:(e,t,o)=>{e.setToolbars(t,o)},refreshToolbar:(e,t)=>e.refreshToolbar(t),toggleToolbarDrawer:(e,t)=>{e.toggleToolbarDrawer(t)},isToolbarDrawerToggled:(e,t)=>e.isToolbarDrawerToggled(t),getThrobber:(e,t)=>e.getThrobber(t),setMenubar:(e,t,o)=>{e.setMenubar(t,o)},focusMenubar:(e,t)=>{e.focusMenubar(t)},focusToolbar:(e,t)=>{e.focusToolbar(t)}}});const KE={file:{title:"File",items:"newdocument restoredraft | preview | export print | deleteallconversations"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall | searchreplace"},view:{title:"View",items:"code | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments"},insert:{title:"Insert",items:"image link media addcomment pageembed template codesample inserttable | charmap emoticons hr | pagebreak nonbreaking anchor tableofcontents footnotes | mergetags | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | styles blocks fontfamily fontsize align lineheight | forecolor backcolor | language | removeformat"},tools:{title:"Tools",items:"spellchecker spellcheckerlanguage | autocorrect capitalization | a11ycheck code wordcount"},table:{title:"Table",items:"inserttable | cell row column | advtablesort | tableprops deletetable"},help:{title:"Help",items:"help"}},ZE=e=>e.split(" "),QE=(e,t)=>{const o={...KE,...t.menus},n=ae(t.menus).length>0,s=void 0===t.menubar||!0===t.menubar?ZE("file edit view insert format tools table help"):ZE(!1===t.menubar?"":t.menubar),a=W(s,(e=>{const o=ve(KE,e);return n?o||be(t.menus,e).exists((e=>ve(e,"items"))):o})),i=P(a,(n=>{const s=o[n];return((e,t,o)=>{const n=cf(o).split(/[ ,]/);return{text:e.title,getItems:()=>X(e.items,(e=>{const o=e.toLowerCase();return 0===o.trim().length||R(n,(e=>e===o))?[]:"separator"===o||"|"===o?[{type:"separator"}]:t.menuItems[o]?[t.menuItems[o]]:[]}))}})({title:s.title,items:ZE(s.items)},t,e)}));return W(i,(e=>e.getItems().length>0&&R(e.getItems(),(e=>r(e)||"separator"!==e.type))))},eM=e=>{const t=()=>{e._skinLoaded=!0,(e=>{e.dispatch("SkinLoaded")})(e)};return()=>{e.initialized?t():e.on("init",t)}},tM=(e,t,o)=>(e.on("remove",(()=>o.unload(t))),o.load(t)),oM=(e,t)=>tM(e,t+"/skin.min.css",e.ui.styleSheetLoader),nM=(e,t)=>{var o;return o=Ie(e.getElement()),mt(o).isSome()?tM(e,t+"/skin.shadowdom.min.css",Gh.DOM.styleSheetLoader):Promise.resolve()},sM=(e,t)=>{const o=Vf(t);o&&t.contentCSS.push(o+(e?"/content.inline":"/content")+".min.css"),!Ff(t)&&r(o)?Promise.all([oM(t,o),nM(t,o)]).then(eM(t),((e,t)=>()=>((e,t)=>{e.dispatch("SkinLoadError",t)})(e,{message:"Skin could not be loaded"}))(t)):eM(t)()},rM=S(sM,!1),aM=S(sM,!0),iM=(e,t)=>o=>{const n=Ll(),s=()=>{o.setActive(e.formatter.match(t));const s=e.formatter.formatChanged(t,o.setActive);n.set(s)};return e.initialized?s():e.once("init",s),()=>{e.off("init",s),n.clear()}},lM=(e,t,o)=>n=>{const s=()=>o(n),r=()=>{o(n),e.on(t,s)};return e.initialized?r():e.once("init",r),()=>{e.off("init",r),e.off(t,s)}},cM=e=>t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("mceToggleFormat",!1,t.format)}))},dM=(e,t)=>()=>e.execCommand(t),uM=(e,t,o)=>{const n=(e,n,r,a)=>{const i=t.shared.providers.translate(e.title);if("separator"===e.type)return M.some({type:"separator",text:i});if("submenu"===e.type){const t=X(e.getStyleItems(),(e=>s(e,n,a)));return 0===n&&t.length<=0?M.none():M.some({type:"nestedmenuitem",text:i,enabled:t.length>0,getSubmenuItems:()=>X(e.getStyleItems(),(e=>s(e,n,a)))})}return M.some({type:"togglemenuitem",text:i,icon:e.icon,active:e.isSelected(a),enabled:!r,onAction:o.onAction(e),...e.getStylePreview().fold((()=>({})),(e=>({meta:{style:e}})))})},s=(e,t,s)=>{const r="formatter"===e.type&&o.isInvalid(e);return 0===t?r?[]:n(e,t,!1,s).toArray():n(e,t,r,s).toArray()},r=e=>{const t=o.getCurrentValue(),n=o.shouldHide?0:1;return X(e,(e=>s(e,n,t)))};return{validateItems:r,getFetch:(e,t)=>(o,n)=>{const s=t(),a=r(s);n(Sk(a,Kf.CLOSE_ON_EXECUTE,e,{isHorizontalMenu:!1,search:M.none()}))}}},mM=(e,t,o)=>{const n=o.dataset,s="basic"===n.type?()=>P(n.data,(e=>a_(e,o.isSelectedFor,o.getPreviewFor))):n.getData;return{items:uM(0,t,o),getStyleItems:s}},gM=(e,t,o)=>{const{items:n,getStyleItems:s}=mM(0,t,o),r=lM(e,"NodeChange",(e=>{const t=e.getComponent();o.updateText(t)}));return vk({text:o.icon.isSome()?M.none():o.text,icon:o.icon,tooltip:M.from(o.tooltip),role:M.none(),fetch:n.getFetch(t,s),onSetup:r,getApi:e=>({getComponent:y(e)}),columns:1,presets:"normal",classes:o.icon.isSome()?[]:["bespoke"],dropdownBehaviours:[]},"tox-tbtn",t.shared)};var pM;!function(e){e[e.SemiColon=0]="SemiColon",e[e.Space=1]="Space"}(pM||(pM={}));const hM=(e,t,o)=>{const n=(s=((e,t)=>t===pM.SemiColon?e.replace(/;$/,"").split(";"):e.split(" "))(e.options.get(t),o),P(s,(e=>{let t=e,o=e;const n=e.split("=");return n.length>1&&(t=n[0],o=n[1]),{title:t,format:o}})));var s;return{type:"basic",data:n}},fM=[{title:"Left",icon:"align-left",format:"alignleft",command:"JustifyLeft"},{title:"Center",icon:"align-center",format:"aligncenter",command:"JustifyCenter"},{title:"Right",icon:"align-right",format:"alignright",command:"JustifyRight"},{title:"Justify",icon:"align-justify",format:"alignjustify",command:"JustifyFull"}],bM=e=>{const t={type:"basic",data:fM};return{tooltip:"Align",text:M.none(),icon:M.some("align-left"),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:M.none,getPreviewFor:e=>M.none,onAction:t=>()=>G(fM,(e=>e.format===t.format)).each((t=>e.execCommand(t.command))),updateText:t=>{const o=G(fM,(t=>e.formatter.match(t.format))).fold(y("left"),(e=>e.title.toLowerCase()));Or(t,bk,{icon:`align-${o}`})},dataset:t,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},vM=(e,t)=>{const o=t(),n=P(o,(e=>e.format));return M.from(e.formatter.closest(n)).bind((e=>G(o,(t=>t.format===e)))).orThunk((()=>Ce(e.formatter.match("p"),{title:"Paragraph",format:"p"})))},yM=e=>{const t="Paragraph",o=hM(e,"block_formats",pM.SemiColon);return{tooltip:"Blocks",text:M.some(t),icon:M.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:M.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return o?M.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):M.none()},onAction:cM(e),updateText:n=>{const s=vM(e,(()=>o.data)).fold(y(t),(e=>e.title));Or(n,fk,{text:s})},dataset:o,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},xM=["-apple-system","Segoe UI","Roboto","Helvetica Neue","sans-serif"],wM=e=>{const t=e.split(/\s*,\s*/);return P(t,(e=>e.replace(/^['"]+|['"]+$/g,"")))},SM=e=>{const t="System Font",o=()=>{const o=e=>e?wM(e)[0]:"",s=e.queryCommandValue("FontName"),r=n.data,a=s?s.toLowerCase():"",i=G(r,(e=>{const t=e.format;return t.toLowerCase()===a||o(t).toLowerCase()===o(a).toLowerCase()})).orThunk((()=>Ce((e=>0===e.indexOf("-apple-system")&&(()=>{const t=wM(e.toLowerCase());return J(xM,(e=>t.indexOf(e.toLowerCase())>-1))})())(a),{title:t,format:a})));return{matchOpt:i,font:s}},n=hM(e,"font_family_formats",pM.SemiColon);return{tooltip:"Fonts",text:M.some(t),icon:M.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getCurrentValue:()=>{const{matchOpt:e}=o();return e},getPreviewFor:e=>()=>M.some({tag:"div",styles:-1===e.indexOf("dings")?{"font-family":e}:{}}),onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontName",!1,t.format)}))},updateText:e=>{const{matchOpt:t,font:n}=o(),s=t.fold(y(n),(e=>e.title));Or(e,fk,{text:s})},dataset:n,shouldHide:!1,isInvalid:_}},CM={"8pt":"1","10pt":"2","12pt":"3","14pt":"4","18pt":"5","24pt":"6","36pt":"7"},kM={"xx-small":"7pt","x-small":"8pt",small:"10pt",medium:"12pt",large:"14pt","x-large":"18pt","xx-large":"24pt"},OM=(e,t)=>/[0-9.]+px$/.test(e)?((e,t)=>{const o=Math.pow(10,t);return Math.round(e*o)/o})(72*parseInt(e,10)/96,t||0)+"pt":be(kM,e).getOr(e),_M=e=>be(CM,e).getOr(""),TM=e=>{const t=()=>{let t=M.none();const o=n.data,s=e.queryCommandValue("FontSize");if(s)for(let e=3;t.isNone()&&e>=0;e--){const n=OM(s,e),r=_M(n);t=G(o,(e=>e.format===s||e.format===n||e.format===r))}return{matchOpt:t,size:s}},o=y(M.none),n=hM(e,"font_size_formats",pM.Space);return{tooltip:"Font sizes",text:M.some("12pt"),icon:M.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getPreviewFor:o,getCurrentValue:()=>{const{matchOpt:e}=t();return e},onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontSize",!1,t.format)}))},updateText:e=>{const{matchOpt:o,size:n}=t(),s=o.fold(y(n),(e=>e.title));Or(e,fk,{text:s})},dataset:n,shouldHide:!1,isInvalid:_}},EM=(e,t)=>{const o="Paragraph";return{tooltip:"Formats",text:M.some(o),icon:M.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:M.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return void 0!==o?M.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):M.none()},onAction:cM(e),updateText:t=>{const n=e=>t_(e)?X(e.items,n):o_(e)?[{title:e.title,format:e.format}]:[],s=X(r_(e),n),r=vM(e,y(s)).fold(y(o),(e=>e.title));Or(t,fk,{text:r})},shouldHide:af(e),isInvalid:t=>!e.formatter.canApply(t.format),dataset:t}};var MM=Object.freeze({__proto__:null,events:(e,t)=>{const o=(o,n)=>{e.updateState.each((e=>{const s=e(o,n);t.set(s)})),e.renderComponents.each((s=>{const r=s(n,t.get());(e.reuseDom?Mp:Ep)(o,r)}))};return Br([Fr(or(),((t,n)=>{const s=n;if(!s.universal){const n=e.channel;V(s.channels,n)&&o(t,s.data)}})),Lr(((t,n)=>{e.initialData.each((e=>{o(t,e)}))}))])}}),BM=Object.freeze({__proto__:null,getState:(e,t,o)=>o}),AM=[Xn("channel"),ns("renderComponents"),ns("updateState"),ns("initialData"),fs("reuseDom",!0)];const DM=hl({fields:AM,name:"reflecting",active:MM,apis:BM,state:Object.freeze({__proto__:null,init:()=>{const e=xs(M.none());return{readState:()=>e.get().getOr("none"),get:e.get,set:e.set,clear:()=>e.set(M.none())}}})}),FM=y([Xn("toggleClass"),Xn("fetch"),Ci("onExecute"),us("getHotspot",M.some),us("getAnchorOverrides",y({})),dc(),Ci("onItemExecute"),ns("lazySink"),Xn("dom"),wi("onOpen"),nu("splitDropdownBehaviours",[Bx,Tp,Wp]),us("matchWidth",!1),us("useMinWidth",!1),us("eventOrder",{}),ns("role")].concat(qx())),IM=Bu({factory:Mh,schema:[Xn("dom")],name:"arrow",defaults:()=>({buttonBehaviours:gl([Wp.revoke()])}),overrides:e=>({dom:{tag:"span",attributes:{role:"presentation"}},action:t=>{t.getSystem().getByUid(e.uid).each(_r)},buttonBehaviours:gl([Yp.config({toggleOnExecute:!1,toggleClass:e.toggleClass})])})}),VM=Bu({factory:Mh,schema:[Xn("dom")],name:"button",defaults:()=>({buttonBehaviours:gl([Wp.revoke()])}),overrides:e=>({dom:{tag:"span",attributes:{role:"presentation"}},action:t=>{t.getSystem().getByUid(e.uid).each((o=>{e.onExecute(o,t)}))}})}),RM=y([IM,VM,Du({factory:{sketch:e=>({uid:e.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:e.text}})},schema:[Xn("text")],name:"aria-descriptor"}),Au({schema:[vi()],name:"menu",defaults:e=>({onExecute:(t,o)=>{t.getSystem().getByUid(e.uid).each((n=>{e.onItemExecute(n,t,o)}))}})}),Hx()]),zM=rm({name:"SplitDropdown",configFields:FM(),partFields:RM(),factory:(e,t,o,n)=>{const s=e=>{cm.getCurrent(e).each((e=>{Fm.highlightFirst(e),Tp.focusIn(e)}))},r=t=>{Wx(e,x,t,n,s,kh.HighlightMenuAndItem).get(b)},a=t=>{const o=Gu(t,e,"button");return _r(o),M.some(!0)},i={...Br([Lr(((t,o)=>{ju(t,e,"aria-descriptor").each((e=>{const o=Qr("aria");vt(e.element,"id",o),vt(t.element,"aria-describedby",o)}))}))]),...Zp(M.some(r))},l={repositionMenus:e=>{Yp.isOn(e)&&$x(e)}};return{uid:e.uid,dom:e.dom,components:t,apis:l,eventOrder:{...e.eventOrder,[nr()]:["disabling","toggling","alloy.base.behaviour"]},events:i,behaviours:ru(e.splitDropdownBehaviours,[Bx.config({others:{sandbox:t=>{const o=Gu(t,e,"arrow");return Gx(e,t,{onOpen:()=>{Yp.on(o),Yp.on(t)},onClose:()=>{Yp.off(o),Yp.off(t)}})}}}),Tp.config({mode:"special",onSpace:a,onEnter:a,onDown:e=>(r(e),M.some(!0))}),Wp.config({}),Yp.config({toggleOnExecute:!1,aria:{mode:"expanded"}})]),domModification:{attributes:{role:e.role.getOr("button"),"aria-haspopup":!0}}}},apis:{repositionMenus:(e,t)=>e.repositionMenus(t)}}),HM=e=>({isEnabled:()=>!Cm.isDisabled(e),setEnabled:t=>Cm.set(e,!t)}),PM=e=>({setActive:t=>{Yp.set(e,t)},isActive:()=>Yp.isOn(e),isEnabled:()=>!Cm.isDisabled(e),setEnabled:t=>Cm.set(e,!t)}),NM=(e,t)=>e.map((e=>({"aria-label":t.translate(e),title:t.translate(e)}))).getOr({}),LM=Qr("focus-button"),WM=(e,t,o,n,s,r)=>({dom:{tag:"button",classes:["tox-tbtn"].concat(t.isSome()?["tox-tbtn--select"]:[]),attributes:NM(o,r)},components:uy([e.map((e=>gk(e,r.icons))),t.map((e=>hk(e,"tox-tbtn",r)))]),eventOrder:{[Vs()]:["focusing","alloy.base.behaviour","common-button-display-events"]},buttonBehaviours:gl([ry(r.isDisabled),oy(),zp("common-button-display-events",[Fr(Vs(),((e,t)=>{t.event.prevent(),kr(e,LM)}))])].concat(n.map((o=>DM.config({channel:o,initialData:{icon:e,text:t},renderComponents:(e,t)=>uy([e.icon.map((e=>gk(e,r.icons))),e.text.map((e=>hk(e,"tox-tbtn",r)))])}))).toArray()).concat(s.getOr([])))}),UM=(e,t,o)=>{const n=xs(b),s=WM(e.icon,e.text,e.tooltip,M.none(),M.none(),o);return Mh.sketch({dom:s.dom,components:s.components,eventOrder:uk,buttonBehaviours:gl([zp("toolbar-button-events",[(r={onAction:e.onAction,getApi:t.getApi},jr(((e,t)=>{ay(r,e)((t=>{Or(e,dk,{buttonApi:t}),r.onAction(t)}))}))),iy(t,n),ly(t,n)]),ry((()=>!e.enabled||o.isDisabled())),oy()].concat(t.toolbarButtonBehaviours))});var r},jM=(e,t,o)=>UM(e,{toolbarButtonBehaviours:o.length>0?[zp("toolbarButtonWith",o)]:[],getApi:HM,onSetup:e.onSetup},t),GM=(e,t,o)=>UM(e,{toolbarButtonBehaviours:[Rp.config({}),Yp.config({toggleClass:"tox-tbtn--enabled",aria:{mode:"pressed"},toggleOnExecute:!1})].concat(o.length>0?[zp("toolbarToggleButtonWith",o)]:[]),getApi:PM,onSetup:e.onSetup},t),$M=(e,t,o)=>n=>Vx((e=>t.fetch(e))).map((s=>M.from(sw(cn(bx(Qr("menu-value"),s,(o=>{t.onItemAction(e(n),o)}),t.columns,t.presets,Kf.CLOSE_ON_EXECUTE,t.select.getOr(_),o),{movement:yx(t.columns,t.presets),menuBehaviours:Uv("auto"!==t.columns?[]:[Lr(((e,o)=>{Wv(e,4,lb(t.presets)).each((({numRows:t,numColumns:o})=>{Tp.setGridSize(e,t,o)}))}))])}))))),qM=[{name:"history",items:["undo","redo"]},{name:"styles",items:["styles"]},{name:"formatting",items:["bold","italic"]},{name:"alignment",items:["alignleft","aligncenter","alignright","alignjustify"]},{name:"indentation",items:["outdent","indent"]},{name:"permanent pen",items:["permanentpen"]},{name:"comments",items:["addcomment"]}],XM=(e,t)=>(o,n,s)=>{const r=e(o).mapError((e=>Un(e))).getOrDie();return t(r,n,s)},JM={button:XM(pv,((e,t)=>{return o=e,n=t.shared.providers,jM(o,n,[]);var o,n})),togglebutton:XM(bv,((e,t)=>{return o=e,n=t.shared.providers,GM(o,n,[]);var o,n})),menubutton:XM(OT,((e,t)=>Zk(e,"tox-tbtn",t,M.none()))),splitbutton:XM((e=>Nn("SplitButton",_T,e)),((e,t)=>((e,t)=>{const o=Qr("channel-update-split-dropdown-display"),n=e=>({isEnabled:()=>!Cm.isDisabled(e),setEnabled:t=>Cm.set(e,!t),setIconFill:(t,o)=>{si(e.element,'svg path[id="'+t+'"], rect[id="'+t+'"]').each((e=>{vt(e,"fill",o)}))},setActive:t=>{vt(e.element,"aria-pressed",t),si(e.element,"span").each((o=>{e.getSystem().getByDom(o).each((e=>Yp.set(e,t)))}))},isActive:()=>si(e.element,"span").exists((t=>e.getSystem().getByDom(t).exists(Yp.isOn)))}),s=xs(b),r={getApi:n,onSetup:e.onSetup};return zM.sketch({dom:{tag:"div",classes:["tox-split-button"],attributes:{"aria-pressed":!1,...NM(e.tooltip,t.providers)}},onExecute:t=>{e.onAction(n(t))},onItemExecute:(e,t,o)=>{},splitDropdownBehaviours:gl([sy(t.providers.isDisabled),oy(),zp("split-dropdown-events",[Fr(LM,Wp.focus),iy(r,s),ly(r,s)]),Xw.config({})]),eventOrder:{[pr()]:["alloy.base.behaviour","split-dropdown-events"]},toggleClass:"tox-tbtn--enabled",lazySink:t.getSink,fetch:$M(n,e,t.providers),parts:{menu:pb(0,e.columns,e.presets)},components:[zM.parts.button(WM(e.icon,e.text,M.none(),M.some(o),M.some([Yp.config({toggleClass:"tox-tbtn--enabled",toggleOnExecute:!1})]),t.providers)),zM.parts.arrow({dom:{tag:"button",classes:["tox-tbtn","tox-split-button__chevron"],innerHtml:zh("chevron-down",t.providers.icons)},buttonBehaviours:gl([sy(t.providers.isDisabled),oy(),Hh()])}),zM.parts["aria-descriptor"]({text:t.providers.translate("To open the popup, press Shift+Enter")})]})})(e,t.shared))),grouptoolbarbutton:XM((e=>Nn("GroupToolbarButton",ST,e)),((e,t,o)=>{const n=o.ui.registry.getAll().buttons,s={[lc]:t.shared.header.isPositionedAtTop()?ic.TopToBottom:ic.BottomToTop};if(df(o)===Uh.floating)return((e,t,o,n)=>{const s=t.shared;return CE.sketch({lazySink:s.getSink,fetch:()=>Vx((t=>{t(P(o(e.items),RE))})),markers:{toggledClass:"tox-tbtn--enabled"},parts:{button:WM(e.icon,e.text,e.tooltip,M.none(),M.none(),s.providers),toolbar:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:n}}}})})(e,t,(e=>KM(o,{buttons:n,toolbar:e,allowToolbarGroups:!1},t,M.none())),s);throw new Error("Toolbar groups are only supported when using floating toolbar mode")}))},YM={styles:(e,t)=>{const o={type:"advanced",...t.styles};return gM(e,t,EM(e,o))},fontsize:(e,t)=>gM(e,t,TM(e)),fontfamily:(e,t)=>gM(e,t,SM(e)),blocks:(e,t)=>gM(e,t,yM(e)),align:(e,t)=>gM(e,t,bM(e))},KM=(e,t,o,n)=>{const s=(e=>{const t=e.toolbar,o=e.buttons;return!1===t?[]:void 0===t||!0===t?(e=>{const t=P(qM,(t=>{const o=W(t.items,(t=>ve(e,t)||ve(YM,t)));return{name:t.name,items:o}}));return W(t,(e=>e.items.length>0))})(o):r(t)?(e=>{const t=e.split("|");return P(t,(e=>({items:e.trim().split(" ")})))})(t):(e=>f(e,(e=>ve(e,"name")&&ve(e,"items"))))(t)?t:(console.error("Toolbar type should be string, string[], boolean or ToolbarGroup[]"),[])})(t),a=P(s,(s=>{const r=X(s.items,(s=>0===s.trim().length?[]:((e,t,o,n,s,r)=>be(t,o.toLowerCase()).orThunk((()=>r.bind((e=>re(e,(e=>be(t,e+o.toLowerCase()))))))).fold((()=>be(YM,o.toLowerCase()).map((t=>t(e,s)))),(t=>"grouptoolbarbutton"!==t.type||n?((e,t,o)=>be(JM,e.type).fold((()=>(console.error("skipping button defined by",e),M.none())),(n=>M.some(n(e,t,o)))))(t,s,e):(console.warn(`Ignoring the '${o}' toolbar button. Group toolbar buttons are only supported when using floating toolbar mode and cannot be nested.`),M.none()))))(e,t.buttons,s,t.allowToolbarGroups,o,n).toArray()));return{title:M.from(e.translate(s.name)),items:r}}));return W(a,(e=>e.items.length>0))},ZM=(e,t,o,n)=>{const s=t.outerContainer,a=o.toolbar,i=o.buttons;if(f(a,r)){const t=a.map((t=>{const s={toolbar:t,buttons:i,allowToolbarGroups:o.allowToolbarGroups};return KM(e,s,n,M.none())}));YE.setToolbars(s,t)}else YE.setToolbar(s,KM(e,o,n,M.none()))},QM=_o(),eB=QM.os.isiOS()&&QM.os.version.major<=12;var tB=Object.freeze({__proto__:null,render:(e,t,o,n,s)=>{const r=xs(0),a=t.outerContainer;rM(e);const i=Ie(s.targetNode),l=ut(dt(i));((e,t)=>{kd(e,t,Ao)})(i,t.mothership),Cd(l,t.uiMothership),e.on("PostRender",(()=>{YE.setSidebar(a,o.sidebar,Af(e)),ZM(e,t,o,n),r.set(e.getWin().innerWidth),YE.setMenubar(a,QE(e,o)),((e,t)=>{const o=e.dom;let n=e.getWin();const s=e.getDoc().documentElement,r=xs(Lt(n.innerWidth,n.innerHeight)),a=xs(Lt(s.offsetWidth,s.offsetHeight)),i=()=>{const t=r.get();t.left===n.innerWidth&&t.top===n.innerHeight||(r.set(Lt(n.innerWidth,n.innerHeight)),qy(e))},l=()=>{const t=e.getDoc().documentElement,o=a.get();o.left===t.offsetWidth&&o.top===t.offsetHeight||(a.set(Lt(t.offsetWidth,t.offsetHeight)),qy(e))},c=t=>{((e,t)=>{e.dispatch("ScrollContent",t)})(e,t)};o.bind(n,"resize",i),o.bind(n,"scroll",c);const d=Gl(Ie(e.getBody()),"load",l),u=t.uiMothership.element;e.on("hide",(()=>{_t(u,"display","none")})),e.on("show",(()=>{It(u,"display")})),e.on("NodeChange",l),e.on("remove",(()=>{d.unbind(),o.unbind(n,"resize",i),o.unbind(n,"scroll",c),n=null}))})(e,t)}));const d=YE.getSocket(a).getOrDie("Could not find expected socket element");if(eB){Tt(d.element,{overflow:"scroll","-webkit-overflow-scrolling":"touch"});const t=((e,t)=>{let o=null;return{cancel:()=>{c(o)||(clearTimeout(o),o=null)},throttle:(...t)=>{c(o)&&(o=setTimeout((()=>{o=null,e.apply(null,t)}),20))}}})((()=>{e.dispatch("ScrollContent")})),o=jl(d.element,"scroll",t.throttle);e.on("remove",o.unbind)}ty(e,t),e.addCommand("ToggleSidebar",((t,o)=>{YE.toggleSidebar(a,o),e.dispatch("ToggleSidebar")})),e.addQueryValueHandler("ToggleSidebar",(()=>{var e;return null!==(e=YE.whichSidebar(a))&&void 0!==e?e:""}));const u=df(e);u!==Uh.sliding&&u!==Uh.floating||e.on("ResizeWindow ResizeEditor ResizeContent",(()=>{const o=e.getWin().innerWidth;o!==r.get()&&(YE.refreshToolbar(t.outerContainer),r.set(o))}));const m={setEnabled:e=>{ey(t,!e)},isEnabled:()=>!Cm.isDisabled(a)};return{iframeContainer:d.element.dom,editorContainer:a.element.dom,api:m}}});const oB=e=>/^[0-9\.]+(|px)$/i.test(""+e)?M.some(parseInt(""+e,10)):M.none(),nB=e=>h(e)?e+"px":e,sB=(e,t,o)=>{const n=t.filter((t=>e<t)),s=o.filter((t=>e>t));return n.or(s).getOr(e)},rB=e=>{const t=Qh(e),o=ef(e),n=of(e);return oB(t).map((e=>sB(e,o,n)))},{ToolbarLocation:aB,ToolbarMode:iB}=qf,lB=(e,t)=>{const o=$o(e);return{pos:t?o.y:o.bottom,bounds:o}};var cB=Object.freeze({__proto__:null,render:(e,t,o,n,s)=>{const{mothership:r,uiMothership:a,outerContainer:i}=t,l=Wl(),c=Ie(s.targetNode),d=((e,t,o,n,s)=>{const{uiMothership:r,outerContainer:a}=o,i=Gh.DOM,l=Wf(e),c=Gf(e),d=of(e).or(rB(e)),u=n.shared.header,m=u.isPositionedAtTop,g=df(e),p=g===iB.sliding||g===iB.floating,h=xs(!1),f=()=>h.get()&&!e.removed,b=e=>p?e.fold(y(0),(e=>e.components().length>1?Ht(e.components()[1].element):0)):0,v=()=>{r.broadcastOn([Hd()],{})},x=(e=!1)=>{if(f()){if(l||s.on((e=>{const o=d.getOrThunk((()=>{const e=oB(Mt(ht(),"margin-left")).getOr(0);return $t(ht())-Ut(t).left+e}));_t(e.element,"max-width",o+"px")})),p&&YE.refreshToolbar(a),l||s.on((e=>{const o=YE.getToolbar(a),n=b(o),s=$o(t),r=m()?Math.max(s.y-Ht(e.element)+n,0):s.bottom;Tt(a.element,{position:"absolute",top:Math.round(r)+"px",left:Math.round(s.x)+"px"})})),c){const t=e?uT.reset:uT.refresh;s.on(t)}v()}},w=(o=!0)=>{!l&&c&&f()&&s.on((n=>{const r=u.getDockingMode(),i=(o=>{switch(mf(e)){case aB.auto:const e=YE.getToolbar(a),n=b(e),s=Ht(o.element)-n,r=$o(t);if(r.y>s)return"top";{const e=Ze(t),o=Math.max(e.dom.scrollHeight,Ht(e));return r.bottom<o-s||Xo().bottom<r.bottom-s?"bottom":"top"}case aB.bottom:return"bottom";case aB.top:default:return"top"}})(n);var l;i!==r&&(l=i,s.on((e=>{uT.setModes(e,[l]),u.setDockingMode(l);const t=m()?ic.TopToBottom:ic.BottomToTop;vt(e.element,lc,t)})),o&&x(!0))}))};return{isVisible:f,isPositionedAtTop:m,show:()=>{h.set(!0),_t(a.element,"display","flex"),i.addClass(e.getBody(),"mce-edit-focus"),It(r.element,"display"),w(!1),x()},hide:()=>{h.set(!1),o.outerContainer&&(_t(a.element,"display","none"),i.removeClass(e.getBody(),"mce-edit-focus")),_t(r.element,"display","none")},update:x,updateMode:w,repositionPopups:v}})(e,c,t,n,l),u=hf(e);aM(e);const m=()=>{if(l.isSet())return void d.show();l.set(YE.getHeader(i).getOrDie());const s=Uf(e);Cd(s,r),Cd(s,a),ZM(e,t,o,n),YE.setMenubar(i,QE(e,o)),d.show(),((e,t,o,n)=>{const s=xs(lB(t,o.isPositionedAtTop())),r=n=>{const{pos:r,bounds:a}=lB(t,o.isPositionedAtTop()),{pos:i,bounds:l}=s.get(),c=a.height!==l.height||a.width!==l.width;s.set({pos:r,bounds:a}),c&&qy(e,n),o.isVisible()&&(i!==r?o.update(!0):c&&(o.updateMode(),o.repositionPopups()))};n||(e.on("activate",o.show),e.on("deactivate",o.hide)),e.on("SkinLoaded ResizeWindow",(()=>o.update(!0))),e.on("NodeChange keydown",(e=>{requestAnimationFrame((()=>r(e)))})),e.on("ScrollWindow",(()=>o.updateMode()));const a=Ll();a.set(Gl(Ie(e.getBody()),"load",(e=>r(e.raw)))),e.on("remove",(()=>{a.clear()}))})(e,c,d,u),e.nodeChanged()};e.on("show",m),e.on("hide",d.hide),u||(e.on("focus",m),e.on("blur",d.hide)),e.on("init",(()=>{(e.hasFocus()||u)&&m()})),ty(e,t);const g={show:m,hide:d.hide,setEnabled:e=>{ey(t,!e)},isEnabled:()=>!Cm.isDisabled(i)};return{editorContainer:i.element.dom,api:g}}});const dB="contexttoolbar-hide",uB=(e,t)=>Fr(dk,((o,n)=>{const s=(e=>({hide:()=>kr(e,ir()),getValue:()=>ou.getValue(e)}))(e.get(o));t.onAction(s,n.event.buttonApi)})),mB=(e,t)=>{const o=e.label.fold((()=>({})),(e=>({"aria-label":e}))),n=Bh(yb.sketch({inputClasses:["tox-toolbar-textfield","tox-toolbar-nav-js"],data:e.initValue(),inputAttributes:o,selectOnFocus:!0,inputBehaviours:gl([Tp.config({mode:"special",onEnter:e=>s.findPrimary(e).map((e=>(_r(e),!0))),onLeft:(e,t)=>(t.cut(),M.none()),onRight:(e,t)=>(t.cut(),M.none())})])})),s=((e,t,o)=>{const n=P(t,(t=>Bh(((e,t,o)=>(e=>"contextformtogglebutton"===e.type)(t)?((e,t,o)=>{const{primary:n,...s}=t.original,r=Ln(bv({...s,type:"togglebutton",onAction:b}));return GM(r,o,[uB(e,t)])})(e,t,o):((e,t,o)=>{const{primary:n,...s}=t.original,r=Ln(pv({...s,type:"button",onAction:b}));return jM(r,o,[uB(e,t)])})(e,t,o))(e,t,o))));return{asSpecs:()=>P(n,(e=>e.asSpec())),findPrimary:e=>re(t,((t,o)=>t.primary?M.from(n[o]).bind((t=>t.getOpt(e))).filter(C(Cm.isDisabled)):M.none()))}})(n,e.commands,t);return[{title:M.none(),items:[n.asSpec()]},{title:M.none(),items:s.asSpecs()}]},gB=(e,t,o=.01)=>t.bottom-e.y>=o&&e.bottom-t.y>=o,pB=e=>{const t=(e=>{const t=e.getBoundingClientRect();if(t.height<=0&&t.width<=0){const o=at(Ie(e.startContainer),e.startOffset).element;return(We(o)?et(o):M.some(o)).filter(Le).map((e=>e.dom.getBoundingClientRect())).getOr(t)}return t})(e.selection.getRng());if(e.inline){const e=zo();return Go(e.left+t.left,e.top+t.top,t.width,t.height)}{const o=qo(Ie(e.getBody()));return Go(o.x+t.left,o.y+t.top,t.width,t.height)}},hB=(e,t,o,n=0)=>{const s=No(window),r=$o(Ie(e.getContentAreaContainer())),a=If(e)||zf(e)||Pf(e),{x:i,width:l}=((e,t,o)=>{const n=Math.max(e.x+o,t.x);return{x:n,width:Math.min(e.right-o,t.right)-n}})(r,s,n);if(e.inline&&!a)return Go(i,s.y,l,s.height);{const a=t.header.isPositionedAtTop(),{y:c,bottom:d}=((e,t,o,n,s,r)=>{const a=Ie(e.getContainer()),i=si(a,".tox-editor-header").getOr(a),l=$o(i),c=l.y>=t.bottom,d=n&&!c;if(e.inline&&d)return{y:Math.max(l.bottom+r,o.y),bottom:o.bottom};if(e.inline&&!d)return{y:o.y,bottom:Math.min(l.y-r,o.bottom)};const u="line"===s?$o(a):t;return d?{y:Math.max(l.bottom+r,o.y),bottom:Math.min(u.bottom-r,o.bottom)}:{y:Math.max(u.y+r,o.y),bottom:Math.min(l.y-r,o.bottom)}})(e,r,s,a,o,n);return Go(i,c,l,d-c)}},fB={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"],inset:["tox-pop--inset"]},bB={maxHeightFunction:Zl(),maxWidthFunction:bE()},vB=e=>"node"===e,yB=(e,t,o,n,s)=>{const r=pB(e),a=n.lastElement().exists((e=>Xe(o,e)));return((e,t)=>{const o=e.selection.getRng(),n=at(Ie(o.startContainer),o.startOffset);return o.startContainer===o.endContainer&&o.startOffset===o.endOffset-1&&Xe(n.element,t)})(e,o)?a?PO:IO:a?((e,o,s)=>{const a=At(e,"position");_t(e,"position",o);const i=gB(r,$o(t))&&!n.isReposition()?LO:PO;return a.each((t=>_t(e,"position",t))),i})(t,n.getMode()):("fixed"===n.getMode()?s.y+zo().top:s.y)+(Ht(t)+12)<=r.y?IO:VO},xB=(e,t,o,n)=>{const s=t=>(n,s,r,a,i)=>({...yB(e,a,t,o,i)({...n,y:i.y,height:i.height},s,r,a,i),alwaysFit:!0}),r=e=>vB(n)?[s(e)]:[];return t?{onLtr:e=>[Qi,Xi,Ji,Yi,Ki,Zi].concat(r(e)),onRtl:e=>[Qi,Ji,Xi,Ki,Yi,Zi].concat(r(e))}:{onLtr:e=>[Zi,Qi,Yi,Xi,Ki,Ji].concat(r(e)),onRtl:e=>[Zi,Qi,Ki,Ji,Yi,Xi].concat(r(e))}},wB=(e,t)=>{const o=W(t,(t=>t.predicate(e.dom))),{pass:n,fail:s}=L(o,(e=>"contexttoolbar"===e.type));return{contextToolbars:n,contextForms:s}},SB=(e,t)=>{const o={},n=[],s=[],r={},a={},i=ae(e);return N(i,(i=>{const l=e[i];"contextform"===l.type?((e,i)=>{const l=Ln(Nn("ContextForm",kv,i));o[e]=l,l.launch.map((o=>{r["form:"+e]={...i.launch,type:"contextformtogglebutton"===o.type?"togglebutton":"button",onAction:()=>{t(l)}}})),"editor"===l.scope?s.push(l):n.push(l),a[e]=l})(i,l):"contexttoolbar"===l.type&&((e,t)=>{var o;(o=t,Nn("ContextToolbar",Ov,o)).each((o=>{"editor"===t.scope?s.push(o):n.push(o),a[e]=o}))})(i,l)})),{forms:o,inNodeScope:n,inEditorScope:s,lookupTable:a,formNavigators:r}},CB=Qr("forward-slide"),kB=Qr("backward-slide"),OB=Qr("change-slide-event"),_B="tox-pop--resizing",TB="tox-pop--transition",EB=(e,t,o,n)=>{const s=n.backstage,r=s.shared,a=_o().deviceType.isTouch,i=Wl(),l=Wl(),c=Wl(),d=Ja((e=>{const t=xs([]);return Th.sketch({dom:{tag:"div",classes:["tox-pop"]},fireDismissalEventInstead:{event:"doNotDismissYet"},onShow:e=>{t.set([]),Th.getContent(e).each((e=>{It(e.element,"visibility")})),Da(e.element,_B),It(e.element,"width")},inlineBehaviours:gl([zp("context-toolbar-events",[Nr(Xs(),((e,t)=>{"width"===t.event.raw.propertyName&&(Da(e.element,_B),It(e.element,"width"))})),Fr(OB,((e,t)=>{const o=e.element;It(o,"width");const n=$t(o);Th.setContent(e,t.event.contents),Aa(o,_B);const s=$t(o);_t(o,"width",n+"px"),Th.getContent(e).each((e=>{t.event.focus.bind((e=>(wl(e),kl(o)))).orThunk((()=>(Tp.focusIn(e),Cl(dt(o)))))})),setTimeout((()=>{_t(e.element,"width",s+"px")}),0)})),Fr(CB,((e,o)=>{Th.getContent(e).each((o=>{t.set(t.get().concat([{bar:o,focus:Cl(dt(e.element))}]))})),Or(e,OB,{contents:o.event.forwardContents,focus:M.none()})})),Fr(kB,((e,o)=>{ne(t.get()).each((o=>{t.set(t.get().slice(0,t.get().length-1)),Or(e,OB,{contents:Ya(o.bar),focus:o.focus})}))}))]),Tp.config({mode:"special",onEscape:o=>ne(t.get()).fold((()=>e.onEscape()),(e=>(kr(o,kB),M.some(!0))))})]),lazySink:()=>Ko.value(e.sink)})})({sink:o,onEscape:()=>(e.focus(),M.some(!0))})),u=()=>{const t=c.get().getOr("node"),o=vB(t)?1:0;return hB(e,r,t,o)},m=()=>!(e.removed||a()&&s.isContextMenuOpen()),g=()=>{if(m()){const t=u(),o=xe(c.get(),"node")?((e,t)=>t.filter((e=>pt(e)&&(e=>Le(e)&&He(e.dom))(e))).map(qo).getOrThunk((()=>pB(e))))(e,i.get()):pB(e);return t.height<=0||!gB(o,t)}return!0},p=()=>{i.clear(),l.clear(),c.clear(),Th.hide(d)},h=()=>{if(Th.isOpen(d)){const e=d.element;It(e,"display"),g()?_t(e,"display","none"):(l.set(0),Th.reposition(d))}},f=t=>({dom:{tag:"div",classes:["tox-pop__dialog"]},components:[t],behaviours:gl([Tp.config({mode:"acyclic"}),zp("pop-dialog-wrap-events",[Lr((t=>{e.shortcuts.add("ctrl+F9","focus statusbar",(()=>Tp.focusIn(t)))})),Wr((t=>{e.shortcuts.remove("ctrl+F9")}))])])}),v=Xt((()=>SB(t,(e=>{const t=y([e]);Or(d,CB,{forwardContents:f(t)})})))),y=t=>{const{buttons:o}=e.ui.registry.getAll(),s={...o,...v().formNavigators},a=df(e)===Uh.scrolling?Uh.scrolling:Uh.default,i=q(P(t,(t=>"contexttoolbar"===t.type?((t,o)=>KM(e,{buttons:t,toolbar:o.items,allowToolbarGroups:!1},n.backstage,M.some(["form:"])))(s,t):((e,t)=>mB(e,t))(t,r.providers))));return LE({type:a,uid:Qr("context-toolbar"),initGroups:i,onEscape:M.none,cyclicKeying:!0,providers:r.providers})},x=(t,n)=>{if(w.cancel(),!m())return;const s=y(t),p=t[0].position,h=((t,n)=>{const s="node"===t?r.anchors.node(n):r.anchors.cursor(),c=((e,t,o,n)=>"line"===t?{bubble:oc(12,0,fB),layouts:{onLtr:()=>[el],onRtl:()=>[tl]},overrides:bB}:{bubble:oc(0,12,fB,1/12),layouts:xB(e,o,n,t),overrides:bB})(e,t,a(),{lastElement:i.get,isReposition:()=>xe(l.get(),0),getMode:()=>ud.getMode(o)});return cn(s,c)})(p,n);c.set(p),l.set(1);const b=d.element;It(b,"display"),(e=>xe(Se(e,i.get(),Xe),!0))(n)||(Da(b,TB),ud.reset(o,d)),Th.showWithinBounds(d,f(s),{anchor:h,transition:{classes:[TB],mode:"placement"}},(()=>M.some(u()))),n.fold(i.clear,i.set),g()&&_t(b,"display","none")},w=zk((()=>{e.hasFocus()&&!e.removed&&(Fa(d.element,TB)?w.throttle():((e,t)=>{const o=Ie(t.getBody()),n=e=>Xe(e,o),s=Ie(t.selection.getNode());return(e=>!n(e)&&!Je(o,e))(s)?M.none():((e,t,o)=>{const n=wB(e,t);if(n.contextForms.length>0)return M.some({elem:e,toolbars:[n.contextForms[0]]});{const t=wB(e,o);if(t.contextForms.length>0)return M.some({elem:e,toolbars:[t.contextForms[0]]});if(n.contextToolbars.length>0||t.contextToolbars.length>0){const o=(e=>{if(e.length<=1)return e;{const t=t=>R(e,(e=>e.position===t)),o=t=>W(e,(e=>e.position===t)),n=t("selection"),s=t("node");if(n||s){if(s&&n){const e=o("node"),t=P(o("selection"),(e=>({...e,position:"node"})));return e.concat(t)}return o(n?"selection":"node")}return o("line")}})(n.contextToolbars.concat(t.contextToolbars));return M.some({elem:e,toolbars:o})}return M.none()}})(s,e.inNodeScope,e.inEditorScope).orThunk((()=>((e,t,o)=>e(t)?M.none():Os(t,(e=>{if(Le(e)){const{contextToolbars:t,contextForms:n}=wB(e,o.inNodeScope),s=n.length>0?n:(e=>{if(e.length<=1)return e;{const t=t=>G(e,(e=>e.position===t));return t("selection").orThunk((()=>t("node"))).orThunk((()=>t("line"))).map((e=>e.position)).fold((()=>[]),(t=>W(e,(e=>e.position===t))))}})(t);return s.length>0?M.some({elem:e,toolbars:s}):M.none()}return M.none()}),e))(n,s,e)))})(v(),e).fold(p,(e=>{x(e.toolbars,M.some(e.elem))})))}),17);e.on("init",(()=>{e.on("remove",p),e.on("ScrollContent ScrollWindow ObjectResized ResizeEditor longpress",h),e.on("click keyup focus SetContent",w.throttle),e.on(dB,p),e.on("contexttoolbar-show",(t=>{const o=v();be(o.lookupTable,t.toolbarKey).each((o=>{x([o],Ce(t.target!==e,t.target)),Th.getContent(d).each(Tp.focusIn)}))})),e.on("focusout",(t=>{Eh.setEditorTimeout(e,(()=>{kl(o.element).isNone()&&kl(d.element).isNone()&&p()}),0)})),e.on("SwitchMode",(()=>{e.mode.isReadOnly()&&p()})),e.on("AfterProgressState",(t=>{t.state?p():e.hasFocus()&&w.throttle()})),e.on("NodeChange",(e=>{kl(d.element).fold(w.throttle,b)}))}))},MB={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},BB=(()=>{const e="[0-9]+",t="[eE][+-]?[0-9]+",o=e=>`(?:${e})?`,n=["Infinity","[0-9]+\\."+o(e)+o(t),"\\.[0-9]+"+o(t),e+o(t)].join("|");return new RegExp(`^([+-]?(?:${n}))(.*)$`)})(),AB=(e,t)=>{const o=()=>{const o=t.getOptions(e),n=t.getCurrent(e).map(t.hash),s=Wl();return P(o,(o=>({type:"togglemenuitem",text:t.display(o),onSetup:r=>{const a=e=>{e&&(s.on((e=>e.setActive(!1))),s.set(r)),r.setActive(e)};a(xe(n,t.hash(o)));const i=t.watcher(e,o,a);return()=>{s.clear(),i()}},onAction:()=>t.setCurrent(e,o)})))};e.ui.registry.addMenuButton(t.name,{tooltip:t.text,icon:t.icon,fetch:e=>e(o()),onSetup:t.onToolbarSetup}),e.ui.registry.addNestedMenuItem(t.name,{type:"nestedmenuitem",text:t.text,getSubmenuItems:o,onSetup:t.onMenuSetup})},DB={name:"lineheight",text:"Line height",icon:"line-height",getOptions:Rf,hash:e=>((e,t)=>((e,t)=>M.from(BB.exec(e)).bind((e=>{const o=Number(e[1]),n=e[2];return((e,t)=>R(t,(t=>R(MB[t],(t=>e===t)))))(n,t)?M.some({value:o,unit:n}):M.none()})))(e,["fixed","relative","empty"]).map((({value:e,unit:t})=>e+t)))(e).getOr(e),display:x,watcher:(e,t,o)=>e.formatter.formatChanged("lineheight",o,!1,{value:t}).unbind,getCurrent:e=>M.from(e.queryCommandValue("LineHeight")),setCurrent:(e,t)=>e.execCommand("LineHeight",!1,t)},FB=e=>lM(e,"NodeChange",(t=>{t.setEnabled(e.queryCommandState("outdent"))})),IB=(e,t)=>o=>{o.setActive(t.get());const n=e=>{t.set(e.state),o.setActive(e.state)};return e.on("PastePlainTextToggle",n),()=>e.off("PastePlainTextToggle",n)},VB=(e,t)=>()=>{e.execCommand("mceToggleFormat",!1,t)},RB=e=>{(e=>{(e=>{ok.each([{name:"bold",text:"Bold",icon:"bold"},{name:"italic",text:"Italic",icon:"italic"},{name:"underline",text:"Underline",icon:"underline"},{name:"strikethrough",text:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",icon:"superscript"}],((t,o)=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:iM(e,t.name),onAction:VB(e,t.name)})}));for(let t=1;t<=6;t++){const o="h"+t;e.ui.registry.addToggleButton(o,{text:o.toUpperCase(),tooltip:"Heading "+t,onSetup:iM(e,o),onAction:VB(e,o)})}})(e),(e=>{ok.each([{name:"cut",text:"Cut",action:"Cut",icon:"cut"},{name:"copy",text:"Copy",action:"Copy",icon:"copy"},{name:"paste",text:"Paste",action:"Paste",icon:"paste"},{name:"help",text:"Help",action:"mceHelp",icon:"help"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"remove",text:"Remove",action:"Delete",icon:"remove"},{name:"print",text:"Print",action:"mcePrint",icon:"print"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onAction:dM(e,t.action)})}))})(e),(e=>{ok.each([{name:"blockquote",text:"Blockquote",action:"mceBlockQuote",icon:"quote"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:dM(e,t.action),onSetup:iM(e,t.name)})}))})(e)})(e),(e=>{ok.each([{name:"bold",text:"Bold",action:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",action:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",action:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",action:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",action:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",action:"Superscript",icon:"superscript"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"cut",text:"Cut",action:"Cut",icon:"cut",shortcut:"Meta+X"},{name:"copy",text:"Copy",action:"Copy",icon:"copy",shortcut:"Meta+C"},{name:"paste",text:"Paste",action:"Paste",icon:"paste",shortcut:"Meta+V"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A"},{name:"print",text:"Print...",action:"mcePrint",icon:"print",shortcut:"Meta+P"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onAction:dM(e,t.action)})})),e.ui.registry.addMenuItem("codeformat",{text:"Code",icon:"sourcecode",onAction:VB(e,"code")})})(e)},zB=(e,t)=>lM(e,"Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",(o=>{o.setEnabled(!e.mode.isReadOnly()&&e.undoManager[t]())})),HB=e=>lM(e,"VisualAid",(t=>{t.setActive(e.hasVisual)})),PB=(e,t)=>{(e=>{N([{name:"alignleft",text:"Align left",cmd:"JustifyLeft",icon:"align-left"},{name:"aligncenter",text:"Align center",cmd:"JustifyCenter",icon:"align-center"},{name:"alignright",text:"Align right",cmd:"JustifyRight",icon:"align-right"},{name:"alignjustify",text:"Justify",cmd:"JustifyFull",icon:"align-justify"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:dM(e,t.cmd),onSetup:iM(e,t.name)})})),e.ui.registry.addButton("alignnone",{tooltip:"No alignment",icon:"align-none",onAction:dM(e,"JustifyNone")})})(e),RB(e),((e,t)=>{((e,t)=>{const o=mM(0,t,bM(e));e.ui.registry.addNestedMenuItem("align",{text:t.shared.providers.translate("Align"),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=mM(0,t,SM(e));e.ui.registry.addNestedMenuItem("fontfamily",{text:t.shared.providers.translate("Fonts"),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o={type:"advanced",...t.styles},n=mM(0,t,EM(e,o));e.ui.registry.addNestedMenuItem("styles",{text:"Formats",getSubmenuItems:()=>n.items.validateItems(n.getStyleItems())})})(e,t),((e,t)=>{const o=mM(0,t,yM(e));e.ui.registry.addNestedMenuItem("blocks",{text:"Blocks",getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=mM(0,t,TM(e));e.ui.registry.addNestedMenuItem("fontsize",{text:"Font sizes",getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t)})(e,t),(e=>{(e=>{e.ui.registry.addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onSetup:zB(e,"hasUndo"),onAction:dM(e,"undo")}),e.ui.registry.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onSetup:zB(e,"hasRedo"),onAction:dM(e,"redo")})})(e),(e=>{e.ui.registry.addButton("undo",{tooltip:"Undo",icon:"undo",enabled:!1,onSetup:zB(e,"hasUndo"),onAction:dM(e,"undo")}),e.ui.registry.addButton("redo",{tooltip:"Redo",icon:"redo",enabled:!1,onSetup:zB(e,"hasRedo"),onAction:dM(e,"redo")})})(e)})(e),(e=>{(e=>{e.addCommand("mceApplyTextcolor",((t,o)=>{((e,t,o)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.apply(t,{value:o}),e.nodeChanged()}))})(e,t,o)})),e.addCommand("mceRemoveTextcolor",(t=>{((e,t)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.remove(t,{value:null},void 0,!0),e.nodeChanged()}))})(e,t)}))})(e);const t=xs(lx),o=xs(lx);px(e,"forecolor","forecolor","Text color",t),px(e,"backcolor","hilitecolor","Background color",o),hx(e,"forecolor","forecolor","Text color"),hx(e,"backcolor","hilitecolor","Background color")})(e),(e=>{(e=>{e.ui.registry.addButton("visualaid",{tooltip:"Visual aids",text:"Visual aids",onAction:dM(e,"mceToggleVisualAid")})})(e),(e=>{e.ui.registry.addToggleMenuItem("visualaid",{text:"Visual aids",onSetup:HB(e),onAction:dM(e,"mceToggleVisualAid")})})(e)})(e),(e=>{(e=>{e.ui.registry.addButton("outdent",{tooltip:"Decrease indent",icon:"outdent",onSetup:FB(e),onAction:dM(e,"outdent")}),e.ui.registry.addButton("indent",{tooltip:"Increase indent",icon:"indent",onAction:dM(e,"indent")})})(e)})(e),(e=>{AB(e,DB),(e=>M.from(lf(e)).map((t=>({name:"language",text:"Language",icon:"language",getOptions:y(t),hash:e=>u(e.customCode)?e.code:`${e.code}/${e.customCode}`,display:e=>e.title,watcher:(e,t,o)=>{var n;return e.formatter.formatChanged("lang",o,!1,{value:t.code,customValue:null!==(n=t.customCode)&&void 0!==n?n:null}).unbind},getCurrent:e=>{const t=Ie(e.selection.getNode());return _s(t,(e=>M.some(e).filter(Le).bind((e=>wt(e,"lang").map((t=>({code:t,customCode:wt(e,"data-mce-lang").getOrUndefined(),title:""})))))))},setCurrent:(e,t)=>e.execCommand("Lang",!1,t),onToolbarSetup:t=>{const o=Ll();return t.setActive(e.formatter.match("lang",{},void 0,!0)),o.set(e.formatter.formatChanged("lang",t.setActive,!0)),o.clear}}))))(e).each((t=>AB(e,t)))})(e),(e=>{const t=xs(Bf(e)),o=()=>e.execCommand("mceTogglePlainTextPaste");e.ui.registry.addToggleButton("pastetext",{active:!1,icon:"paste-text",tooltip:"Paste as text",onAction:o,onSetup:IB(e,t)}),e.ui.registry.addToggleMenuItem("pastetext",{text:"Paste as text",icon:"paste-text",onAction:o,onSetup:IB(e,t)})})(e)},NB=e=>r(e)?e.split(/[ ,]/):e,LB=e=>t=>t.options.get(e),WB=LB("contextmenu_never_use_native"),UB=LB("contextmenu_avoid_overlap"),jB=e=>{const t=e.ui.registry.getAll().contextMenus,o=e.options.get("contextmenu");return e.options.isSet("contextmenu")?o:W(o,(e=>ve(t,e)))},GB=(e,t)=>({type:"makeshift",x:e,y:t}),$B=e=>"longpress"===e.type||0===e.type.indexOf("touch"),qB=(e,t)=>"contextmenu"===t.type||"longpress"===t.type?e.inline?(e=>{if($B(e)){const t=e.touches[0];return GB(t.pageX,t.pageY)}return GB(e.pageX,e.pageY)})(t):((e,t)=>{const o=Gh.DOM.getPos(e);return((e,t,o)=>GB(e.x+t,e.y+o))(t,o.x,o.y)})(e.getContentAreaContainer(),(e=>{if($B(e)){const t=e.touches[0];return GB(t.clientX,t.clientY)}return GB(e.clientX,e.clientY)})(t)):XB(e),XB=e=>({type:"selection",root:Ie(e.selection.getNode())}),JB=(e,t,o)=>{switch(o){case"node":return(e=>({type:"node",node:M.some(Ie(e.selection.getNode())),root:Ie(e.getBody())}))(e);case"point":return qB(e,t);case"selection":return XB(e)}},YB=(e,t,o,n,s,r)=>{const a=o(),i=JB(e,t,r);Sk(a,Kf.CLOSE_ON_EXECUTE,n,{isHorizontalMenu:!1,search:M.none()}).map((e=>{t.preventDefault(),Th.showMenuAt(s,{anchor:i},{menu:{markers:ub("normal")},data:e})}))},KB={onLtr:()=>[Qi,Xi,Ji,Yi,Ki,Zi,IO,VO,FO,AO,DO,BO],onRtl:()=>[Qi,Ji,Xi,Ki,Yi,Zi,IO,VO,DO,BO,FO,AO]},ZB={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},QB=(e,t,o,n,s,r)=>{const a=_o(),i=a.os.isiOS(),l=a.os.isMacOS(),c=a.os.isAndroid(),d=a.deviceType.isTouch(),u=()=>{const a=o();((e,t,o,n,s,r,a)=>{const i=((e,t,o)=>{const n=JB(e,t,o);return{bubble:oc(0,"point"===o?12:0,ZB),layouts:KB,overrides:{maxWidthFunction:bE(),maxHeightFunction:Zl()},...n}})(e,t,r);Sk(o,Kf.CLOSE_ON_EXECUTE,n,{isHorizontalMenu:!0,search:M.none()}).map((o=>{t.preventDefault();const l=a?kh.HighlightMenuAndItem:kh.HighlightNone;Th.showMenuWithinBounds(s,{anchor:i},{menu:{markers:ub("normal"),highlightOnOpen:l},data:o,type:"horizontal"},(()=>M.some(hB(e,n.shared,"node"===r?"node":"selection")))),e.dispatch(dB)}))})(e,t,a,n,s,r,!(c||i||l&&d))};if((l||i)&&"node"!==r){const o=()=>{(e=>{const t=e.selection.getRng(),o=()=>{Eh.setEditorTimeout(e,(()=>{e.selection.setRng(t)}),10),r()};e.once("touchend",o);const n=e=>{e.preventDefault(),e.stopImmediatePropagation()};e.on("mousedown",n,!0);const s=()=>r();e.once("longpresscancel",s);const r=()=>{e.off("touchend",o),e.off("longpresscancel",s),e.off("mousedown",n)}})(e),u()};((e,t)=>{const o=e.selection;if(o.isCollapsed()||t.touches.length<1)return!1;{const n=t.touches[0],s=o.getRng();return Pc(e.getWin(),Mc.domRange(s)).exists((e=>e.left<=n.clientX&&e.right>=n.clientX&&e.top<=n.clientY&&e.bottom>=n.clientY))}})(e,t)?o():(e.once("selectionchange",o),e.once("touchend",(()=>e.off("selectionchange",o))))}else u()},eA=e=>r(e)?"|"===e:"separator"===e.type,tA={type:"separator"},oA=e=>{const t=e=>({text:e.text,icon:e.icon,enabled:e.enabled,shortcut:e.shortcut});if(r(e))return e;switch(e.type){case"separator":return tA;case"submenu":return{type:"nestedmenuitem",...t(e),getSubmenuItems:()=>{const t=e.getSubmenuItems();return r(t)?t:P(t,oA)}};default:const n=e;return{type:"menuitem",...t(n),onAction:(o=n.onAction,()=>o())}}var o},nA=(e,t)=>{if(0===t.length)return e;const o=ne(e).filter((e=>!eA(e))).fold((()=>[]),(e=>[tA]));return e.concat(o).concat(t).concat([tA])},sA=(e,t)=>!(e=>"longpress"===e.type||ve(e,"touches"))(t)&&(2!==t.button||t.target===e.getBody()&&""===t.pointerType),rA=(e,t)=>sA(e,t)?e.selection.getStart(!0):t.target,aA=(e,t,o)=>{const n=_o().deviceType.isTouch,s=Ja(Th.sketch({dom:{tag:"div"},lazySink:t,onEscape:()=>e.focus(),onShow:()=>o.setContextMenuState(!0),onHide:()=>o.setContextMenuState(!1),fireDismissalEventInstead:{},inlineBehaviours:gl([zp("dismissContextMenu",[Fr(fr(),((t,o)=>{Rd.close(t),e.focus()}))])])})),a=()=>Th.hide(s),i=t=>{if(WB(e)&&t.preventDefault(),((e,t)=>t.ctrlKey&&!WB(e))(e,t)||(e=>0===jB(e).length)(e))return;const a=((e,t)=>{const o=UB(e),n=sA(e,t)?"selection":"point";if(Ee(o)){const s=rA(e,t);return aw(Ie(s),o)?"node":n}return n})(e,t);(n()?QB:YB)(e,t,(()=>{const o=rA(e,t),n=e.ui.registry.getAll(),s=jB(e);return((e,t,o)=>{const n=j(t,((t,n)=>be(e,n.toLowerCase()).map((e=>{const n=e.update(o);if(r(n))return nA(t,n.split(" "));if(n.length>0){const e=P(n,oA);return nA(t,e)}return t})).getOrThunk((()=>t.concat([n])))),[]);return n.length>0&&eA(n[n.length-1])&&n.pop(),n})(n.contextMenus,s,o)}),o,s,a)};e.on("init",(()=>{const t="ResizeEditor ScrollContent ScrollWindow longpresscancel"+(n()?"":" ResizeWindow");e.on(t,a),e.on("longpress contextmenu",i)}))},iA=ws([{offset:["x","y"]},{absolute:["x","y"]},{fixed:["x","y"]}]),lA=e=>t=>t.translate(-e.left,-e.top),cA=e=>t=>t.translate(e.left,e.top),dA=e=>(t,o)=>j(e,((e,t)=>t(e)),Lt(t,o)),uA=(e,t,o)=>e.fold(dA([cA(o),lA(t)]),dA([lA(t)]),dA([])),mA=(e,t,o)=>e.fold(dA([cA(o)]),dA([]),dA([cA(t)])),gA=(e,t,o)=>e.fold(dA([]),dA([lA(o)]),dA([cA(t),lA(o)])),pA=(e,t,o)=>{const n=e.fold(((e,t)=>({position:M.some("absolute"),left:M.some(e+"px"),top:M.some(t+"px")})),((e,t)=>({position:M.some("absolute"),left:M.some(e-o.left+"px"),top:M.some(t-o.top+"px")})),((e,t)=>({position:M.some("fixed"),left:M.some(e+"px"),top:M.some(t+"px")})));return{right:M.none(),bottom:M.none(),...n}},hA=(e,t,o,n)=>{const s=(e,s)=>(r,a)=>{const i=e(t,o,n);return s(r.getOr(i.left),a.getOr(i.top))};return e.fold(s(gA,fA),s(mA,bA),s(uA,vA))},fA=iA.offset,bA=iA.absolute,vA=iA.fixed,yA=(e,t)=>{const o=xt(e,t);return u(o)?NaN:parseInt(o,10)},xA=(e,t,o,n,s,r)=>{const a=((e,t,o,n)=>((e,t)=>{const o=e.element,n=yA(o,t.leftAttr),s=yA(o,t.topAttr);return isNaN(n)||isNaN(s)?M.none():M.some(Lt(n,s))})(e,t).fold((()=>o),(e=>vA(e.left+n.left,e.top+n.top))))(e,t,o,n),i=t.mustSnap?SA(e,t,a,s,r):CA(e,t,a,s,r),l=uA(a,s,r);return((e,t,o)=>{const n=e.element;vt(n,t.leftAttr,o.left+"px"),vt(n,t.topAttr,o.top+"px")})(e,t,l),i.fold((()=>({coord:vA(l.left,l.top),extra:M.none()})),(e=>({coord:e.output,extra:e.extra})))},wA=(e,t,o,n)=>re(e,(e=>{const s=e.sensor,r=((e,t,o,n,s,r)=>{const a=mA(e,s,r),i=mA(t,s,r);return Math.abs(a.left-i.left)<=o&&Math.abs(a.top-i.top)<=n})(t,s,e.range.left,e.range.top,o,n);return r?M.some({output:hA(e.output,t,o,n),extra:e.extra}):M.none()})),SA=(e,t,o,n,s)=>{const r=t.getSnapPoints(e);return wA(r,o,n,s).orThunk((()=>{const e=j(r,((e,t)=>{const r=t.sensor,a=((e,t,o,n,s,r)=>{const a=mA(e,s,r),i=mA(t,s,r),l=Math.abs(a.left-i.left),c=Math.abs(a.top-i.top);return Lt(l,c)})(o,r,t.range.left,t.range.top,n,s);return e.deltas.fold((()=>({deltas:M.some(a),snap:M.some(t)})),(o=>(a.left+a.top)/2<=(o.left+o.top)/2?{deltas:M.some(a),snap:M.some(t)}:e))}),{deltas:M.none(),snap:M.none()});return e.snap.map((e=>({output:hA(e.output,o,n,s),extra:e.extra})))}))},CA=(e,t,o,n,s)=>{const r=t.getSnapPoints(e);return wA(r,o,n,s)};var kA=Object.freeze({__proto__:null,snapTo:(e,t,o,n)=>{const s=t.getTarget(e.element);if(t.repositionTarget){const t=Ye(e.element),o=zo(t),r=J_(s),a=((e,t,o)=>({coord:hA(e.output,e.output,t,o),extra:e.extra}))(n,o,r),i=pA(a.coord,0,r);Et(s,i)}}});const OA="data-initial-z-index",_A=(e,t)=>{e.getSystem().addToGui(t),(e=>{et(e.element).filter(Le).each((t=>{At(t,"z-index").each((e=>{vt(t,OA,e)})),_t(t,"z-index",Mt(e.element,"z-index"))}))})(t)},TA=e=>{(e=>{et(e.element).filter(Le).each((e=>{wt(e,OA).fold((()=>It(e,"z-index")),(t=>_t(e,"z-index",t))),Ct(e,OA)}))})(e),e.getSystem().removeFromGui(e)},EA=(e,t,o)=>e.getSystem().build(fw.sketch({dom:{styles:{left:"0px",top:"0px",width:"100%",height:"100%",position:"fixed","z-index":"1000000000000000"},classes:[t]},events:o}));var MA=ds("snaps",[Xn("getSnapPoints"),wi("onSensor"),Xn("leftAttr"),Xn("topAttr"),us("lazyViewport",Xo),us("mustSnap",!1)]);const BA=[us("useFixed",_),Xn("blockerClass"),us("getTarget",x),us("onDrag",b),us("repositionTarget",!0),us("onDrop",b),bs("getBounds",Xo),MA],AA=(e,t)=>({bounds:e.getBounds(),height:Pt(t.element),width:qt(t.element)}),DA=(e,t,o,n,s)=>{const r=o.update(n,s),a=o.getStartData().getOrThunk((()=>AA(t,e)));r.each((o=>{((e,t,o,n)=>{const s=t.getTarget(e.element);if(t.repositionTarget){const r=Ye(e.element),a=zo(r),i=J_(s),l=(e=>{return(t=At(e,"left"),o=At(e,"top"),n=At(e,"position"),s=(e,t,o)=>("fixed"===o?vA:fA)(parseInt(e,10),parseInt(t,10)),t.isSome()&&o.isSome()&&n.isSome()?M.some(s(t.getOrDie(),o.getOrDie(),n.getOrDie())):M.none()).getOrThunk((()=>{const t=Ut(e);return bA(t.left,t.top)}));var t,o,n,s})(s),c=((e,t,o,n,s,r,a)=>((e,t,o,n,s)=>{const r=s.bounds,a=mA(t,o,n),i=Pi(a.left,r.x,r.x+r.width-s.width),l=Pi(a.top,r.y,r.y+r.height-s.height),c=bA(i,l);return t.fold((()=>{const e=gA(c,o,n);return fA(e.left,e.top)}),y(c),(()=>{const e=uA(c,o,n);return vA(e.left,e.top)}))})(0,t.fold((()=>{const e=(t=o,a=r.left,i=r.top,t.fold(((e,t)=>fA(e+a,t+i)),((e,t)=>bA(e+a,t+i)),((e,t)=>vA(e+a,t+i))));var t,a,i;const l=uA(e,n,s);return vA(l.left,l.top)}),(t=>{const a=xA(e,t,o,r,n,s);return a.extra.each((o=>{t.onSensor(e,o)})),a.coord})),n,s,a))(e,t.snaps,l,a,i,n,o),d=pA(c,0,i);Et(s,d)}t.onDrag(e,s,n)})(e,t,a,o)}))},FA=(e,t,o,n)=>{t.each(TA),o.snaps.each((t=>{((e,t)=>{((e,t)=>{const o=e.element;Ct(o,t.leftAttr),Ct(o,t.topAttr)})(e,t)})(e,t)}));const s=o.getTarget(e.element);n.reset(),o.onDrop(e,s)},IA=e=>(t,o)=>{const n=e=>{o.setStartData(AA(t,e))};return Br([Fr(mr(),(e=>{o.getStartData().each((()=>n(e)))})),...e(t,o,n)])};var VA=Object.freeze({__proto__:null,getData:e=>M.from(Lt(e.x,e.y)),getDelta:(e,t)=>Lt(t.left-e.left,t.top-e.top)});const RA=(e,t,o)=>[Fr(Vs(),((n,s)=>{if(0!==s.event.raw.button)return;s.stop();const r=()=>FA(n,M.some(l),e,t),a=iw(r,200),i={drop:r,delayDrop:a.schedule,forceDrop:r,move:o=>{a.cancel(),DA(n,e,t,VA,o)}},l=EA(n,e.blockerClass,(e=>Br([Fr(Vs(),e.forceDrop),Fr(Hs(),e.drop),Fr(Rs(),((t,o)=>{e.move(o.event)})),Fr(zs(),e.delayDrop)]))(i));o(n),_A(n,l)}))],zA=[...BA,Oi("dragger",{handlers:IA(RA)})];var HA=Object.freeze({__proto__:null,getData:e=>{const t=e.raw.touches;return 1===t.length?(e=>{const t=e[0];return M.some(Lt(t.clientX,t.clientY))})(t):M.none()},getDelta:(e,t)=>Lt(t.left-e.left,t.top-e.top)});const PA=(e,t,o)=>{const n=Wl(),s=o=>{FA(o,n.get(),e,t),n.clear()};return[Fr(As(),((r,a)=>{a.stop();const i=()=>s(r),l={drop:i,delayDrop:b,forceDrop:i,move:o=>{DA(r,e,t,HA,o)}},c=EA(r,e.blockerClass,(e=>Br([Fr(As(),e.forceDrop),Fr(Fs(),e.drop),Fr(Is(),e.drop),Fr(Ds(),((t,o)=>{e.move(o.event)}))]))(l));n.set(c),o(r),_A(r,c)})),Fr(Ds(),((o,n)=>{n.stop(),DA(o,e,t,HA,n.event)})),Fr(Fs(),((e,t)=>{t.stop(),s(e)})),Fr(Is(),s)]},NA=zA,LA=[...BA,Oi("dragger",{handlers:IA(PA)})],WA=[...BA,Oi("dragger",{handlers:IA(((e,t,o)=>[...RA(e,t,o),...PA(e,t,o)]))})];var UA=Object.freeze({__proto__:null,mouse:NA,touch:LA,mouseOrTouch:WA}),jA=Object.freeze({__proto__:null,init:()=>{let e=M.none(),t=M.none();const o=y({});return ba({readState:o,reset:()=>{e=M.none(),t=M.none()},update:(t,o)=>t.getData(o).bind((o=>((t,o)=>{const n=e.map((e=>t.getDelta(e,o)));return e=M.some(o),n})(t,o))),getStartData:()=>t,setStartData:e=>{t=M.some(e)}})}});const GA=bl({branchKey:"mode",branches:UA,name:"dragging",active:{events:(e,t)=>e.dragger.handlers(e,t)},extra:{snap:e=>({sensor:e.sensor,range:e.range,output:e.output,extra:M.from(e.extra)})},state:jA,apis:kA}),$A=(e,t,o,n,s,r)=>e.fold((()=>GA.snap({sensor:bA(o-20,n-20),range:Lt(s,r),output:bA(M.some(o),M.some(n)),extra:{td:t}})),(e=>{const s=o-20,r=n-20,a=e.element.dom.getBoundingClientRect();return GA.snap({sensor:bA(s,r),range:Lt(40,40),output:bA(M.some(o-a.width/2),M.some(n-a.height/2)),extra:{td:t}})})),qA=(e,t,o)=>({getSnapPoints:e,leftAttr:"data-drag-left",topAttr:"data-drag-top",onSensor:(e,n)=>{const s=n.td;((e,t)=>e.exists((e=>Xe(e,t))))(t.get(),s)||(t.set(s),o(s))},mustSnap:!0}),XA=e=>Bh(Mh.sketch({dom:{tag:"div",classes:["tox-selector"]},buttonBehaviours:gl([GA.config({mode:"mouseOrTouch",blockerClass:"blocker",snaps:e}),Xw.config({})]),eventOrder:{mousedown:["dragging","alloy.base.behaviour"],touchstart:["dragging","alloy.base.behaviour"]}})),JA=(e,t)=>{const o=xs([]),n=xs([]),s=xs(!1),r=Wl(),a=Wl(),i=e=>{const o=qo(e);return $A(u.getOpt(t),e,o.x,o.y,o.width,o.height)},l=e=>{const o=qo(e);return $A(m.getOpt(t),e,o.right,o.bottom,o.width,o.height)},c=qA((()=>P(o.get(),(e=>i(e)))),r,(t=>{a.get().each((o=>{e.dispatch("TableSelectorChange",{start:t,finish:o})}))})),d=qA((()=>P(n.get(),(e=>l(e)))),a,(t=>{r.get().each((o=>{e.dispatch("TableSelectorChange",{start:o,finish:t})}))})),u=XA(c),m=XA(d),g=Ja(u.asSpec()),p=Ja(m.asSpec()),h=(t,o,n,s)=>{const r=n(o);GA.snapTo(t,r),((t,o,n,r)=>{const a=o.dom.getBoundingClientRect();It(t.element,"display");const i=Qe(Ie(e.getBody())).dom.innerHeight,l=a[s]<0,c=((e,t)=>e[s]>t)(a,i);(l||c)&&_t(t.element,"display","none")})(t,o)},f=e=>h(g,e,i,"top"),b=e=>h(p,e,l,"bottom");_o().deviceType.isTouch()&&(e.on("TableSelectionChange",(e=>{s.get()||(vd(t,g),vd(t,p),s.set(!0)),r.set(e.start),a.set(e.finish),e.otherCells.each((t=>{o.set(t.upOrLeftCells),n.set(t.downOrRightCells),f(e.start),b(e.finish)}))})),e.on("ResizeEditor ResizeWindow ScrollContent",(()=>{r.get().each(f),a.get().each(b)})),e.on("TableSelectionClear",(()=>{s.get()&&(wd(g),wd(p),s.set(!1)),r.clear(),a.clear()})))},YA=(e,t,o)=>{var n;const s=null!==(n=t.delimiter)&&void 0!==n?n:"\u203a";return{dom:{tag:"div",classes:["tox-statusbar__path"],attributes:{role:"navigation"}},behaviours:gl([Tp.config({mode:"flow",selector:"div[role=button]"}),Cm.config({disabled:o.isDisabled}),oy(),Cw.config({}),Rp.config({}),zp("elementPathEvents",[Lr(((t,n)=>{e.shortcuts.add("alt+F11","focus statusbar elementpath",(()=>Tp.focusIn(t))),e.on("NodeChange",(n=>{const r=(t=>{const o=[];let n=t.length;for(;n-- >0;){const r=t[n];if(1===r.nodeType&&"BR"!==(s=r).nodeName&&!s.getAttribute("data-mce-bogus")&&"bookmark"!==s.getAttribute("data-mce-type")){const t=Xy(e,r);if(t.isDefaultPrevented()||o.push({name:t.name,element:r}),t.isPropagationStopped())break}}var s;return o})(n.parents),a=r.length>0?j(r,((t,n,r)=>{const a=((t,n,s)=>Mh.sketch({dom:{tag:"div",classes:["tox-statusbar__path-item"],attributes:{"data-index":s,"aria-level":s+1}},components:[Ga(t)],action:t=>{e.focus(),e.selection.select(n),e.nodeChanged()},buttonBehaviours:gl([ny(o.isDisabled),oy()])}))(n.name,n.element,r);return 0===r?t.concat([a]):t.concat([{dom:{tag:"div",classes:["tox-statusbar__path-divider"],attributes:{"aria-hidden":!0}},components:[Ga(` ${s} `)]},a])}),[]):[];Rp.set(t,a)}))}))])]),components:[]}};var KA;!function(e){e[e.None=0]="None",e[e.Both=1]="Both",e[e.Vertical=2]="Vertical"}(KA||(KA={}));const ZA=(e,t,o)=>{const n=Ie(e.getContainer()),s=((e,t,o,n,s)=>{const r={height:sB(n+t.top,tf(e),nf(e))};return o===KA.Both&&(r.width=sB(s+t.left,ef(e),of(e))),r})(e,t,o,Ht(n),$t(n));le(s,((e,t)=>{h(e)&&_t(n,t,nB(e))})),(e=>{e.dispatch("ResizeEditor")})(e)},QA=(e,t,o,n)=>{const s=Lt(20*o,20*n);return ZA(e,s,t),M.some(!0)},eD=(e,t)=>({dom:{tag:"div",classes:["tox-statusbar"]},components:(()=>{const o=(()=>{const o=[];return Tf(e)&&o.push(YA(e,{},t)),e.hasPlugin("wordcount")&&o.push(((e,t)=>{const o=(e,o,n)=>Rp.set(e,[Ga(t.translate(["{0} "+n,o[n]]))]);return Mh.sketch({dom:{tag:"button",classes:["tox-statusbar__wordcount"]},components:[],buttonBehaviours:gl([ny(t.isDisabled),oy(),Cw.config({}),Rp.config({}),ou.config({store:{mode:"memory",initialValue:{mode:"words",count:{words:0,characters:0}}}}),zp("wordcount-events",[jr((e=>{const t=ou.getValue(e),n="words"===t.mode?"characters":"words";ou.setValue(e,{mode:n,count:t.count}),o(e,t.count,n)})),Lr((t=>{e.on("wordCountUpdate",(e=>{const{mode:n}=ou.getValue(t);ou.setValue(t,{mode:n,count:e.wordCount}),o(t,e.wordCount,n)}))}))])]),eventOrder:{[nr()]:["disabling","alloy.base.behaviour","wordcount-events"]}})})(e,t)),Ef(e)&&o.push({dom:{tag:"span",classes:["tox-statusbar__branding"]},components:[{dom:{tag:"a",attributes:{href:"https://www.tiny.cloud/powered-by-tiny?utm_campaign=editor_referral&utm_medium=poweredby&utm_source=tinymce&utm_content=v6",rel:"noopener",target:"_blank","aria-label":Ah.translate(["Powered by {0}","Tiny"])},innerHtml:'<svg width="50px" height="16px" viewBox="0 0 50 16" xmlns="http://www.w3.org/2000/svg">\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M10.143 0c2.608.015 5.186 2.178 5.186 5.331 0 0 .077 3.812-.084 4.87-.361 2.41-2.164 4.074-4.65 4.496-1.453.284-2.523.49-3.212.623-.373.071-.634.122-.785.152-.184.038-.997.145-1.35.145-2.732 0-5.21-2.04-5.248-5.33 0 0 0-3.514.03-4.442.093-2.4 1.758-4.342 4.926-4.963 0 0 3.875-.752 4.036-.782.368-.07.775-.1 1.15-.1Zm1.826 2.8L5.83 3.989v2.393l-2.455.475v5.968l6.137-1.189V9.243l2.456-.476V2.8ZM5.83 6.382l3.682-.713v3.574l-3.682.713V6.382Zm27.173-1.64-.084-1.066h-2.226v9.132h2.456V7.743c-.008-1.151.998-2.064 2.149-2.072 1.15-.008 1.987.92 1.995 2.072v5.065h2.455V7.359c-.015-2.18-1.657-3.929-3.837-3.913a3.993 3.993 0 0 0-2.908 1.296Zm-6.3-4.266L29.16 0v2.387l-2.456.475V.476Zm0 3.2v9.132h2.456V3.676h-2.456Zm18.179 11.787L49.11 3.676H46.58l-1.612 4.527-.46 1.382-.384-1.382-1.611-4.527H39.98l3.3 9.132L42.15 16l2.732-.537ZM22.867 9.738c0 .752.568 1.075.921 1.075.353 0 .668-.047.998-.154l.537 1.765c-.23.154-.92.537-2.225.537-1.305 0-2.655-.997-2.686-2.686a136.877 136.877 0 0 1 0-4.374H18.8V3.676h1.612v-1.98l2.455-.476v2.456h2.302V5.9h-2.302v3.837Z"/>\n</svg>\n'.trim()},behaviours:gl([Wp.config({})])}]}),o.length>0?[{dom:{tag:"div",classes:["tox-statusbar__text-container"]},components:o}]:[]})(),n=((e,t)=>{const o=(e=>{const t=Mf(e);return!1===t?KA.None:"both"===t?KA.Both:KA.Vertical})(e);return o===KA.None?M.none():M.some(Nh("resize-handle",{tag:"div",classes:["tox-statusbar__resize-handle"],attributes:{title:t.translate("Resize")},behaviours:[GA.config({mode:"mouse",repositionTarget:!1,onDrag:(t,n,s)=>ZA(e,s,o),blockerClass:"tox-blocker"}),Tp.config({mode:"special",onLeft:()=>QA(e,o,-1,0),onRight:()=>QA(e,o,1,0),onUp:()=>QA(e,o,0,-1),onDown:()=>QA(e,o,0,1)}),Cw.config({}),Wp.config({})]},t.icons))})(e,t);return o.concat(n.toArray())})()}),tD=e=>e.get().getOrDie("UI has not been rendered"),oD=e=>{const t=e.inline,o=t?cB:tB,n=Gf(e)?wT:q_,s=Wl(),r=Wl(),a=Wl(),i=Wl(),l=_o().deviceType.isTouch()?["tox-platform-touch"]:[],c=Nf(e),d=df(e),u=Bh({dom:{tag:"div",classes:["tox-anchorbar"]}}),m=()=>r.get().bind(YE.getHeader),g=()=>Ko.fromOption(s.get(),"UI has not been rendered"),p=()=>r.get().bind((e=>YE.getToolbar(e))).getOrDie("Could not find more toolbar element"),h=()=>r.get().bind((e=>YE.getThrobber(e))).getOrDie("Could not find throbber element"),f=((e,t,o)=>{const n=xs(!1),s=(e=>{const t=xs(Nf(e)?"bottom":"top");return{isPositionedAtTop:()=>"top"===t.get(),getDockingMode:t.get,setDockingMode:t.set}})(t),r={shared:{providers:{icons:()=>t.ui.registry.getAll().icons,menuItems:()=>t.ui.registry.getAll().menuItems,translate:Ah.translate,isDisabled:()=>t.mode.isReadOnly()||!t.ui.isEnabled(),getOption:t.options.get},interpreter:e=>((e,t,o)=>SO(xO,e,{},o))(e,0,r),anchors:qO(t,o,s.isPositionedAtTop),header:s,getSink:e},urlinput:F_(t),styles:l_(t),colorinput:ZO(t),dialog:e_(t),isContextMenuOpen:()=>n.get(),setContextMenuState:e=>n.set(e)};return r})(g,e,(()=>r.get().bind((e=>u.getOpt(e))).getOrDie("Could not find a anchor bar element"))),b=()=>{const o=(()=>{const t={attributes:{[lc]:c?ic.BottomToTop:ic.TopToBottom}},o=YE.parts.menubar({dom:{tag:"div",classes:["tox-menubar"]},backstage:f,onEscape:()=>{e.focus()}}),n=YE.parts.toolbar({dom:{tag:"div",classes:["tox-toolbar"]},getSink:g,providers:f.shared.providers,onEscape:()=>{e.focus()},type:d,lazyToolbar:p,lazyHeader:()=>m().getOrDie("Could not find header element"),...t}),s=YE.parts["multiple-toolbar"]({dom:{tag:"div",classes:["tox-toolbar-overlord"]},providers:f.shared.providers,onEscape:()=>{e.focus()},type:d}),r=Pf(e),a=zf(e),i=If(e),l=Df(e),h=YE.parts.promotion({dom:{tag:"div",classes:["tox-promotion"]}}),b=l?[h,o]:[o];return YE.parts.header({dom:{tag:"div",classes:["tox-editor-header"],...t},components:q([i?b:[],r?[s]:a?[n]:[],Wf(e)?[]:[u.asSpec()]]),sticky:Gf(e),editor:e,sharedBackstage:f.shared})})(),n={dom:{tag:"div",classes:["tox-sidebar-wrap"]},components:[YE.parts.socket({dom:{tag:"div",classes:["tox-edit-area"]}}),YE.parts.sidebar({dom:{tag:"div",classes:["tox-sidebar"]}})]},s=YE.parts.throbber({dom:{tag:"div",classes:["tox-throbber"]},backstage:f}),i=_f(e)&&!t?M.some(eD(e,f.shared.providers)):M.none(),h=q([c?[]:[o],t?[]:[n],c?[o]:[]]),b=q([[{dom:{tag:"div",classes:["tox-editor-container"]},components:h}],t?[]:i.toArray(),[s]]),v=jf(e),y={role:"application",...Ah.isRtl()?{dir:"rtl"}:{},...v?{"aria-hidden":"true"}:{}},x=Ja(YE.sketch({dom:{tag:"div",classes:["tox","tox-tinymce"].concat(t?["tox-tinymce-inline"]:[]).concat(c?["tox-tinymce--toolbar-bottom"]:[]).concat(l),styles:{visibility:"hidden",...v?{opacity:"0",border:"0"}:{}},attributes:y},components:b,behaviours:gl([oy(),Cm.config({disableClass:"tox-tinymce--disabled"}),Tp.config({mode:"cyclic",selector:".tox-menubar, .tox-toolbar, .tox-toolbar__primary, .tox-toolbar__overflow--open, .tox-sidebar__overflow--open, .tox-statusbar__path, .tox-statusbar__wordcount, .tox-statusbar__branding a, .tox-statusbar__resize-handle"})])})),w=bw(x);return r.set(x),a.set(w),{mothership:w,outerContainer:x}},v=t=>{const o=nB((e=>{const t=(e=>{const t=Zh(e),o=tf(e),n=nf(e);return oB(t).map((e=>sB(e,o,n)))})(e);return t.getOr(Zh(e))})(e)),n=nB((e=>rB(e).getOr(Qh(e)))(e));return e.inline||(Ft("div","width",n)&&_t(t.element,"width",n),Ft("div","height",o)?_t(t.element,"height",o):_t(t.element,"height","400px")),o};return{getMothership:()=>tD(a),getUiMothership:()=>tD(i),backstage:f,renderUI:()=>{const{mothership:t,outerContainer:r}=b(),{uiMothership:a,sink:c}=(()=>{const t=Uf(e),o=Xe(ht(),t)&&"grid"===Mt(t,"display"),r={dom:{tag:"div",classes:["tox","tox-silver-sink","tox-tinymce-aux"].concat(l),attributes:{...Ah.isRtl()?{dir:"rtl"}:{}}},behaviours:gl([ud.config({useFixed:()=>n.isDocked(m)})])},a={dom:{styles:{width:document.body.clientWidth+"px"}},events:Br([Fr(gr(),(e=>{_t(e.element,"width",document.body.clientWidth+"px")}))])},c=Ja(cn(r,o?a:{})),d=bw(c);return s.set(c),i.set(d),{sink:c,uiMothership:d}})();ce(uf(e),((t,o)=>{e.ui.registry.addGroupToolbarButton(o,t)}));const{buttons:u,menuItems:p,contextToolbars:y,sidebars:x}=e.ui.registry.getAll(),w=Hf(e),S={menuItems:p,menus:$f(e),menubar:bf(e),toolbar:w.getOrThunk((()=>vf(e))),allowToolbarGroups:d===Uh.floating,buttons:u,sidebar:x};(t=>{e.addShortcut("alt+F9","focus menubar",(()=>{YE.focusMenubar(t)})),e.addShortcut("alt+F10","focus toolbar",(()=>{YE.focusToolbar(t)})),e.addCommand("ToggleToolbarDrawer",(()=>{YE.toggleToolbarDrawer(t)})),e.addQueryStateHandler("ToggleToolbarDrawer",(()=>YE.isToolbarDrawerToggled(t)))})(r),((e,t,o)=>{const n=(e,n)=>{N([t,o],(t=>{t.broadcastEvent(e,n)}))},s=(e,n)=>{N([t,o],(t=>{t.broadcastOn([e],n)}))},r=e=>s(zd(),{target:e.target}),a=Lo(),i=jl(a,"touchstart",r),l=jl(a,"touchmove",(e=>n(dr(),e))),c=jl(a,"touchend",(e=>n(ur(),e))),d=jl(a,"mousedown",r),u=jl(a,"mouseup",(e=>{0===e.raw.button&&s(Pd(),{target:e.target})})),m=e=>s(zd(),{target:Ie(e.target)}),g=e=>{0===e.button&&s(Pd(),{target:Ie(e.target)})},p=()=>{N(e.editorManager.get(),(t=>{e!==t&&t.dispatch("DismissPopups",{relatedTarget:e})}))},h=e=>n(mr(),$l(e)),f=e=>{s(Hd(),{}),n(gr(),$l(e))},b=()=>s(Hd(),{}),v=t=>{t.state&&s(zd(),{target:Ie(e.getContainer())})},y=e=>{s(zd(),{target:Ie(e.relatedTarget.getContainer())})};e.on("PostRender",(()=>{e.on("click",m),e.on("tap",m),e.on("mouseup",g),e.on("mousedown",p),e.on("ScrollWindow",h),e.on("ResizeWindow",f),e.on("ResizeEditor",b),e.on("AfterProgressState",v),e.on("DismissPopups",y)})),e.on("remove",(()=>{e.off("click",m),e.off("tap",m),e.off("mouseup",g),e.off("mousedown",p),e.off("ScrollWindow",h),e.off("ResizeWindow",f),e.off("ResizeEditor",b),e.off("AfterProgressState",v),e.off("DismissPopups",y),d.unbind(),i.unbind(),l.unbind(),c.unbind(),u.unbind()})),e.on("detach",(()=>{Od(t),Od(o),t.destroy(),o.destroy()}))})(e,t,a),n.setup(e,f.shared,m),PB(e,f),aA(e,g,f),(e=>{const{sidebars:t}=e.ui.registry.getAll();N(ae(t),(o=>{const n=t[o],s=()=>xe(M.from(e.queryCommandValue("ToggleSidebar")),o);e.ui.registry.addToggleButton(o,{icon:n.icon,tooltip:n.tooltip,onAction:t=>{e.execCommand("ToggleSidebar",!1,o),t.setActive(s())},onSetup:t=>{t.setActive(s());const o=()=>t.setActive(s());return e.on("ToggleSidebar",o),()=>{e.off("ToggleSidebar",o)}}})}))})(e),lE(e,h,f.shared),EB(e,y,c,{backstage:f}),JA(e,c);const C={mothership:t,uiMothership:a,outerContainer:r,sink:c},k={targetNode:e.getElement(),height:v(r)};return o.render(e,C,S,f,k)}}},nD=y([Xn("lazySink"),ns("dragBlockClass"),bs("getBounds",Xo),us("useTabstopAt",T),us("eventOrder",{}),nu("modalBehaviours",[Tp]),Si("onExecute"),ki("onEscape")]),sD={sketch:x},rD=y([Du({name:"draghandle",overrides:(e,t)=>({behaviours:gl([GA.config({mode:"mouse",getTarget:e=>oi(e,'[role="dialog"]').getOr(e),blockerClass:e.dragBlockClass.getOrDie(new Error("The drag blocker class was not specified for a dialog with a drag handle: \n"+JSON.stringify(t,null,2)).message),getBounds:e.getDragBounds})])})}),Bu({schema:[Xn("dom")],name:"title"}),Bu({factory:sD,schema:[Xn("dom")],name:"close"}),Bu({factory:sD,schema:[Xn("dom")],name:"body"}),Du({factory:sD,schema:[Xn("dom")],name:"footer"}),Au({factory:{sketch:(e,t)=>({...e,dom:t.dom,components:t.components})},schema:[us("dom",{tag:"div",styles:{position:"fixed",left:"0px",top:"0px",right:"0px",bottom:"0px"}}),us("components",[])],name:"blocker"})]),aD=rm({name:"ModalDialog",configFields:nD(),partFields:rD(),factory:(e,t,o,n)=>{const s=Wl(),r=Qr("modal-events"),a={...e.eventOrder,[pr()]:[r].concat(e.eventOrder["alloy.system.attached"]||[])};return{uid:e.uid,dom:e.dom,components:t,apis:{show:t=>{s.set(t);const o=e.lazySink(t).getOrDie(),r=n.blocker(),a=o.getSystem().build({...r,components:r.components.concat([Ya(t)]),behaviours:gl([Wp.config({}),zp("dialog-blocker-events",[Nr(Ns(),(()=>{Tp.focusIn(t)}))])])});vd(o,a),Tp.focusIn(t)},hide:e=>{s.clear(),et(e.element).each((t=>{e.getSystem().getByDom(t).each((e=>{wd(e)}))}))},getBody:t=>Gu(t,e,"body"),getFooter:t=>Gu(t,e,"footer"),setIdle:e=>{rE.unblock(e)},setBusy:(e,t)=>{rE.block(e,t)}},eventOrder:a,domModification:{attributes:{role:"dialog","aria-modal":"true"}},behaviours:ru(e.modalBehaviours,[Rp.config({}),Tp.config({mode:"cyclic",onEnter:e.onExecute,onEscape:e.onEscape,useTabstopAt:e.useTabstopAt}),rE.config({getRoot:s.get}),zp(r,[Lr((t=>{((e,t)=>{const o=wt(e,"id").fold((()=>{const e=Qr("dialog-label");return vt(t,"id",e),e}),x);vt(e,"aria-labelledby",o)})(t.element,Gu(t,e,"title").element),((e,t)=>{const o=M.from(xt(e,"id")).fold((()=>{const e=Qr("dialog-describe");return vt(t,"id",e),e}),x);vt(e,"aria-describedby",o)})(t.element,Gu(t,e,"body").element)}))])])}},apis:{show:(e,t)=>{e.show(t)},hide:(e,t)=>{e.hide(t)},getBody:(e,t)=>e.getBody(t),getFooter:(e,t)=>e.getFooter(t),setBusy:(e,t,o)=>{e.setBusy(t,o)},setIdle:(e,t)=>{e.setIdle(t)}}}),iD=kn([Rb,zb].concat(Dv)),lD=Fn,cD=[cv("button"),Yb,hs("align","end",["start","end"]),sv,nv,is("buttonType",["primary","secondary"])],dD=[...cD,Pb],uD=[Zn("type",["submit","cancel","custom"]),...dD],mD=[Zn("type",["menu"]),Jb,Kb,Yb,os("items",iD),...cD],gD=jn("type",{submit:uD,cancel:uD,custom:uD,menu:mD}),pD=[Rb,Pb,Zn("level",["info","warn","error","success"]),Lb,us("url","")],hD=kn(pD),fD=[Rb,Pb,nv,cv("button"),Yb,ov,is("buttonType",["primary","secondary","toolbar"]),sv],bD=kn(fD),vD=[Rb,zb],yD=vD.concat([Zb]),xD=vD.concat([Hb,nv]),wD=kn(xD),SD=Fn,CD=yD.concat([rv("auto")]),kD=kn(CD),OD=En([Wb,Pb,Lb]),_D=kn(yD),TD=Dn,ED=kn(yD),MD=Dn,BD=vD.concat([ps("tag","textarea"),Kn("scriptId"),Kn("scriptUrl"),ms("settings",void 0,Rn)]),AD=vD.concat([ps("tag","textarea"),Qn("init")]),DD=Hn((e=>Nn("customeditor.old",Cn(AD),e).orThunk((()=>Nn("customeditor.new",Cn(BD),e))))),FD=Dn,ID=kn(yD),VD=On(vn),RD=e=>[Rb,Yn("columns"),e],zD=[Rb,Kn("html"),hs("presets","presentation",["presentation","document"])],HD=kn(zD),PD=yD.concat([fs("sandboxed",!0),fs("transparent",!0)]),ND=kn(PD),LD=Dn,WD=kn(vD.concat([as("height")])),UD=kn([Kn("url"),rs("zoom"),rs("cachedWidth"),rs("cachedHeight")]),jD=yD.concat([as("inputMode"),as("placeholder"),fs("maximized",!1),nv]),GD=kn(jD),$D=Dn,qD=e=>[Rb,Hb,e],XD=[Pb,Wb],JD=[Pb,os("items",((e,t)=>{const o=Xt(t);return{extract:(e,t)=>o().extract(e,t),toString:()=>o().toString()}})(0,(()=>YD)))],YD=_n([kn(XD),kn(JD)]),KD=yD.concat([os("items",YD),nv]),ZD=kn(KD),QD=Dn,eF=yD.concat([ts("items",[Pb,Wb]),gs("size",1),nv]),tF=kn(eF),oF=Dn,nF=yD.concat([fs("constrain",!0),nv]),sF=kn(nF),rF=kn([Kn("width"),Kn("height")]),aF=vD.concat([Hb,gs("min",0),gs("max",0)]),iF=kn(aF),lF=An,cF=[Rb,os("header",Dn),os("cells",On(Dn))],dF=kn(cF),uF=yD.concat([as("placeholder"),fs("maximized",!1),nv]),mF=kn(uF),gF=Dn,pF=yD.concat([hs("filetype","file",["image","media","file"]),nv]),hF=kn(pF),fF=kn([Wb,av]),bF=e=>Gn("items","items",{tag:"required",process:{}},On(Hn((t=>Nn(`Checking item of ${e}`,vF,t).fold((e=>Ko.error(Un(e))),(e=>Ko.value(e))))))),vF=wn((()=>{return zn("type",{alertbanner:hD,bar:kn((e=bF("bar"),[Rb,e])),button:bD,checkbox:wD,colorinput:_D,colorpicker:ED,dropzone:ID,grid:kn(RD(bF("grid"))),iframe:ND,input:GD,listbox:ZD,selectbox:tF,sizeinput:sF,slider:iF,textarea:mF,urlinput:hF,customeditor:DD,htmlpanel:HD,imagepreview:WD,collection:kD,label:kn(qD(bF("label"))),table:dF,panel:xF});var e})),yF=[Rb,us("classes",[]),os("items",vF)],xF=kn(yF),wF=[cv("tab"),Nb,os("items",vF)],SF=[Rb,ts("tabs",wF)],CF=kn(SF),kF=dD,OF=gD,_F=kn([Kn("title"),Jn("body",zn("type",{panel:xF,tabpanel:CF})),ps("size","normal"),os("buttons",OF),us("initialData",{}),bs("onAction",b),bs("onChange",b),bs("onSubmit",b),bs("onClose",b),bs("onCancel",b),bs("onTabChange",b)]),TF=kn([Zn("type",["cancel","custom"]),...kF]),EF=kn([Kn("title"),Kn("url"),rs("height"),rs("width"),cs("buttons",TF),bs("onAction",b),bs("onCancel",b),bs("onClose",b),bs("onMessage",b)]),MF=e=>a(e)?[e].concat(X(fe(e),MF)):l(e)?X(e,MF):[],BF=e=>r(e.type)&&r(e.name),AF={checkbox:SD,colorinput:TD,colorpicker:MD,dropzone:VD,input:$D,iframe:LD,imagepreview:UD,selectbox:oF,sizeinput:rF,slider:lF,listbox:QD,size:rF,textarea:gF,urlinput:fF,customeditor:FD,collection:OD,togglemenuitem:lD},DF=e=>{const t=(e=>W(MF(e),BF))(e),o=X(t,(e=>(e=>M.from(AF[e.type]))(e).fold((()=>[]),(t=>[Jn(e.name,t)]))));return kn(o)},FF=e=>{var t;return{internalDialog:Ln(Nn("dialog",_F,e)),dataValidator:DF(e),initialData:null!==(t=e.initialData)&&void 0!==t?t:{}}},IF={open:(e,t)=>{const o=FF(t);return e(o.internalDialog,o.initialData,o.dataValidator)},openUrl:(e,t)=>e(Ln(Nn("dialog",EF,t))),redial:e=>FF(e)},VF=e=>{const t=[],o={};return le(e,((e,n)=>{e.fold((()=>{t.push(n)}),(e=>{o[n]=e}))})),t.length>0?Ko.error(t):Ko.value(o)},RF=(e,t,o)=>{const n=Bh(HC.sketch((n=>({dom:{tag:"div",classes:["tox-form"].concat(e.classes)},components:P(e.items,(e=>wO(n,e,t,o)))}))));return{dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[n.asSpec()]}],behaviours:gl([Tp.config({mode:"acyclic",useTabstopAt:C(ck)}),(s=n,cm.config({find:s.getOpt})),YC(n,{postprocess:e=>VF(e).fold((e=>(console.error(e),{})),x)})])};var s},zF=sm({name:"TabButton",configFields:[us("uid",void 0),Xn("value"),Gn("dom","dom",gn((()=>({attributes:{role:"tab",id:Qr("aria"),"aria-selected":"false"}}))),Mn()),ns("action"),us("domModification",{}),nu("tabButtonBehaviours",[Wp,Tp,ou]),Xn("view")],factory:(e,t)=>({uid:e.uid,dom:e.dom,components:e.components,events:Zp(e.action),behaviours:ru(e.tabButtonBehaviours,[Wp.config({}),Tp.config({mode:"execution",useSpace:!0,useEnter:!0}),ou.config({store:{mode:"memory",initialValue:e.value}})]),domModification:e.domModification})}),HF=y([Xn("tabs"),Xn("dom"),us("clickToDismiss",!1),nu("tabbarBehaviours",[Fm,Tp]),yi(["tabClass","selectedClass"])]),PF=Fu({factory:zF,name:"tabs",unit:"tab",overrides:e=>{const t=(e,t)=>{Fm.dehighlight(e,t),Or(e,wr(),{tabbar:e,button:t})},o=(e,t)=>{Fm.highlight(e,t),Or(e,xr(),{tabbar:e,button:t})};return{action:n=>{const s=n.getSystem().getByUid(e.uid).getOrDie(),r=Fm.isHighlighted(s,n);(r&&e.clickToDismiss?t:r?b:o)(s,n)},domModification:{classes:[e.markers.tabClass]}}}}),NF=y([PF]),LF=rm({name:"Tabbar",configFields:HF(),partFields:NF(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,"debug.sketcher":"Tabbar",domModification:{attributes:{role:"tablist"}},behaviours:ru(e.tabbarBehaviours,[Fm.config({highlightClass:e.markers.selectedClass,itemClass:e.markers.tabClass,onHighlight:(e,t)=>{vt(t.element,"aria-selected","true")},onDehighlight:(e,t)=>{vt(t.element,"aria-selected","false")}}),Tp.config({mode:"flow",getInitial:e=>Fm.getHighlighted(e).map((e=>e.element)),selector:"."+e.markers.tabClass,executeOnMove:!0})])})}),WF=sm({name:"Tabview",configFields:[nu("tabviewBehaviours",[Rp])],factory:(e,t)=>({uid:e.uid,dom:e.dom,behaviours:ru(e.tabviewBehaviours,[Rp.config({})]),domModification:{attributes:{role:"tabpanel"}}})}),UF=y([us("selectFirst",!0),wi("onChangeTab"),wi("onDismissTab"),us("tabs",[]),nu("tabSectionBehaviours",[])]),jF=Bu({factory:LF,schema:[Xn("dom"),es("markers",[Xn("tabClass"),Xn("selectedClass")])],name:"tabbar",defaults:e=>({tabs:e.tabs})}),GF=Bu({factory:WF,name:"tabview"}),$F=y([jF,GF]),qF=rm({name:"TabSection",configFields:UF(),partFields:$F(),factory:(e,t,o,n)=>{const s=(t,o)=>{ju(t,e,"tabbar").each((e=>{o(e).each(_r)}))};return{uid:e.uid,dom:e.dom,components:t,behaviours:su(e.tabSectionBehaviours),events:Br(q([e.selectFirst?[Lr(((e,t)=>{s(e,Fm.getFirst)}))]:[],[Fr(xr(),((t,o)=>{(t=>{const o=ou.getValue(t);ju(t,e,"tabview").each((n=>{G(e.tabs,(e=>e.value===o)).each((o=>{const s=o.view();wt(t.element,"id").each((e=>{vt(n.element,"aria-labelledby",e)})),Rp.set(n,s),e.onChangeTab(n,t,s)}))}))})(o.event.button)})),Fr(wr(),((t,o)=>{const n=o.event.button;e.onDismissTab(t,n)}))]])),apis:{getViewItems:t=>ju(t,e,"tabview").map((e=>Rp.contents(e))).getOr([]),showTab:(e,t)=>{s(e,(e=>{const o=Fm.getCandidates(e);return G(o,(e=>ou.getValue(e)===t)).filter((t=>!Fm.isHighlighted(e,t)))}))}}}},apis:{getViewItems:(e,t)=>e.getViewItems(t),showTab:(e,t,o)=>{e.showTab(t,o)}}}),XF=(e,t)=>{_t(e,"height",t+"px"),_t(e,"flex-basis",t+"px")},JF=(e,t,o)=>{oi(e,'[role="dialog"]').each((e=>{si(e,'[role="tablist"]').each((n=>{o.get().map((o=>(_t(t,"height","0"),_t(t,"flex-basis","0"),Math.min(o,((e,t,o)=>{const n=Ze(e).dom,s=oi(e,".tox-dialog-wrap").getOr(e);let r;r="fixed"===Mt(s,"position")?Math.max(n.clientHeight,window.innerHeight):Math.max(n.offsetHeight,n.scrollHeight);const a=Ht(t),i=t.dom.offsetLeft>=o.dom.offsetLeft+$t(o)?Math.max(Ht(o),a):a,l=parseInt(Mt(e,"margin-top"),10)||0,c=parseInt(Mt(e,"margin-bottom"),10)||0;return r-(Ht(e)+l+c-i)})(e,t,n))))).each((e=>{XF(t,e)}))}))}))},YF=e=>si(e,'[role="tabpanel"]'),KF="send-data-to-section",ZF="send-data-to-view",QF=(e,t,o)=>{const n=xs({}),s=e=>{const t=ou.getValue(e),o=VF(t).getOr({}),s=n.get(),r=cn(s,o);n.set(r)},r=e=>{const t=n.get();ou.setValue(e,t)},a=xs(null),i=P(e.tabs,(e=>({value:e.name,dom:{tag:"div",classes:["tox-dialog__body-nav-item"]},components:[Ga(o.shared.providers.translate(e.title))],view:()=>[HC.sketch((n=>({dom:{tag:"div",classes:["tox-form"]},components:P(e.items,(e=>wO(n,e,t,o))),formBehaviours:gl([Tp.config({mode:"acyclic",useTabstopAt:C(ck)}),zp("TabView.form.events",[Lr(r),Wr(s)]),yl.config({channels:Cs([{key:KF,value:{onReceive:s}},{key:ZF,value:{onReceive:r}}])})])})))]}))),l=(e=>{const t=Wl(),o=[Lr((o=>{const n=o.element;YF(n).each((s=>{_t(s,"visibility","hidden"),o.getSystem().getByDom(s).toOptional().each((o=>{const n=((e,t,o)=>P(e,((n,s)=>{Rp.set(o,e[s].view());const r=t.dom.getBoundingClientRect();return Rp.set(o,[]),r.height})))(e,s,o),r=(e=>oe(ee(e,((e,t)=>e>t?-1:e<t?1:0))))(n);r.fold(t.clear,t.set)})),JF(n,s,t),It(s,"visibility"),((e,t)=>{oe(e).each((e=>qF.showTab(t,e.value)))})(e,o),requestAnimationFrame((()=>{JF(n,s,t)}))}))})),Fr(gr(),(e=>{const o=e.element;YF(o).each((e=>{JF(o,e,t)}))})),Fr(zw,((e,o)=>{const n=e.element;YF(n).each((e=>{const o=Cl(dt(e));_t(e,"visibility","hidden");const s=At(e,"height").map((e=>parseInt(e,10)));It(e,"height"),It(e,"flex-basis");const r=e.dom.getBoundingClientRect().height;s.forall((e=>r>e))?(t.set(r),JF(n,e,t)):s.each((t=>{XF(e,t)})),It(e,"visibility"),o.each(wl)}))}))];return{extraEvents:o,selectFirst:!1}})(i);return qF.sketch({dom:{tag:"div",classes:["tox-dialog__body"]},onChangeTab:(e,t,o)=>{const n=ou.getValue(t);Or(e,Rw,{name:n,oldName:a.get()}),a.set(n)},tabs:i,components:[qF.parts.tabbar({dom:{tag:"div",classes:["tox-dialog__body-nav"]},components:[LF.parts.tabs({})],markers:{tabClass:"tox-tab",selectedClass:"tox-dialog__body-nav-item--active"},tabbarBehaviours:gl([Cw.config({})])}),qF.parts.tabview({dom:{tag:"div",classes:["tox-dialog__body-content"]}})],selectFirst:l.selectFirst,tabSectionBehaviours:gl([zp("tabpanel",l.extraEvents),Tp.config({mode:"acyclic"}),cm.config({find:e=>oe(qF.getViewItems(e))}),ZC(M.none(),(e=>(e.getSystem().broadcastOn([KF],{}),n.get())),((e,t)=>{n.set(t),e.getSystem().broadcastOn([ZF],{})}))])})},eI=Qr("update-dialog"),tI=Qr("update-title"),oI=Qr("update-body"),nI=Qr("update-footer"),sI=Qr("body-send-message"),rI=(e,t,o,n,s)=>({dom:{tag:"div",classes:["tox-dialog__content-js"],attributes:{...o.map((e=>({id:e}))).getOr({}),...s?{"aria-live":"polite"}:{}}},components:[],behaviours:gl([$C(0),DM.config({channel:`${oI}-${t}`,updateState:(e,t)=>M.some({isTabPanel:()=>"tabpanel"===t.body.type}),renderComponents:e=>{const t=e.body;return"tabpanel"===t.type?[QF(t,e.initialData,n)]:[RF(t,e.initialData,n)]},initialData:e})])}),aI=qh.deviceType.isTouch(),iI=(e,t)=>({dom:{tag:"div",styles:{display:"none"},classes:["tox-dialog__header"]},components:[e,t]}),lI=(e,t)=>aD.parts.close(Mh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":t.translate("Close")}},action:e,buttonBehaviours:gl([Cw.config({})])})),cI=()=>aD.parts.title({dom:{tag:"div",classes:["tox-dialog__title"],innerHtml:"",styles:{display:"none"}}}),dI=(e,t)=>aD.parts.body({dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[{dom:aE(`<p>${t.translate(e)}</p>`)}]}]}),uI=e=>aD.parts.footer({dom:{tag:"div",classes:["tox-dialog__footer"]},components:e}),mI=(e,t)=>[fw.sketch({dom:{tag:"div",classes:["tox-dialog__footer-start"]},components:e}),fw.sketch({dom:{tag:"div",classes:["tox-dialog__footer-end"]},components:t})],gI=e=>{const t="tox-dialog",o=t+"-wrap",n=o+"__backdrop",s=t+"__disable-scroll";return aD.sketch({lazySink:e.lazySink,onEscape:t=>(e.onEscape(t),M.some(!0)),useTabstopAt:e=>!ck(e),dom:{tag:"div",classes:[t].concat(e.extraClasses),styles:{position:"relative",...e.extraStyles}},components:[e.header,e.body,...e.footer.toArray()],parts:{blocker:{dom:aE(`<div class="${o}"></div>`),components:[{dom:{tag:"div",classes:aI?[n,n+"--opaque"]:[n]}}]}},dragBlockClass:o,modalBehaviours:gl([Wp.config({}),zp("dialog-events",e.dialogEvents.concat([Nr(Ns(),((e,t)=>{Tp.focusIn(e)}))])),zp("scroll-lock",[Lr((()=>{Aa(ht(),s)})),Wr((()=>{Da(ht(),s)}))]),...e.extraBehaviours]),eventOrder:{[nr()]:["dialog-events"],[pr()]:["scroll-lock","dialog-events","alloy.base.behaviour"],[hr()]:["alloy.base.behaviour","dialog-events","scroll-lock"],...e.eventOrder}})},pI=e=>Mh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":e.translate("Close"),title:e.translate("Close")}},components:[Nh("close",{tag:"div",classes:["tox-icon"]},e.icons)],action:e=>{kr(e,Aw)}}),hI=(e,t,o,n)=>({dom:{tag:"div",classes:["tox-dialog__title"],attributes:{...o.map((e=>({id:e}))).getOr({})}},components:[],behaviours:gl([DM.config({channel:`${tI}-${t}`,initialData:e,renderComponents:e=>[Ga(n.translate(e.title))]})])}),fI=()=>({dom:aE('<div class="tox-dialog__draghandle"></div>')}),bI=(e,t,o)=>((e,t,o)=>{const n=aD.parts.title(hI(e,t,M.none(),o)),s=aD.parts.draghandle(fI()),r=aD.parts.close(pI(o)),a=[n].concat(e.draggable?[s]:[]).concat([r]);return fw.sketch({dom:aE('<div class="tox-dialog__header"></div>'),components:a})})({title:o.shared.providers.translate(e),draggable:o.dialog.isDraggableModal()},t,o.shared.providers),vI=(e,t,o)=>({dom:{tag:"div",classes:["tox-dialog__busy-spinner"],attributes:{"aria-label":o.translate(e)},styles:{left:"0px",right:"0px",bottom:"0px",top:"0px",position:"absolute"}},behaviours:t,components:[{dom:aE('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}),yI=(e,t,o)=>({onClose:()=>o.closeWindow(),onBlock:o=>{aD.setBusy(e(),((e,n)=>vI(o.message,n,t)))},onUnblock:()=>{aD.setIdle(e())}}),xI=(e,t,o,n)=>Ja(gI({...e,lazySink:n.shared.getSink,extraBehaviours:[DM.config({channel:`${eI}-${e.id}`,updateState:(e,t)=>M.some(t),initialData:t}),QC({}),...e.extraBehaviours],onEscape:e=>{kr(e,Aw)},dialogEvents:o,eventOrder:{[or()]:[DM.name(),yl.name()],[pr()]:["scroll-lock",DM.name(),"messages","dialog-events","alloy.base.behaviour"],[hr()]:["alloy.base.behaviour","dialog-events","messages",DM.name(),"scroll-lock"]}})),wI=e=>P(e,(e=>"menu"===e.type?(e=>{const t=P(e.items,(e=>({...e,storage:xs(!1)})));return{...e,items:t}})(e):e)),SI=e=>j(e,((e,t)=>"menu"===t.type?j(t.items,((e,t)=>(e[t.name]=t.storage,e)),e):e),{}),CI=(e,t)=>[zr(Ns(),lk),e(Bw,((e,o)=>{t.onClose(),o.onClose()})),e(Aw,((e,t,o,n)=>{t.onCancel(e),kr(n,Bw)})),Fr(Vw,((e,o)=>t.onUnblock())),Fr(Iw,((e,o)=>t.onBlock(o.event)))],kI=(e,t,o)=>{const n=(t,o)=>Fr(t,((t,n)=>{s(t,((s,r)=>{o(e(),s,n.event,t)}))})),s=(e,t)=>{DM.getState(e).get().each((o=>{t(o.internalDialog,e)}))};return[...CI(n,t),n(Fw,((e,t)=>t.onSubmit(e))),n(Mw,((e,t,o)=>{t.onChange(e,{name:o.name})})),n(Dw,((e,t,n,s)=>{const r=()=>Tp.focusIn(s),a=e=>St(e,"disabled")||wt(e,"aria-disabled").exists((e=>"true"===e)),i=dt(s.element),l=Cl(i);t.onAction(e,{name:n.name,value:n.value}),Cl(i).fold(r,(e=>{a(e)||l.exists((t=>Je(e,t)&&a(t)))?r():o().toOptional().filter((t=>!Je(t.element,e))).each(r)}))})),n(Rw,((e,t,o)=>{t.onTabChange(e,{newTabName:o.name,oldTabName:o.oldName})})),Wr((t=>{const o=e();ou.setValue(t,o.getData())}))]},OI=(e,t)=>{const o=t.map((e=>e.footerButtons)).getOr([]),n=L(o,(e=>"start"===e.align)),s=(e,t)=>fw.sketch({dom:{tag:"div",classes:[`tox-dialog__footer-${e}`]},components:P(t,(e=>e.memento.asSpec()))});return[s("start",n.pass),s("end",n.fail)]},_I=(e,t,o)=>({dom:aE('<div class="tox-dialog__footer"></div>'),components:[],behaviours:gl([DM.config({channel:`${nI}-${t}`,initialData:e,updateState:(e,t)=>{const n=P(t.buttons,(e=>{const t=Bh(((e,t)=>rO(e,e.type,t))(e,o));return{name:e.name,align:e.align,memento:t}}));return M.some({lookupByName:t=>((e,t,o)=>G(t,(e=>e.name===o)).bind((t=>t.memento.getOpt(e))))(e,n,t),footerButtons:n})},renderComponents:OI})])}),TI=(e,t,o)=>aD.parts.footer(_I(e,t,o)),EI=(e,t)=>{if(e.getRoot().getSystem().isConnected()){const o=cm.getCurrent(e.getFormWrapper()).getOr(e.getFormWrapper());return HC.getField(o,t).orThunk((()=>{const o=e.getFooter();return DM.getState(o).get().bind((e=>e.lookupByName(t)))}))}return M.none()},MI=(e,t,o)=>{const n=t=>{const o=e.getRoot();o.getSystem().isConnected()&&t(o)},s={getData:()=>{const t=e.getRoot(),n=t.getSystem().isConnected()?e.getFormWrapper():t;return{...ou.getValue(n),...ce(o,(e=>e.get()))}},setData:t=>{n((n=>{const r=s.getData(),a=cn(r,t),i=((e,t)=>{const o=e.getRoot();return DM.getState(o).get().map((e=>Ln(Nn("data",e.dataValidator,t)))).getOr(t)})(e,a),l=e.getFormWrapper();ou.setValue(l,i),le(o,((e,t)=>{ve(a,t)&&e.set(a[t])}))}))},setEnabled:(t,o)=>{EI(e,t).each(o?Cm.enable:Cm.disable)},focus:t=>{EI(e,t).each(Wp.focus)},block:e=>{if(!r(e))throw new Error("The dialogInstanceAPI.block function should be passed a blocking message of type string as an argument");n((t=>{Or(t,Iw,{message:e})}))},unblock:()=>{n((e=>{kr(e,Vw)}))},showTab:t=>{n((o=>{const n=e.getBody();DM.getState(n).get().exists((e=>e.isTabPanel()))&&cm.getCurrent(n).each((e=>{qF.showTab(e,t)}))}))},redial:o=>{n((n=>{const r=e.getId(),a=t(o);n.getSystem().broadcastOn([`${eI}-${r}`],a),n.getSystem().broadcastOn([`${tI}-${r}`],a.internalDialog),n.getSystem().broadcastOn([`${oI}-${r}`],a.internalDialog),n.getSystem().broadcastOn([`${nI}-${r}`],a.internalDialog),s.setData(a.initialData)}))},close:()=>{n((e=>{kr(e,Bw)}))}};return s};var BI=tinymce.util.Tools.resolve("tinymce.util.URI");const AI=["insertContent","setContent","execCommand","close","block","unblock"],DI=e=>a(e)&&-1!==AI.indexOf(e.mceAction),FI=(e,t,o,n)=>{const s=Qr("dialog"),i=bI(e.title,s,n),l=(e=>{const t={dom:{tag:"div",classes:["tox-dialog__content-js"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-iframe"]},components:[ak({dom:{tag:"iframe",attributes:{src:e.url}},behaviours:gl([Cw.config({}),Wp.config({})])})]}],behaviours:gl([Tp.config({mode:"acyclic",useTabstopAt:C(ck)})])};return aD.parts.body(t)})(e),c=e.buttons.bind((e=>0===e.length?M.none():M.some(TI({buttons:e},s,n)))),u=((e,t)=>{const o=(t,o)=>Fr(t,((t,s)=>{n(t,((n,r)=>{o(e(),n,s.event,t)}))})),n=(e,t)=>{DM.getState(e).get().each((o=>{t(o,e)}))};return[...CI(o,t),o(Dw,((e,t,o)=>{t.onAction(e,{name:o.name})}))]})((()=>x),yI((()=>y),n.shared.providers,t)),m={...e.height.fold((()=>({})),(e=>({height:e+"px","max-height":e+"px"}))),...e.width.fold((()=>({})),(e=>({width:e+"px","max-width":e+"px"})))},p=e.width.isNone()&&e.height.isNone()?["tox-dialog--width-lg"]:[],h=new BI(e.url,{base_uri:new BI(window.location.href)}),f=`${h.protocol}://${h.host}${h.port?":"+h.port:""}`,b=Ll(),v=[zp("messages",[Lr((()=>{const t=jl(Ie(window),"message",(t=>{if(h.isSameOrigin(new BI(t.raw.origin))){const n=t.raw.data;DI(n)?((e,t,o)=>{switch(o.mceAction){case"insertContent":e.insertContent(o.content);break;case"setContent":e.setContent(o.content);break;case"execCommand":const n=!!d(o.ui)&&o.ui;e.execCommand(o.cmd,n,o.value);break;case"close":t.close();break;case"block":t.block(o.message);break;case"unblock":t.unblock()}})(o,x,n):(e=>!DI(e)&&a(e)&&ve(e,"mceAction"))(n)&&e.onMessage(x,n)}}));b.set(t)})),Wr(b.clear)]),yl.config({channels:{[sI]:{onReceive:(e,t)=>{si(e.element,"iframe").each((e=>{const o=e.dom.contentWindow;g(o)&&o.postMessage(t,f)}))}}}})],y=xI({id:s,header:i,body:l,footer:c,extraClasses:p,extraBehaviours:v,extraStyles:m},e,u,n),x=(e=>{const t=t=>{e.getSystem().isConnected()&&t(e)};return{block:e=>{if(!r(e))throw new Error("The urlDialogInstanceAPI.block function should be passed a blocking message of type string as an argument");t((t=>{Or(t,Iw,{message:e})}))},unblock:()=>{t((e=>{kr(e,Vw)}))},close:()=>{t((e=>{kr(e,Bw)}))},sendMessage:e=>{t((t=>{t.getSystem().broadcastOn([sI],e)}))}}})(y);return{dialog:y,instanceApi:x}},II=(e,t,o)=>t&&o?[]:[uT.config({contextual:{lazyContext:()=>M.some($o(Ie(e.getContentAreaContainer()))),fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top"]})],VI=e=>{const t=e.backstage,o=e.editor,n=Gf(o),s=(e=>{const t=e.shared;return{open:(o,n)=>{const s=()=>{aD.hide(l),n()},r=Bh(rO({name:"close-alert",text:"OK",primary:!0,buttonType:M.some("primary"),align:"end",enabled:!0,icon:M.none()},"cancel",e)),a=cI(),i=lI(s,t.providers),l=Ja(gI({lazySink:()=>t.getSink(),header:iI(a,i),body:dI(o,t.providers),footer:M.some(uI(mI([],[r.asSpec()]))),onEscape:s,extraClasses:["tox-alert-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Fr(Aw,s)],eventOrder:{}}));aD.show(l);const c=r.get(l);Wp.focus(c)}}})(t),r=(e=>{const t=e.shared;return{open:(o,n)=>{const s=e=>{aD.hide(c),n(e)},r=Bh(rO({name:"yes",text:"Yes",primary:!0,buttonType:M.some("primary"),align:"end",enabled:!0,icon:M.none()},"submit",e)),a=rO({name:"no",text:"No",primary:!1,buttonType:M.some("secondary"),align:"end",enabled:!0,icon:M.none()},"cancel",e),i=cI(),l=lI((()=>s(!1)),t.providers),c=Ja(gI({lazySink:()=>t.getSink(),header:iI(i,l),body:dI(o,t.providers),footer:M.some(uI(mI([],[a,r.asSpec()]))),onEscape:()=>s(!1),extraClasses:["tox-confirm-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Fr(Aw,(()=>s(!1))),Fr(Fw,(()=>s(!0)))],eventOrder:{}}));aD.show(c);const d=r.get(c);Wp.focus(d)}}})(t),a=(e,o)=>IF.open(((e,n,s)=>{const r=n,a=((e,t,o)=>{const n=Qr("dialog"),s=e.internalDialog,r=bI(s.title,n,o),a=((e,t,o)=>{const n=rI(e,t,M.none(),o,!1);return aD.parts.body(n)})({body:s.body,initialData:s.initialData},n,o),i=wI(s.buttons),l=SI(i),c=TI({buttons:i},n,o),d=kI((()=>h),yI((()=>g),o.shared.providers,t),o.shared.getSink),u=(e=>{switch(e){case"large":return["tox-dialog--width-lg"];case"medium":return["tox-dialog--width-md"];default:return[]}})(s.size),m={id:n,header:r,body:a,footer:M.some(c),extraClasses:u,extraBehaviours:[],extraStyles:{}},g=xI(m,e,d,o),p={getId:y(n),getRoot:y(g),getBody:()=>aD.getBody(g),getFooter:()=>aD.getFooter(g),getFormWrapper:()=>{const e=aD.getBody(g);return cm.getCurrent(e).getOr(e)}},h=MI(p,t.redial,l);return{dialog:g,instanceApi:h}})({dataValidator:s,initialData:r,internalDialog:e},{redial:IF.redial,closeWindow:()=>{aD.hide(a.dialog),o(a.instanceApi)}},t);return aD.show(a.dialog),a.instanceApi.setData(r),a.instanceApi}),e),i=(e,s,r,a=!1)=>IF.open(((e,i,l)=>{const c=Ln(Nn("data",l,i)),d=Wl(),u=t.shared.header.isPositionedAtTop(),m=()=>d.on((e=>{Th.reposition(e),uT.refresh(e)})),g=((e,t,o,n)=>{const s=Qr("dialog"),r=Qr("dialog-label"),a=Qr("dialog-content"),i=e.internalDialog,l=Bh(((e,t,o,n)=>fw.sketch({dom:aE('<div class="tox-dialog__header"></div>'),components:[hI(e,t,M.some(o),n),fI(),pI(n)],containerBehaviours:gl([GA.config({mode:"mouse",blockerClass:"blocker",getTarget:e=>ri(e,'[role="dialog"]').getOrDie(),snaps:{getSnapPoints:()=>[],leftAttr:"data-drag-left",topAttr:"data-drag-top"}})])}))({title:i.title,draggable:!0},s,r,o.shared.providers)),c=Bh(((e,t,o,n,s)=>rI(e,t,M.some(o),n,s))({body:i.body,initialData:i.initialData},s,a,o,n)),d=wI(i.buttons),u=SI(d),m=Bh(((e,t,o)=>_I(e,t,o))({buttons:d},s,o)),g=kI((()=>h),{onBlock:e=>{rE.block(p,((t,n)=>vI(e.message,n,o.shared.providers)))},onUnblock:()=>{rE.unblock(p)},onClose:()=>t.closeWindow()},o.shared.getSink),p=Ja({dom:{tag:"div",classes:["tox-dialog","tox-dialog-inline"],attributes:{role:"dialog","aria-labelledby":r,"aria-describedby":a}},eventOrder:{[or()]:[DM.name(),yl.name()],[nr()]:["execute-on-form"],[pr()]:["reflecting","execute-on-form"]},behaviours:gl([Tp.config({mode:"cyclic",onEscape:e=>(kr(e,Bw),M.some(!0)),useTabstopAt:e=>!ck(e)&&("button"!==Pe(e)||"disabled"!==xt(e,"disabled"))}),DM.config({channel:`${eI}-${s}`,updateState:(e,t)=>M.some(t),initialData:e}),Wp.config({}),zp("execute-on-form",g.concat([Nr(Ns(),((e,t)=>{Tp.focusIn(e)}))])),rE.config({getRoot:()=>M.some(p)}),Rp.config({}),QC({})]),components:[l.asSpec(),c.asSpec(),m.asSpec()]}),h=MI({getId:y(s),getRoot:y(p),getFooter:()=>m.get(p),getBody:()=>c.get(p),getFormWrapper:()=>{const e=c.get(p);return cm.getCurrent(e).getOr(e)}},t.redial,u);return{dialog:p,instanceApi:h}})({dataValidator:l,initialData:c,internalDialog:e},{redial:IF.redial,closeWindow:()=>{d.on(Th.hide),o.off("ResizeEditor",m),d.clear(),r(g.instanceApi)}},t,a),p=Ja(Th.sketch({lazySink:t.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:{},...u?{}:{fireRepositionEventInstead:{}},inlineBehaviours:gl([zp("window-manager-inline-events",[Fr(fr(),((e,t)=>{kr(g.dialog,Aw)}))]),...II(o,n,u)]),isExtraPart:(e,t)=>(e=>aw(e,".tox-alert-dialog")||aw(e,".tox-confirm-dialog"))(t)}));return d.set(p),Th.showWithin(p,Ya(g.dialog),{anchor:s},M.some(ht())),n&&u||(uT.refresh(p),o.on("ResizeEditor",m)),g.instanceApi.setData(c),Tp.focusIn(g.dialog),g.instanceApi}),e);return{open:(e,o,n)=>void 0!==o&&"toolbar"===o.inline?i(e,t.shared.anchors.inlineDialog(),n,o.ariaAttrs):void 0!==o&&"cursor"===o.inline?i(e,t.shared.anchors.cursor(),n,o.ariaAttrs):a(e,n),openUrl:(e,n)=>((e,n)=>IF.openUrl((e=>{const s=FI(e,{closeWindow:()=>{aD.hide(s.dialog),n(s.instanceApi)}},o,t);return aD.show(s.dialog),s.instanceApi}),e))(e,n),alert:(e,t)=>{s.open(e,t)},close:e=>{e.close()},confirm:(e,t)=>{r.open(e,t)}}};E.add("silver",(e=>{(e=>{Yh(e),(e=>{const t=e.options.register;var o;t("color_map",{processor:e=>f(e,r)?{value:ox(e),valid:!0}:{valid:!1,message:"Must be an array of strings."},default:["#BFEDD2","Light Green","#FBEEB8","Light Yellow","#F8CAC6","Light Red","#ECCAFA","Light Purple","#C2E0F4","Light Blue","#2DC26B","Green","#F1C40F","Yellow","#E03E2D","Red","#B96AD9","Purple","#3598DB","Blue","#169179","Dark Turquoise","#E67E23","Orange","#BA372A","Dark Red","#843FA1","Dark Purple","#236FA1","Dark Blue","#ECF0F1","Light Gray","#CED4D9","Medium Gray","#95A5A6","Gray","#7E8C8D","Dark Gray","#34495E","Navy Blue","#000000","Black","#ffffff","White"]}),t("color_cols",{processor:"number",default:(o=ax(e).length,Math.max(5,Math.ceil(Math.sqrt(o))))}),t("custom_colors",{processor:"boolean",default:!0})})(e),(e=>{const t=e.options.register;t("contextmenu_avoid_overlap",{processor:"string",default:""}),t("contextmenu_never_use_native",{processor:"boolean",default:!1}),t("contextmenu",{processor:e=>!1===e?{value:[],valid:!0}:r(e)||f(e,r)?{value:NB(e),valid:!0}:{valid:!1,message:"Must be false or a string."},default:"link linkchecker image editimage table spellchecker configurepermanentpen"})})(e)})(e);const{getUiMothership:t,backstage:o,renderUI:n}=oD(e);rw(e,o.shared);const s=VI({editor:e,backstage:o});return{renderUI:n,getWindowManagerImpl:y(s),getNotificationManagerImpl:()=>((e,t,o)=>{const n=t.backstage.shared,s=()=>{const t=$o(Ie(e.getContentAreaContainer())),o=Xo(),n=Pi(o.x,t.x,t.right),s=Pi(o.y,t.y,t.bottom),r=Math.max(t.right,o.right),a=Math.max(t.bottom,o.bottom);return M.some(Go(n,s,r-n,a-s))};return{open:(t,r)=>{const a=()=>{r(),Th.hide(l)},i=Ja(Wh.sketch({text:t.text,level:V(["success","error","warning","warn","info"],t.type)?t.type:void 0,progress:!0===t.progressBar,icon:t.icon,closeButton:t.closeButton,onAction:a,iconProvider:n.providers.icons,translationProvider:n.providers.translate})),l=Ja(Th.sketch({dom:{tag:"div",classes:["tox-notifications-container"]},lazySink:n.getSink,fireDismissalEventInstead:{},...n.header.isPositionedAtTop()?{}:{fireRepositionEventInstead:{}}}));o.add(l),h(t.timeout)&&t.timeout>0&&Eh.setEditorTimeout(e,(()=>{a()}),t.timeout);const c={close:a,reposition:()=>{const t=Ya(i),o={maxHeightFunction:Zl()},r=e.notificationManager.getNotifications();if(r[0]===c){const e={...n.anchors.banner(),overrides:o};Th.showWithinBounds(l,t,{anchor:e},s)}else I(r,c).each((e=>{const n=r[e-1].getEl(),a={type:"node",root:ht(),node:M.some(Ie(n)),overrides:o,layouts:{onRtl:()=>[Qi],onLtr:()=>[Qi]}};Th.showWithinBounds(l,t,{anchor:a},s)}))},text:e=>{Wh.updateText(i,e)},settings:t,getEl:()=>i.element.dom,progressBar:{value:e=>{Wh.updateProgress(i,e)}}};return c},close:e=>{e.close()},getArgs:e=>e.settings}})(e,{backstage:o},t())}}))}();