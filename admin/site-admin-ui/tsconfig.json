{
  "compilerOptions": {
    "target": "esnext",
    "module": "esnext",
    "strict": true,
    "jsx": "preserve",
    "importHelpers": true,
    "moduleResolution": "node",
    "experimentalDecorators": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "isolatedModules": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "sourceMap": true,
    "baseUrl": ".",
    "allowJs": false,
    "resolveJsonModule": true, // 包含导入的模块。json的扩展
    "lib": [
      "dom",
      "esnext"
    ],
    "incremental": true,
    "paths": {
      "@/*": [
        "src/*"
      ],
      "~/*": [
        "./*"
      ]
    },
    "types": [
      "node",
      "vite/client"
    ],
    "typeRoots": [
      "./node_modules/@types/",
      "./types"
    ]
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "types/*.d.ts",
    "vite.config.ts", "src/utils/validate.js"  ],
  "exclude": [
    "node_modules",
    "dist",
    "**/*.js"
  ]
}