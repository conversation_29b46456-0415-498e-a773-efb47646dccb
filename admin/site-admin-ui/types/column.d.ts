/** 栏目 */
interface IColumn extends IBaseField {
    columnCode: string
    columnImage: string
    columnIsshow: string | null
    columnLevel: string
    columnList: Array<IColumn> | null
    columnName: string
    columnNameEn: string
    columnPcUrl: string
    columnPid: string
    columnType: string
    planceNo: number
    readOnly: false
    retrieveType: string | null
    showPosition: string
    /** 标签列表 */
    tagList: Array<ITag> | null
    contentList: Array<IRelease> | null
    /** 是否在常用功能区域展示 (0: 否/1: 是) */
    stockFlag: 0|1
    describes: string
    content: string
    needAudit: string | null
    flowId:string
}

interface IColumnTree extends IColumn {
    children?: IColumn[]
}