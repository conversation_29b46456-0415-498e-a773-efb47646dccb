/**
 * 会务ui模块
 */
interface IConfModule{
  /** 主键 */
  id?: number;

  /** 组件类型 */
  moduleType: string;

  /** 宽度;half|wide */
  width: string;

  /** 高度;1x|2x */
  height: string;

  /** 排序 */
  sort?: number;

  /** 移动端路由 */
  path: string;

  /** 图标样式 */
  iconClass?: string;

  /** 标题 */
  title?: string;

  /** 子标题 */
  subtitle?: string;

  /** 文字颜色样式 */
  textColorClass?: string;

  /** 颜色样式 */
  bgColorClass?: string;

  /** 背景图路径 */
  bgImgUrl?: string;

  /** 创建人 */
  createBy?: string;

  /** 创建时间 */
  createTime?: string | Date;

  /** 更新人 */
  updateBy?: string;

  /** 更新时间 */
  updateTime?: string | Date;
}

/**
 * 会务首页设计预设
 */
interface IConfModulePreset{
  /** 主键 */
  id?: number;

  /** 预设名称 */
  presetName?: string;

  /** 预设首页模块json */
  presetJson?: string;

  /** 预设首页模块数据 */
  presetData: IConfModuleData[];

  /** banner图片路径 */
  bannerUrl?: string;

  /** banner颜色 */
  bannerColorClass?: string;

  /** banner标题 */
  bannerTitle?: string;

  /** banner标题颜色 */
  bannerTitleColorClass?: string;

  /** banner子标题 */
  bannerSubtitle?: string;

  /** banner子标题颜色 */
  bannerSubtitleColorClass?: string;

  /** 底图图片路径 */
  backgroundUrl?: string;

  /** 底图颜色 */
  backgroundColorClass?: string;

  /** 创建人 */
  createBy?: string;

  /** 创建时间 */
  createTime?: string | Date;

  /** 更新人 */
  updateBy?: string;

  /** 更新时间 */
  updateTime?: string | Date;
}

/**
 * 会务ui模块预览数据
 */
type IConfPreviewData = Omit<
  IConfModulePreset,
  "id" | "presetName" | "presetJson" | "createBy" | "createTime" | "updateBy" | "updateTime"
>

/**
 * 会务ui模块数据
 */
type IConfModuleData = Omit<IConfModule, "id" | "createBy" | "createTime" | "updateBy" | "updateTime">


