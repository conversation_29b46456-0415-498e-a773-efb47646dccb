/** 发布消息申请基础字段 */
interface IReleasePublish extends IBaseField {
    id?: string
    moduleFst: string,
    moduleSend: string,
    title: string,
    applyTime: string
    publishTime: string
    editors: string,
    checkers: string
    checkTime: string,
    status: number,
    describes: string,
    /** 1:保存, 2:保存并发布 */
    saveType: string,
    releaseId: string
    columnId: string,
    releaseTitle: string
    releaseSource: string,
    releaseDescribes: string,
    moduleFstName:string,
    moduleSendName:string,
    releaseIds: string[]
    applyTimeStart: string
    applyTimeEnd: string
    createByName: string
    auditInfo: string
    userImage: string
}