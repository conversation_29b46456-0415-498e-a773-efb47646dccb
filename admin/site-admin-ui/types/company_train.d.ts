interface ICompanyTrain {
    id: string
    /**
     * 企业id
     */
    companyId: number
    /**
       * 用户id
       */
    userId: number
    /**
     * 企业名称
     */
    companyName: string
    /**
     * 企业注册地
     */
    companyRegistrationPlace: string
    /**
     * 联系人
     */
    contact: string
    /**
     * 联系方式
     */
    contactWay: string
    /**
     * 邮箱
     */
    email: string
    /**
     * 诉求类型
     */
    demandType: string
    /**
     * 诉求时间
     */
    demandTime: string
    /**
     * 诉求标题
     */
    demandTitle: string
    /**
     * 诉求具体内容
     */
    demandContent: string
    /**
     * 审核状态
     */
    status: string
}

interface ICompanyTrainSearchVo {
    id: string
    /**
     * 企业名称
     */
    companyName: string
    /**
     * 诉求类型
     */
    demandType: string
    /**
     * 诉求开始时间
     */
    demandStartTime: string
    /**
     * 诉求结束时间
     */
    demandEndTime: string
    /**
     * 诉求标题
     */
    demandTitle: string
    /**
     * 审核状态
     */
    status: string[]
}

interface ICompanyTrainDto extends ICompanyTrain {
    fileList: IContentFile[]
    auditList: {
        status: string
        reason: string
        auditTime: string
    }[]
}