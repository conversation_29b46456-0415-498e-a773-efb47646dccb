interface IConfBasic {
  /** 主键 */
  id?: number;

  /** 会务代码;用于签到、报名地址 */
  code?: string;

  /** 会务名称 */
  name?: string;

  /** 开始时间 */
  startTime?: string | Date;

  /** 结束时间 */
  endTime?: string | Date;

  /** 时间范围 */
  timeRange?: string[] | Date[];

  /** 报名截止时间 */
  signupDeadline?: string | Date;

  /** 会务地点 */
  location?: string;

  /** 会务地点坐标 */
  pixel?: string;

  /** 首页模块JSON */
  presetJson?: string;

  /** 首页模块数据 */
  presetData: IConfModuleData[];

  /** banner图片路径 */
  bannerUrl?: string;

  /** banner颜色 */
  bannerColorClass?: string;

  /** banner标题 */
  bannerTitle?: string;

  /** banner标题颜色 */
  bannerTitleColorClass?: string;

  /** banner子标题 */
  bannerSubtitle?: string;

  /** banner子标题颜色 */
  bannerSubtitleColorClass?: string;

  /** 底图图片路径 */
  backgroundUrl?: string;

  /** 底图颜色 */
  backgroundColorClass?: string;

  /** 会务状态;0 草稿 1 发布 2 结束 */
  status?: string;

  /** 创建人 */
  createBy?: string;

  /** 创建时间 */
  createTime?: string | Date;

  /** 更新人 */
  updateBy?: string;

  /** 更新时间 */
  updateTime?: string | Date;
}