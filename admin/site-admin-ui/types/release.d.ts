/** 发布消息基础字段 */
interface IRelease extends IBaseField {
    id?: string
    title: string,
    columnId: string
    tagIdList: string[]
    columnIdList: string[]
    releaseDate: string
    releaseStatus: string
    releasePerson: string,
    releaseType: string
    isTop: number,
    sort: number,
    /** 1:保存, 2:保存并发布 */
    saveType: string,
    titleSub: string,
    describes: string,
    articleDate: string
    useFlag: string
    contentColumn?: IColumn[]
    tagList?: ITag[],
    tag: string
    releaseDateStart?: string,
    releaseDateEnd?: string
    image: string
    content?: string
    source?: string
    mapperId?: string
    /** 流程id */
    processId?: string
    processInfo?: {
        identityLinkList?: {groupId: string}
    }
    groupId?: string
    
}