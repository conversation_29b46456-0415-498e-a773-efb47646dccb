interface IbaseResponse {
    code: number,
    status?: number,
    message: string
}
interface IResponse<T> extends IbaseResponse {
    data: T
}

interface IResponsePage<T> extends IbaseResponse {
    // data: {
        pageNum: number,
        pageSize: number,
        total: number,
        rows: T[]
    // },
}

interface IPage {
    pageNum: number,
    pageSize: number,
}

interface IBaseFiled extends IPage {
    id?: string,
    createTime?: string,
    createBy?: string,
    updateTime?: string,
    updateBy?: string
    delFlag: 0|1
}

interface IPolicy extends IBaseFiled {
    title?: String
    startDate: Date
    endDate: Date
}

interface IPolicySearch extends IPolicy {
    startDateBegin?: string
    startDateEnd?: string
    endDateBegin?: string
    endDateEnd?: string
}

interface IPolicyDetail extends IBaseFiled {
    policyId: string
    deptName: string
    publishDate: Date
    fileName: string
    fileNumber: string
    url: string
    sort: number
    type: number
}