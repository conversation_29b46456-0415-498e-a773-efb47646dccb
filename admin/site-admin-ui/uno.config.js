// uno.config.ts
import presetIcons from '@unocss/preset-icons'
import presetWind3 from '@unocss/preset-wind3'
import { defineConfig } from 'unocss'

// 颜色
const colors = [
  'slate', 'gray', 'zinc', 'neutral', 'stone',
  'red', 'orange', 'amber', 'yellow', 'lime', 'green', 'emerald', 'teal', 'cyan',
  'sky', 'blue', 'indigo', 'violet', 'purple', 'fuchsia', 'pink', 'rose'
]

// 深度（可加 50/950）
const shades = [
  50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950
]

// 生成 safelist
const safelist = []

// text-*
colors.forEach(c => {
  shades.forEach(s => {
    safelist.push(`text-${c}-${s}`)
  })
})
safelist.push('text-black')
safelist.push('text-white')

// bg-*
colors.forEach(c => {
  shades.forEach(s => {
    safelist.push(`bg-${c}-${s}`)
  })
})
safelist.push('bg-white')
safelist.push('bg-black')

// from-*
colors.forEach(c => {
  shades.forEach(s => {
    safelist.push(`from-${c}-${s}`)
  })
})
safelist.push('from-black')
safelist.push('from-white')

// to-*
colors.forEach(c => {
  shades.forEach(s => {
    safelist.push(`to-${c}-${s}`)
  })
})
safelist.push('to-black')
safelist.push('to-white')

// 渐变方向
;['t','tr','r','br','b','bl','l','tl'].forEach(d => {
  safelist.push(`bg-gradient-to-${d}`)
})

export default defineConfig({
  // presets: [
  //   presetWind3(),
  //   presetIcons({
  //     scale: 1,
  //   }),
  // ],
  // safelist,
})